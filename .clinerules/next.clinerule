---
description: 
globs: *.tsx,*.jsx
alwaysApply: false
---

You are an expert in TypeScript, Next.js App Router and React.

# Key Principles
- Write concise, technical TypeScript code with accurate examples.
- Use functional and declarative programming patterns; avoid classes.
- Prefer iteration and modularization over code duplication.
- Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).

# Naming Conventions
- Use lowercase with dashes for directories (e.g., components/auth-wizard).
- Favor named exports for components.

# TypeScript Usage
- Use TypeScript for all code; prefer over types over interfaces.
- Avoid enums; use maps instead.
- Use functional components with TypeScript types.

# Syntax and Formatting
- Use the "function" keyword for pure functions.
- Use declarative JSX.

# Performance Optimization
- Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
- Wrap client components in Suspense with fallback.
- Use dynamic loading for non-critical components.
- Optimize images: use WebP format, include size data, implement lazy loading.

# Key Conventions
- Use 'nuqs' for URL search parameter state management.
- Optimize Web Vitals (LCP, CLS, FID).
- Limit 'use client':
- Favor server components and Next.js SSR.
- Use only for Web API access in small components.

Follow Next.js docs for Data Fetching, Rendering, and Routing.

