## 📝 Description

Please include a summary of the changes. Provide context and motivation for the change, and describe what problem it solves.

- **Type:** Bug fix / New feature / Documentation / Refactor

## 🛠️ Key Changes

- [Change 1 - Brief description]
- [Change 2 - Brief description]
- ...

## 📌 To-Do Before Merging

- [ ] [Task 1 - Brief description]
- [ ] [Task 2 - Brief description]
- ...

## 🧪 How to Test

- **Setup:** [Setup details]
- **Steps to Test:**
  1. [Step 1 - Brief description]
  2. [Step 2 - Brief description]
  3. ...
- **Expected Results:** [Outcome]
- **Additional Notes:** [Additional info]

## 📸 Screenshots

Attach if there are UI changes.

## 🔖 Resources

Feel free to share any references to documentation, libraries, blog posts, or other resources that you consulted or used during the implementation of the changes.

- [Resource 1]
- [Resource 2]
- ...
