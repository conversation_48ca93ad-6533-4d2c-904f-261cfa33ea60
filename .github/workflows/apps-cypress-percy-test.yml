name: Site Cypress and Percy Tests

on:
  push:
    paths:
      - "apps/**"
    branches: [main]
  pull_request:
    paths:
      - "apps/**"
    branches: [main]

jobs:
  cypress-run:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [ff-site, ffdweb-site]
    defaults:
      run:
        working-directory: apps/${{ matrix.app }}

    env:
      AIRTABLE_READ_ONLY_TOKEN: ${{ secrets.AIRTABLE_READ_ONLY_TOKEN }}
      PERCY_TOKEN: ${{ matrix.app == 'ff-site' && secrets.PERCY_TOKEN_FF_SITE || secrets.PERCY_TOKEN_FFDWEB_SITE || '' }}
      ENCRYPTION_SECRET_KEY: "a-random-key-so-that-ci-builds-dont-fail"

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"

      - name: Install Dependencies
        run: npm install --prefer-offline || npm install --prefer-offline

      - name: Build Project
        working-directory: .
        run: npx turbo run build --filter="./apps/${{ matrix.app }}"

      - name: Start Server
        run: |
          npm start &
          echo "Waiting for server to start..."
          npx wait-on http://localhost:3000

      - name: Determine Test Type
        id: test-type
        run: |
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            BRANCH_NAME="${{ github.head_ref }}"
            COMMIT_MESSAGE="${{ github.event.pull_request.title }}"
          else
            BRANCH_NAME="${{ github.ref_name }}"
            COMMIT_MESSAGE="${{ github.event.head_commit.message }}"
          fi

          if [[ "$BRANCH_NAME" == cms/* ]] || [[ "$COMMIT_MESSAGE" == *"[skip percy]"* ]]; then
            echo "skip_percy=true" >> $GITHUB_OUTPUT
          else
            echo "skip_percy=false" >> $GITHUB_OUTPUT
          fi

      - name: Run Cypress Tests without Percy
        if: steps.test-type.outputs.skip_percy == 'true'
        run: npm run test:cypress

      - name: Run Cypress Tests with Percy
        if: steps.test-type.outputs.skip_percy == 'false'
        run: npm run test:cypress:percy
