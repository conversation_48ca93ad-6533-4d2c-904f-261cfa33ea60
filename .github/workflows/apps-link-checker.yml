name: Site Link Checker

on:
  schedule:
    - cron: "0 2 * * 0"
  workflow_dispatch:

jobs:
  linkChecker:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [ff-site, ffdweb-site]
        include:
          - app: ff-site
            base_url: https://fil.org
          - app: ffdweb-site
            base_url: https://ffdweb.org

    env:
      BASE_URL: ${{ matrix.base_url }}

    defaults:
      run:
        working-directory: apps/${{ matrix.app }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Lychee Link Checker
        uses: lycheeverse/lychee-action@v2.4.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        id: lychee
        with:
          args: |
            --accept 100..=103,200..=299,403,429
            --base ${{ env.BASE_URL }}
            --exclude '(twitter|x)\.com'
            --retry-wait-time 3
            'apps/${{ matrix.app }}/src/**/*.md'
