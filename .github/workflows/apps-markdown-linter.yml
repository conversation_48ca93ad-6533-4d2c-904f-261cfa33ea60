name: A<PERSON> Markdown Linter

on:
  pull_request:
    paths:
      - apps/**/*.md

jobs:
  lint:
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Run markdownlint
        uses: david<PERSON>on/markdownlint-cli2-action@v20
        with:
          globs: |
            **/*.md
            !**/node_modules/**/*.md
          config: ".markdownlint.jsonc"
