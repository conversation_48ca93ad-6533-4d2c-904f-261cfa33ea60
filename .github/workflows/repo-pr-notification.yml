name: Apps PR Review Notification

on:
  pull_request:
    types: [opened, ready_for_review]

jobs:
  notify:
    if: ${{ !github.event.pull_request.draft }}
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read

    steps:
      - uses: actions/checkout@v4

      - name: Prepare Reviewers List and App Info
        id: format_info
        run: |
          REVIEWERS=$(echo '${{ toJSON(github.event.pull_request.requested_reviewers) }}' | jq -r 'map(.login) | join(", ")')
          APP_PATH=$(echo "${{ github.event.pull_request.changed_files }}" | grep -o 'apps/[^/]*' | head -1)
          if [ -z "$REVIEWERS" ]; then
            REVIEWERS="No reviewers assigned"
          fi
          echo "REVIEWERS=$REVIEWERS" >> $GITHUB_ENV
          echo "APP_PATH=$APP_PATH" >> $GITHUB_ENV

      - name: Post text to a Slack channel
        uses: slackapi/slack-github-action@v2.1.1
        with:
          method: chat.postMessage
          token: ${{ secrets.SLACK_BOT_TOKEN }}
          payload: |
            channel: ${{ secrets.SLACK_CHANNEL_ID }}
            text: "${{ github.actor }} has marked a pull request as ready for review in ${{ env.APP_PATH }}: <${{ github.event.pull_request.html_url }}|${{ github.event.pull_request.title }}>\nReviewers: ${{ env.REVIEWERS }}"
