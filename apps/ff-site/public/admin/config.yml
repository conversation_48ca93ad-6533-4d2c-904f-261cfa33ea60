local_backend: true
site_url: https://fil.org
display_url: https://fil.org

backend:
  name: github
  branch: main
  repo: FilecoinFoundationWeb/filecoin-foundation
  base_url: https://decap-proxy.filecoin-foundation.workers.dev/

publish_mode: editorial_workflow
media_folder: "apps/ff-site/public/assets/images"
public_folder: "/assets/images"

date_iso_format: &date_iso_format "YYYY-MM-DD"
time_iso_format: &time_iso_format "HH:mm:ss.SSSZ"

slug:
  encoding: "ascii"
  clean_accents: true

created_on: &created_on
  name: "created-on"
  label: "Created On"
  widget: "datetime"
  default: "{{now}}"
  picker_utc: true

updated_on: &updated_on
  name: "updated-on"
  label: "Updated On"
  widget: "datetime"
  default: "{{now}}"
  picker_utc: true

published_on: &published_on
  name: "published-on"
  label: "Published On"
  widget: "datetime"
  default: "{{now}}"
  picker_utc: true

header_config: &header_config
  name: "header"
  label: "Header"
  widget: "object"
  fields:
    - name: "title"
      label: "Title"
      widget: "string"
    - name: "description"
      label: "Description"
      widget: "text"

body_config: &body_config
  name: "body"
  label: "Content"
  widget: "markdown"
  editor_components:
    - "code-block"
    - "image"
    - "video"
    - "audio"

image_alt_config: &image_alt_config
  name: "alt"
  label: "Alt Text"
  widget: "hidden"
  required: false
  hint: "Refer to the W3C guidelines on when to use alt text: https://www.w3.org/WAI/tutorials/images/decision-tree/. Preferably, upload the image to ChatGPT 4.0 and use the prompt: 'Check the W3C guidelines (https://www.w3.org/WAI/tutorials/images/decision-tree/) and generate appropriate alt text for this image.'"

image_config: &image_config
  name: "image"
  label: "Image"
  widget: "object"
  required: false
  collapsed: true
  hint: Adding an image is optional. If no image is added, a default fallback image will be used automatically. Simply leave the image field empty to use the fallback.
  fields:
    - name: "src"
      label: "URL"
      widget: "image"
      allow_multiple: false
      choose_url: false
      required: false
      hint: "Use https://squoosh.app/ to compress images before uploading. Choose the WebP format and resize the image to 2000px width."
    - *image_alt_config

event_sponsor_config: &event_sponsor_config
  - name: "name"
    label: "Name"
    widget: "string"
  - name: "website"
    label: "Website"
    widget: "string"
    required: false
  - name: "image"
    label: "Image"
    widget: "object"
    fields:
      - name: "src"
        label: "URL"
        widget: "image"
        allow_multiple: false
        choose_url: false
        hint: "Use https://squoosh.app/ to compress images before uploading. Choose the WebP format and resize the image to 320px height. Make sure no whitespace is present around the logo."
      - *image_alt_config

seo_metadata_description_max_characters: &seo_metadata_description_max_characters 220

meta_config_with_title_fallback: &meta_config_with_title_fallback
  name: "seo"
  label: "SEO Metadata"
  widget: "object"
  fields:
    - name: "title"
      label: "Meta Title"
      widget: "string"
      required: false
      hint: "If left empty, the main entry title will automatically become the SEO title. '- Filecoin Event' will be added for events, and '- Ecosystem Project' for ecosystem projects. For example: 'Acasia - Filecoin Ecosystem Project' or 'FIL Bangkok 2024 - Filecoin Event'."
    - name: "description"
      label: "Meta Description"
      widget: "text"
      pattern:
        - "^.{0,220}$"
        - "Must have at most 220 characters."
      hint: "To generate meta title and description, copy and paste the entry content into ChatGPT 4.0, then use the prompt: 'Generate a meta title and a meta description under 220 characters for the following content: [paste your content here].'"

meta_config: &meta_config
  name: "seo"
  label: "SEO Metadata"
  widget: "object"
  fields:
    - name: "title"
      label: "Meta Title"
      widget: "string"
    - name: "description"
      label: "Meta Description"
      widget: "text"
      pattern:
        - "^.{0,220}$"
        - "Must have at most 220 characters."
      hint: "To generate meta title and description, copy and paste the entry content into ChatGPT 4.0, then use the prompt: 'Generate a meta title and a meta description under 220 characters for the following content: [paste your content here].'"

collections:
  - name: "pages"
    label: "Pages"
    files:
      - name: "about"
        label: "About"
        file: "apps/ff-site/src/content/pages/about.md"
        preview_path: "about"
        fields:
          - *header_config
          - *meta_config

      - name: "allocators"
        label: "Filecoin Plus - Allocators"
        file: "apps/ff-site/src/content/pages/filecoin-plus/allocators.md"
        preview_path: "filecoin-plus/allocators"
        fields:
          - *header_config
          - *meta_config

      - name: "blog"
        label: "Blog"
        file: "apps/ff-site/src/content/pages/blog.md"
        preview_path: "blog"
        fields:
          - *header_config
          - name: "featured_entry"
            label: "Featured Post"
            widget: "relation"
            collection: "blog_posts"
            value_field: "apps/ff-site/src/content/blog/{{slug}}.md"
            search_fields:
              - "slug"
              - "title"
            display_fields:
              - "title"
          - *meta_config

      - name: "digest"
        label: "Digest"
        file: "apps/ff-site/src/content/pages/digest.md"
        preview_path: "digest"
        fields:
          - *header_config
          - *meta_config

      - name: "ecosystem-explorer"
        label: "Ecosystem Explorer"
        file: "apps/ff-site/src/content/pages/ecosystem-explorer/ecosystem-explorer.md"
        preview_path: "ecosystem-explorer"
        fields:
          - *header_config
          - name: "featured_entry"
            label: "Featured Project"
            widget: "relation"
            collection: "ecosystem_projects"
            value_field: "apps/ff-site/src/content/ecosystem-explorer/{{slug}}.md"
            search_fields:
              - "slug"
              - "title"
            display_fields:
              - "title"
          - *meta_config

      - name: "ecosystem-explorer/project-form"
        label: "Ecosystem Explorer - Project Form"
        file: "apps/ff-site/src/content/pages/ecosystem-explorer/project-form.md"
        preview_path: "ecosystem-explorer/project-form"
        fields:
          - *header_config
          - *meta_config

      - name: "employee-privacy-policy"
        label: "Employee Privacy Policy"
        file: "apps/ff-site/src/content/pages/employee-privacy-policy.md"
        preview_path: "employee-privacy-policy"
        fields:
          - *header_config
          - *meta_config

      - name: "events"
        label: "Events"
        file: "apps/ff-site/src/content/pages/events.md"
        preview_path: "events"
        fields:
          - *header_config
          - name: "featured_entry"
            label: "Featured Event"
            widget: "relation"
            collection: "event_entries"
            value_field: "apps/ff-site/src/content/events/{{slug}}.md"
            search_fields:
              - "slug"
              - "title"
            display_fields:
              - "title"
          - *meta_config

      - name: "filecoin-plus"
        label: "Filecoin Plus"
        file: "apps/ff-site/src/content/pages/filecoin-plus/filecoin-plus.md"
        preview_path: "filecoin-plus"
        fields:
          - *header_config
          - *meta_config

      - name: "governance"
        label: "Governance"
        file: "apps/ff-site/src/content/pages/governance/governance.md"
        preview_path: "governance"
        fields:
          - *header_config
          - *meta_config

      - name: "govhub"
        label: "Governance - GovHub"
        file: "apps/ff-site/src/content/pages/governance/govhub.md"
        preview_path: "governance/govhub"
        fields:
          - *header_config
          - *meta_config

      - name: "grants"
        label: "Grants"
        file: "apps/ff-site/src/content/pages/grants.md"
        preview_path: "grants"
        fields:
          - *header_config
          - name: "featured_grant_graduates"
            label: "Featured Grant Graduates"
            widget: "relation"
            collection: "ecosystem_projects"
            value_field: "apps/ff-site/src/content/ecosystem-explorer/{{slug}}.md"
            search_fields:
              - "slug"
              - "title"
            display_fields:
              - "title"
            multiple: true
            min: 6
            max: 6
          - *meta_config

      - name: "home"
        label: "Home"
        file: "apps/ff-site/src/content/pages/home.md"
        fields:
          - *header_config
          - name: "featured_ecosystem_projects"
            label: "Featured Ecosystem Projects"
            widget: "relation"
            collection: "ecosystem_projects"
            value_field: "apps/ff-site/src/content/ecosystem-explorer/{{slug}}.md"
            search_fields:
              - "slug"
              - "title"
            display_fields:
              - "title"
            multiple: true
            min: 6
            max: 6
          - *meta_config

      - name: "orbit"
        label: "Orbit"
        file: "apps/ff-site/src/content/pages/orbit.md"
        preview_path: "orbit"
        fields:
          - *header_config
          - *meta_config

      - name: "privacy-policy"
        label: "Privacy Policy"
        file: "apps/ff-site/src/content/pages/privacy-policy.md"
        preview_path: "privacy-policy"
        fields:
          - *header_config
          - *meta_config

      - name: "security"
        label: "Security"
        file: "apps/ff-site/src/content/pages/security/security.md"
        preview_path: "security"
        fields:
          - *header_config
          - *meta_config

      - name: "security-maturity-model"
        label: "Security - Maturity Model"
        file: "apps/ff-site/src/content/pages/security/maturity-model.md"
        preview_path: "security/maturity-model"
        fields:
          - *header_config
          - *meta_config

      - name: "security-bug-bounty"
        label: "Security - Bug Bounty"
        file: "apps/ff-site/src/content/pages/security/bug-bounty.md"
        preview_path: "security/bug-bounty"
        fields:
          - *header_config
          - *meta_config

      - name: "security-bug-bounty-leaderboard"
        label: "Security - Bug Bounty - Leaderboard"
        file: "src/content/pages/security/bug-bounty/leaderboard.md"
        preview_path: "security/bug-bounty/leaderboard"
        fields:
          - *header_config
          - *meta_config

      - name: "terms"
        label: "Terms of Use"
        file: "apps/ff-site/src/content/pages/terms-of-use.md"
        preview_path: "terms-of-use"
        fields:
          - *header_config
          - *meta_config

  - name: "blog_posts"
    label: "Blog Posts"
    label_singular: "Blog Post"
    folder: "apps/ff-site/src/content/blog/"
    preview_path: "blog/{{slug}}"
    create: true
    extension: "md"
    slug: "{{slug}}"
    fields:
      - name: "title"
        label: "Title"
        widget: "string"
      - *created_on
      - *updated_on
      - *published_on
      - name: "category"
        label: "Category"
        widget: "select"
        options:
          - label: "Ecosystem"
            value: "ecosystem"
          - label: "Events"
            value: "events"
          - label: "Interviews"
            value: "interviews"
          - label: "News"
            value: "news"
          - label: "Reports"
            value: "reports"
          - label: "Use Cases"
            value: "use-cases"
      - name: "description"
        label: "Description"
        widget: "text"
      - name: "chinese-translation-url"
        label: "Chinese Translation URL"
        widget: "string"
        required: false
        hint: "URL of the Chinese translation of this post."
      - *image_config
      - name: "add-table-of-contents"
        label: "Add a table of contents to this post?"
        widget: "boolean"
        required: false
        hint: "The table of contents will include headings from h1 (#) to h3 (###). It will appear above the post content and below the header image, if present."
      - *body_config
      - *meta_config_with_title_fallback

  - name: "digest_articles"
    label: "Digest Articles"
    folder: "apps/ff-site/src/content/digest/"
    preview_path: "digest/{{slug}}"
    label_singular: "Digest Article"
    create: true
    extension: "md"
    slug: "{{slug}}"
    fields:
      - name: "title"
        label: "Title"
        widget: "string"
      - *created_on
      - *updated_on
      - *published_on
      - name: "issue-number"
        label: "Issue Number"
        widget: "select"
        options: ["1"]
        default: "1"
      - name: "article-number"
        label: "Article Number"
        widget: "number"
        min: 1
      - name: "authors"
        label: "Authors"
        widget: "list"
        fields:
          - name: "first-name"
            label: "First Name"
            widget: "string"
          - name: "last-name"
            label: "Last Name"
            widget: "string"
          - name: "image"
            label: "Image"
            widget: "object"
            required: false
            hint: "Adding an image is optional. If no image is added, the author's initials will be used automatically. Simply leave the image field empty to use the fallback."
            fields:
              - name: "src"
                label: "URL"
                widget: "image"
                allow_multiple: false
                choose_url: false
                required: false
                hint: "Use https://squoosh.app/ to compress images before uploading. Choose the WebP format and resize the image to 100px width."
              - name: "alt"
                label: "Alt Text"
                widget: "hidden"
                required: false
          - name: "company"
            label: "Company Name"
            widget: "string"
      - *body_config
      - *image_config
      - *meta_config_with_title_fallback

  - name: ecosystem_projects
    label: Ecosystem Projects
    label_singular: Ecosystem Project
    folder: "apps/ff-site/src/content/ecosystem-explorer/"
    preview_path: "ecosystem-explorer/{{slug}}"
    create: true
    extension: md
    slug: "{{slug}}"
    fields:
      - name: title
        label: Company Name
        widget: string
      - *created_on
      - *updated_on
      - *published_on
      - name: email
        label: Submitter's Email
        widget: decrypted_widget
      - name: full-name
        label: Submitter's Full Name
        widget: decrypted_widget
      - *image_config
      - name: category
        label: Category
        widget: select
        options:
          - label: "Artificial Intelligence - AI Productivity & Utilities"
            value: "ai-productivity-utilities"
          - label: "Education, Academia & Health - Education"
            value: "education"
          - label: "Education, Academia & Health - Academia"
            value: "academia"
          - label: "Education, Academia & Health - Health"
            value: "health"
          - label: "Finance - Leasing"
            value: "leasing"
          - label: "Media & Entertainment - Arts, Collectibles & NFTs"
            value: "arts-collectibles-nfts"
          - label: "Media & Entertainment - Photo & Video"
            value: "photo-video"
          - label: "Network - Network Tools"
            value: "network-tools"
          - label: "Public Goods & DWeb - DWeb"
            value: "dweb"
          - label: "Public Goods & DWeb - Governance, DAOs & Public Goods"
            value: "governance-daos-public-goods"
          - label: "Public Goods & DWeb - Social Impact"
            value: "social-impact"
          - label: "Climate & Decentralized Science - Climate"
            value: "climate"
          - label: "Climate & Decentralized Science - Decentralized Science"
            value: "decentralized-science"
          - label: "Storage - Data Storage & Management"
            value: "data-storage-management"
          - label: "Storage - Infrastructure"
            value: "infrastructure"
          - label: "Storage - Storage Provider"
            value: "storage-provider"
          - label: "Storage - Storage Services"
            value: "storage-services"
          - label: "Storage - Storage Software"
            value: "storage-software"
          - label: "Tooling & Productivity - Bridges & Oracles"
            value: "bridges-oracles"
          - label: "Tooling & Productivity - Dapp"
            value: "dapp"
          - label: "Tooling & Productivity - Data Retrieval"
            value: "data-retrieval"
          - label: "Tooling & Productivity - Developer Tools"
            value: "developer-tools"
          - label: "Tooling & Productivity - Wallets, Identity & Authentication"
            value: "wallets-identity-authentication"
      - name: description
        label: Short Description
        widget: string
        pattern:
          - "^.{0,160}$"
          - "Must have at most 160 characters."
        hint: "A short description of the project. Maximum 160 characters."
      - name: website
        label: Website
        widget: string
      - name: tech
        label: Technologies Used
        widget: select
        multiple: true
        options:
          - filecoin
          - ipfs
      - name: repo
        label: GitHub Repo
        widget: string
        required: false
      - name: featured-content
        label: Featured Content
        widget: string
        required: false
      - name: twitter
        label: Twitter Handle
        widget: string
        required: false
      - name: video-url
        label: YouTube Video URL
        widget: string
        required: false
        hint: "Please enter the YouTube embed URL in the format: 'https://www.youtube.com/embed/{videoId}', without additional query parameters."
      - name: year-joined
        label: Using Filecoin Since
        widget: datetime
        required: false
        date_format: YYYY
        hint: "Enter only the year. The date will default to January 1st."
      - name: body
        label: Long Description
        widget: markdown
        required: false
      - *meta_config_with_title_fallback

  - name: "event_entries"
    label: "Events"
    folder: "apps/ff-site/src/content/events/"
    preview_path: "events/{{slug}}"
    label_singular: "Event"
    create: true
    extension: "md"
    slug: "{{slug}}"
    fields:
      - name: "title"
        label: "Title"
        widget: "string"
      - name: "description"
        label: "Description"
        widget: "string"
        required: false
      - *created_on
      - *updated_on
      - *published_on
      - name: "category"
        label: "Category"
        widget: "select"
        options:
          - label: "Hosted"
            value: "hosted"
          - label: "Supported & Sponsored"
            value: "supported-sponsored"
      - name: "location"
        label: "Location"
        widget: "object"
        fields:
          - name: "primary"
            label: "City, State/Country"
            widget: "string"
            default: "Virtual"
            hint: "For in-person events format as 'City, State' for US locations (e.g., 'Austin, TX') or 'City, Country' for international locations (e.g., 'Davos, Switzerland'). Defaults to 'Virtual'."
          - name: "region"
            label: "Region"
            widget: "select"
            required: false
            options:
              - label: "Africa"
                value: "africa"
              - label: "APAC/Asia"
                value: "apac-asia"
              - label: "Australia/Oceania"
                value: "australia-oceania"
              - label: "Europe"
                value: "europe"
              - label: "Middle East"
                value: "middle-east"
              - label: "North America"
                value: "north-america"
              - label: "South America"
                value: "south-america"
            hint: "Select the region where the in-person event takes place. Leave empty for virtual events."
      - name: "start-date"
        label: "Start Date"
        widget: "datetime"
        date_format: *date_iso_format
        hint: "First day of event"
      - name: "end-date"
        label: "End Date"
        widget: "datetime"
        date_format: *date_iso_format
        required: false
        hint: "Last day of event. Leave empty for single-day events."
      - name: "external-link"
        label: "External Link URL"
        widget: "string"
        required: false
      - *image_config
      - name: "luma-calendar-link"
        label: "Luma Calendar URL"
        widget: "string"
        required: false
        hint: "Luma Calendar URL for the event. Include relevant tags when appropriate, e.g., https://lu.ma/filecoin-events?tag=fil%20bangkok%202024"
      - name: program
        label: "Program"
        widget: "object"
        required: false
        collapsed: true
        fields:
          - name: "kicker"
            label: "Section Kicker"
            widget: "string"
            required: false
            hint: "This is the text that appears above the section title. Defaults to 'Explore'."
          - name: "title"
            label: "Section Title"
            widget: "string"
            required: false
            hint: This is the title for the Events section header. For example, "Main Stage Events." Defaults to 'Events.'
          - name: "events"
            label: "Events"
            widget: "list"
            fields:
              - name: "title"
                label: "Title"
                widget: "string"
              - name: "start-date"
                label: "Start Date"
                widget: "datetime"
                date_format: *date_iso_format
                hint: "First day of event"
              - name: "end-date"
                label: "End Date"
                widget: "datetime"
                date_format: *date_iso_format
                required: false
                hint: "Last day of event. Leave empty for single-day events."
              - name: "location"
                label: "Location"
                widget: "string"
              - name: "description"
                label: "Description"
                widget: "string"
                required: false
              - name: "external-link"
                label: "External Link URL"
                widget: "string"
                required: false
      - name: "schedule"
        label: "Schedule"
        widget: "object"
        required: false
        collapsed: true
        fields:
          - name: "kicker"
            label: "Section Kicker"
            widget: "string"
            required: false
            hint: "This is the text that appears above the section title. Defaults to 'Join Us'."
          - name: "title"
            label: "Section Title"
            widget: "string"
            required: false
            hint: This is the title for the Schedule section header. For example, "Main Stage Schedule." Defaults to 'Schedule.'
          - name: "days"
            label: "Event Days"
            widget: "list"
            required: false
            fields:
              - name: "date"
                label: "Date"
                widget: "datetime"
                date_format: *date_iso_format
              - name: "events"
                label: "Events"
                widget: "list"
                fields:
                  - name: "tag"
                    label: "Event Tag"
                    widget: "string"
                    required: false
                    hint: "A short label or category (e.g., event location or host) displayed above the event title."
                  - name: "title"
                    label: "Event Title"
                    widget: "string"
                  - name: "description"
                    label: "Description"
                    widget: "text"
                    required: false
                  - name: "moderators"
                    label: "Moderators"
                    label_singular: "Moderator"
                    widget: "list"
                    fields:
                      - name: "name"
                        label: "Full Name"
                        widget: "string"
                      - name: "company"
                        label: "Company"
                        widget: "string"
                    required: false
                  - name: "speakers"
                    label: "Speakers"
                    label_singular: "Speaker"
                    widget: "list"
                    fields:
                      - name: "name"
                        label: "Full Name"
                        widget: "string"
                      - name: "company"
                        label: "Company"
                        widget: "string"
                    required: false
                  - name: "start-time"
                    label: "Start Time"
                    widget: "datetime"
                    time_format: *time_iso_format
                    picker_utc: true
                  - name: "end-time"
                    label: "End Time"
                    widget: "datetime"
                    time_format: *time_iso_format
                    picker_utc: true
                    required: false
                    hint: "Leave empty if the event has no end time."
                  - name: "location"
                    label: "Location"
                    widget: "string"
                  - name: "url"
                    label: "URL"
                    widget: "string"
                    required: false
      - name: "speakers"
        label: "Speakers Section"
        widget: "object"
        required: false
        fields:
          - name: "kicker"
            label: "Section Kicker"
            widget: "string"
            required: false
            hint: "This is the text that appears above the section title. Defaults to 'Speakers'."
          - name: "title"
            label: "Section Title"
            widget: "string"
            required: false
            hint: "The main heading for the speakers section. Defaults to 'Speakers'."
          - name: "description"
            label: "Section Description"
            widget: "text"
            required: false
            hint: "A brief description of the speakers section"
          - name: "speakers_list"
            label: "Speakers List"
            widget: "list"
            required: false
            fields:
              - name: "name"
                label: "Full Name"
                widget: "string"
              - name: "title"
                label: "Job Title"
                widget: "string"
              - name: "company"
                label: "Company"
                widget: "string"
              - name: "linkedin"
                label: "LinkedIn Profile URL"
                widget: "string"
                required: false
                hint: This field is optional in case the speaker does not have a LinkedIn profile. If they do, please enter the URL.
              - name: "image"
                label: "Image"
                widget: "object"
                collapsed: true
                fields:
                  - name: "src"
                    label: "URL"
                    widget: "image"
                    allow_multiple: false
                    choose_url: false
                    hint: Use https://squoosh.app/ to compress images before uploading. Choose the WebP format and resize the image to max. 200px width, ensuring the aspect ratio is maintained at 2:3 (e.g., 200px width and 300px height).
                  - *image_alt_config
      - name: "sponsors"
        label: "Sponsors"
        widget: "object"
        required: false
        collapsed: true
        fields:
          - name: "first-tier"
            label: "First Tier"
            widget: "list"
            required: false
            min: 0
            max: 4
            fields: *event_sponsor_config
          - name: "second-tier"
            label: "Second Tier"
            widget: "list"
            required: false
            min: 0
            max: 5
            fields: *event_sponsor_config
          - name: "third-tier"
            label: "Third Tier"
            widget: "list"
            required: false
            min: 0
            max: 7
            fields: *event_sponsor_config
      - name: "recap"
        label: "Recap"
        widget: "object"
        required: false
        collapsed: true
        fields:
          - name: "youtube-embed-url"
            label: "Event Recap YouTube Embed URL"
            widget: "string"
            required: false
            hint: "Please enter the YouTube embed URL in the format: 'https://www.youtube.com/embed/{videoId}', without additional query parameters. Will be shown in the recap section."
          - name: "youtube-playlist-url"
            label: "Event Recap YouTube Playlist URL"
            widget: "string"
            required: false
            hint: "Please enter the YouTube playlist URL in the format: 'https://www.youtube.com/playlist?list={playlistId}', without additional query parameters. Will be shown in the page header."
      - *meta_config_with_title_fallback
