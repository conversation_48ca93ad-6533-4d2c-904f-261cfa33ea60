@import 'tailwindcss';
@source "../../../../../node_modules/@filecoin-foundation/ui";

@plugin "@tailwindcss/typography";
@plugin "@headlessui/tailwindcss";
@plugin "@tailwindcss/forms";

@theme {
  --color-brand-100: #eff6fc;
  --color-brand-200: #d8ebfb;
  --color-brand-300: #73b4ed;
  --color-brand-400: #0090ff;
  --color-brand-500: #154ed9;
  --color-brand-600: #0621a4;
  --color-brand-700: #06094e;
  --color-brand-800: #08072e;

  --color-brand-success: theme('colors.green.400');
  --color-brand-error: theme('colors.red.400');

  --color-icon-primary: theme('colors.brand.300');
  --color-icon-accent: theme('colors.brand.200');
  --color-icon-subtle: theme('colors.brand.400');
  --color-icon-success: theme('colors.brand.success');
  --color-icon-error: theme('colors.brand.error');

  --color-brand-overlay: theme('colors.brand.400');

  --spacing-readable: 60ch;
}

@utility brand-outline {
  outline: 2px solid var(--color-brand-100);
  outline-offset: 0px;
  border-color: transparent;
}

@utility prose {
  --tw-prose-body: theme('colors.brand.100');
  --tw-prose-headings: theme('colors.brand.100');
  --tw-prose-bold: theme('colors.brand.100');
  --tw-prose-quotes: theme('colors.brand.100');
  --tw-prose-quote-borders: theme('colors.brand.300');
  --tw-prose-counters: theme('colors.brand.100');
  --tw-prose-bullets: theme('colors.brand.100');
  --tw-prose-code: theme('colors.brand.100');
  --tw-prose-pre-bg: theme('colors.brand.700');
  --tw-prose-pre-code: theme('colors.brand.100');
  --tw-prose-captions: theme('colors.neutral.400');
  --tw-prose-td-borders: theme('colors.brand.300');
  --tw-prose-th-borders: theme('colors.brand.300');

  & code {
    &::before,
    &::after {
      @apply content-none!;
    }
  }

  & pre {
    @apply border-brand-600 border border-solid;
  }

  & iframe {
    @apply w-full;

    &[src*='youtube.com'] {
      @apply aspect-video h-auto w-full;
    }
  }

  & figure {
    @apply text-center;
  }

  & figcaption {
    @apply text-left text-sm;
  }

  & table thead th {
    @apply text-brand-300;
  }
}

@layer components {
  /* SHARED */
  .social-link {
    @apply hover:text-brand-400;
  }

  /* ARTICLE HEADER */
  .article-header-image {
    @apply rounded-lg;
  }

  /* AVATAR */
  .avatar-background {
    @apply bg-brand-600;
  }

  .avatar-full-name {
    @apply text-brand-100;
  }

  .avatar-initials {
    @apply text-brand-200;
  }

  .avatar-ring {
    @apply text-brand-700;
  }

  .avatar-group-expanded-full-name {
    @apply text-brand-300;
  }

  /* BREADCRUMBS */
  .breadcrumb-link {
    @apply focus:brand-outline hover:underline;

    &:not([aria-current='page']) {
      @apply text-brand-300;
    }

    &[aria-current='page'] {
      @apply text-brand-400;
    }
  }

  /* BLOG POST HEADER */
  .blog-post-header-date {
    @apply text-brand-300;
  }

  /* BUTTONS */
  .button {
    @apply focus:brand-outline rounded-lg border px-6 font-semibold transition sm:px-9;

    &.button--primary {
      @apply border-brand-300 bg-brand-300 text-brand-800 hover:border-brand-400 hover:bg-brand-400;
    }

    &.button--ghost {
      @apply bg-brand-800 text-brand-100 hover:border-brand-400 hover:text-brand-400 focus:text-brand-400 border-white;
    }

    &:disabled {
      @apply bg-brand-200;
    }
  }

  /* CARD */
  .card {
    @apply bg-brand-700/30 rounded-lg;

    &[data-with-link='true'] {
      @apply hover:bg-brand-overlay/20;
    }

    &.card-border--lighter {
      @apply border-brand-300;
    }

    &.card-border--light {
      @apply border-brand-400;
    }

    &.card-border--base {
      @apply border-brand-500;
    }

    &.card-border--dark {
      @apply border-brand-600;
    }
  }

  .card-image {
    @apply rounded-lg px-1 pt-1;

    &.card-image-spacing {
      @apply px-6 pt-4;
    }
  }

  .card-link {
    @apply text-brand-300 rounded-lg;
  }

  /* COPY TO CLIPBOARD */
  .copy-to-clipboard-button {
    @apply hover:text-brand-400;
  }

  /* FILTER SIDEBAR */
  .filter-sidebar-button {
    @apply text-brand-300 hover:bg-brand-700 rounded-lg;

    &[data-selected='true'] {
      @apply bg-brand-700 text-brand-400;
    }
  }

  /* ICON BUTTONS */
  .icon-button {
    @apply border-brand-300 text-brand-300 rounded-lg border;
  }

  /* KEY MEMBER CARD */
  .key-member-card {
    @apply border-brand-500 rounded-lg border p-1;

    &[data-with-link='true'] {
      @apply hover:bg-brand-overlay/20;
    }

    .key-member-card-image {
      @apply rounded-sm;
    }

    .key-member-card-title {
      @apply text-brand-300;
    }

    .key-member-card-link {
      @apply text-brand-300 rounded-lg;
    }
  }

  /* LISTBOX */
  .listbox-button {
    @apply bg-brand-800 text-brand-300 hover:text-brand-400 rounded-lg border;

    &[aria-invalid='true'] {
      @apply border-brand-error;
    }
  }

  .listbox-options {
    @apply border-brand-100 bg-brand-800 text-brand-100 rounded-lg border;
  }

  .listbox-option {
    @apply text-brand-100 ui-active:bg-brand-500;
  }

  .listbox-group-header {
    @apply text-brand-300;
  }

  /* META */
  .meta {
    @apply text-brand-300;
  }

  /* NOTIFICATION DIALOG */
  .notification {
    @apply border-brand-100/40 bg-brand-800 rounded-lg border;

    .notification-close-button {
      @apply text-brand-200 hover:text-brand-400 rounded-md;
    }
  }

  /* PAGE HEADER */
  .page-header-image {
    @apply border-brand-100 rounded-lg border;

    &.page-header-image-spacing {
      @apply px-6 pt-4;
    }
  }

  /* PAGINATION */
  .pagination {
    @apply bg-brand-300 text-brand-700 rounded-lg p-1;
  }

  .pagination-delimiter {
    @apply text-brand-800;
  }

  .pagination-navigation-button {
    @apply bg-brand-300 rounded-sm;

    &:not(:disabled) {
      @apply focus-visible:outline-2 focus-visible:outline-white;
    }

    &:disabled {
      @apply cursor-not-allowed;
    }

    &:not(:disabled):hover {
      @apply bg-brand-400;
    }
  }

  .pagination-navigation-number {
    @apply h-full w-full rounded-sm;

    &[data-current='true'] {
      @apply bg-brand-800 text-brand-100;
    }

    &[data-current='false'] {
      @apply bg-brand-300 text-brand-700;
    }

    &[data-current='false']:hover {
      @apply bg-brand-400;
    }
  }

  /* SEARCH */
  .search-input {
    @apply form-input border-brand-300 bg-brand-800 placeholder:text-brand-300 hover:border-brand-400 hover:placeholder:text-brand-400 focus:brand-outline focus:text-brand-100 focus:placeholder:text-brand-100 rounded-lg;
  }

  .search-icon {
    @apply text-brand-300 peer-hover:text-brand-400 peer-focus:text-brand-100;
  }

  .search-clear-button {
    @apply rounded-sm;
  }

  /* SHARE ARTICLE */
  .share-article-list {
    @apply gap-6;
  }

  .share-article-title {
    @apply text-brand-300;
  }

  .share-article-tooltip-link {
    @apply hover:text-brand-400;
  }

  /* SITE LAYOUT */
  .site-layout {
    @apply bg-brand-800 text-brand-100;
  }

  /* SLIDE OVER */
  .slide-over-panel {
    @apply bg-brand-800;
  }

  /* TAGS */
  .tag-label {
    @apply bg-brand-800 text-brand-100 rounded-lg border capitalize;

    &.tag-label--primary {
      @apply border-brand-300;
    }

    &.tag-label--secondary {
      @apply border-brand-500;
    }

    &.tag-label--callout {
      @apply border-brand-error;
    }
  }

  /* TEXT LINKS */
  .text-link {
    @apply text-brand-300 focus:brand-outline no-underline hover:underline;
  }

  /* TOC */
  .toc-header {
    @apply text-brand-200 text-sm font-semibold uppercase;
  }

  /* TOOLTIP */
  .tooltip {
    @apply bg-brand-200 text-brand-800 rounded-lg p-2;

    .tooltip-arrow {
      @apply fill-brand-200;
    }
  }
}
