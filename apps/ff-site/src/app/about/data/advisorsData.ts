import type { MemberData } from '@/types/memberType'

import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON><PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import Denelle<PERSON>ixon from '@/assets/headshots/<PERSON><PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON>n from '@/assets/headshots/Georgia_Quinn.png'
import <PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON><PERSON>.png'
import Rye<PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>ye_<PERSON><PERSON>.png'
import <PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'

export const advisorsData: Array<MemberData> = [
  {
    name: '<PERSON>',
    title: 'Founder, Internet Archive',
    linkedin: 'https://www.linkedin.com/in/brewster-kahle-2a647652/',
    image: <PERSON>,
  },
  {
    name: '<PERSON>',
    title: 'Chief <PERSON> Strategist, Linux Foundation',
    linkedin: 'https://www.linkedin.com/in/brianbehlendorf/',
    image: BrianB<PERSON>lendorf,
  },
  {
    name: 'Denelle <PERSON>',
    title: 'CEO, Stellar Development Foundation',
    linkedin: 'https://www.linkedin.com/in/denelle-dixon-967a236/',
    image: DenelleDixon,
  },
  {
    name: '<PERSON> Ro',
    title: 'CEO, Global Blockchain Business Council',
    linkedin: 'https://www.linkedin.com/in/sandraro/',
    image: SandraRo,
  },
  {
    name: 'Wendy Hanamura',
    title: 'Director of Partnerships, Internet Archive',
    linkedin: 'https://www.linkedin.com/in/wendyhanamura/',
    image: WendyHanamura,
  },
  {
    name: 'Katie Biber',
    title: 'Chief Legal Officer, Paradigm',
    linkedin: 'https://www.linkedin.com/in/katiebiber/',
    image: KatieBiber,
  },
  {
    name: 'Georgia Quinn',
    title: 'Former General Counsel, Anchorage and CoinList ',
    linkedin: 'https://www.linkedin.com/in/georgia-quinn-287bb733/',
    image: GeorgiaQuinn,
  },
  {
    name: 'Veronica McGregor',
    title: 'Chief Legal Officer, Exodus',
    linkedin: 'https://www.linkedin.com/in/vemcgregor/',
    image: VeronicaMcGregor,
  },
  {
    name: 'Alex Feerst',
    title: 'CEO, Murmuration Labs & Former General Counsel, Medium',
    linkedin: 'https://www.linkedin.com/in/feerst/',
    image: AlexFeerst,
  },
  {
    name: 'Rye Barcott',
    title: 'CEO, With Honor',
    linkedin: 'https://www.linkedin.com/in/ryebarcott/',
    image: RyeBarcott,
  },
  {
    name: 'Catherine Stihler',
    title:
      'Former CEO, Creative Commons & Former Member of European Parliament',
    linkedin: 'https://www.linkedin.com/in/catherine-stihler/',
    image: CatherineStihler,
  },
]
