import type { MemberData } from '@/types/memberType'

import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON><PERSON>_<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON><PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>.png'
import <PERSON><PERSON><PERSON><PERSON> from '@/assets/headshots/<PERSON>_<PERSON>.png'

export const boardMembersData: Array<MemberData> = [
  {
    name: '<PERSON>',
    title: 'President & Board Chair, FF & FFDW',
    linkedin: 'https://www.linkedin.com/in/martabelcher/',
    image: <PERSON><PERSON><PERSON><PERSON>,
  },
  {
    name: '<PERSON>',
    title: 'CEO, Project Liberty Institute',
    linkedin: 'https://www.linkedin.com/in/sheilawarren/',
    image: <PERSON><PERSON><PERSON><PERSON>,
  },
  {
    name: '<PERSON><PERSON>',
    title: 'President, Solana Policy Institute',
    linkedin: 'https://www.linkedin.com/in/smithk1/',
    image: <PERSON><PERSON><PERSON><PERSON>,
  },
  {
    name: '<PERSON>',
    title: 'Founder & <PERSON>O, Linden Lab / Second Life',
    linkedin: 'https://www.linkedin.com/in/philip<PERSON>dale',
    image: <PERSON><PERSON><PERSON>,
  },
  {
    name: '<PERSON>',
    title: 'Co-Founder & President, Rendezvous Robotics',
    linkedin: 'https://www.linkedin.com/in/joelandon/',
    image: JoeLandon,
  },
]
