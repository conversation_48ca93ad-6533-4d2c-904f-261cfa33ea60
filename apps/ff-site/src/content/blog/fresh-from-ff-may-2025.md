---
title: "Fresh From FF: May, 2025"
created-on: 2025-05-05T13:43:00.000Z
updated-on: 2025-05-05T13:43:00.000Z
published-on: 2025-05-05T13:43:00.000Z
category: events
description: "Check out the latest updates about what Filecoin Foundation has been up to. "
image:
  src: /assets/images/0215-ff-24-.webp
seo:
  title: "Fresh From FF: May, 2025"
  description: "Check out the latest updates about what Filecoin Foundation has
    been up to. "
---

[Filecoin's Network Version 25 "Teep" upgrade has arrived](https://filecoin.io/blog/posts/filecoins-nv25-teep-network-upgrade-is-here/). The upgrade introduces improvements that make Filecoin more efficient and adaptable, simplifying storage provider economics and enhancing developer tooling. Overall, these changes make the network more flexible, adaptable, and dynamic for builders, storage providers, and storage clients alike.

Fast Finality in Filecoin (F3) went live on mainnet on April 29. F3 represents one of the [biggest storage-retrieval lifecycle improvements](https://x.com/FilFoundation/status/1816890979032727563) in the network's history, speeding up finality on the network by up to 100x and making the future of Filecoin fast, secure, and accessible to a wider range of applications than ever before by reducing finality time from hours down to a few minutes.

[The FDS Virtual Kickoff Series is complete!](https://youtube.com/playlist?list=PL_0VrY55uV19Fx15jwUdSoPyTqywrhHe7) Thanks for tuning in to hear important updates and ecosystem developments, including Filecoin's network growth, market shifts, and the ambitious roadmap shaping the future of the network. Catch the recorded talks on the [FDS-6 playlist](https://youtube.com/playlist?list=PL_0VrY55uV19Fx15jwUdSoPyTqywrhHe7) before the in-person leg begins on May 13 in Toronto.

## Ecosystem

- One of the tracks Filecoin Foundation led at FDS-6 Virtual was on storage adoption, [Winning Clients, Scaling Demand, and Lessons from the Field](https://www.fildev.io/FDS-6#storage-adoption-winning-clients-scaling-demand-and-lessons-from-the-field-thu-apr-24). Highlights from the track include:

  - Head of Technical Programs Stef Magdalinski led the State of Filecoin Client Success: Progress, Pain Points & What's Next, a session providing an update on client success, and helping shape the next wave of improvements driving Filecoin toward widespread adoption.
  - Enterprise Growth Lead Jen King presented the Real-World Wins: ICP Profiles Driving Adoption Across AI, DataDAOs, and Web2 talk, breaking down what makes clients successful with Filecoin—covering use cases, decision drivers, and go-to-market alignment.
  - Data Onboarding Lead Paul Wagner shared the Mapping Filecoin Adoption: Ecosystem Growth & New Onramps session, offering insights into the current ecosystem map, and highlighting key traction zones across industries, regions, and use cases.
  - Quality Engineer Jefferson Sankara led The State of Open Source Tools in the Filecoin Network: A Spotlight on Singularity, exploring the current state of open source within the Filecoin Network, using Singularity as a case study.

All session recordings are available as part of the [Storage Adoption: Winning Clients, Scaling Demand, and Lessons from the Field](https://youtube.com/playlist?list=PL_0VrY55uV1-KsX4rbD6ibLyMAiT-VBqk) playlist on the Filecoin YouTube channel.

## Network Operations

### Governance

- Three FIPS were introduced in NV25 "Teep," including improvements that make Filecoin more efficient and adaptable, simplifying storage provider economics and enhancing developer tooling:
- Transient storage via EIP-1153 in the FEVM ([FIP-0097](https://github.com/filecoin-project/FIPs/blob/master/FIPS/fip-0097.md));
- A simplified fixed-percentage sector termination fee ([FIP-0098](https://github.com/filecoin-project/FIPs/blob/master/FIPS/fip-0098.md));
- Streamlined data onboarding with the removal of the Batch Balancer and gas constraints ([FIP-0100](https://github.com/filecoin-project/FIPs/blob/master/FIPS/fip-0100.md)), alongside mainnet activation of F3 parameters.

### Fil Plus

- [Making Space for Experimentation: The Experimental Pathway Metaallocator](/blog/making-space-for-experimentation-the-experimental-pathway-metaallocator). The Filecoin Plus (Fil+) program is adding a long-awaited Experimental Pathway Metaallocator. Over the past year, the Fil+ governance team, in close collaboration with FIDL and the broader community, has been working to streamline Datacap allocation by moving toward a Pathway Metaallocator model — a more accountable, transparent, and efficient system for approving storage clients.

## Comms, Marketing, & Events

- Filecoin Foundation published the April 2025 Quarterly Update. FF's second quarterly update spotlights how the Filecoin network provides the essential infrastructure for building AI that's not just smart — but also transparent, accountable, and trustworthy. [Read on](/blog/filecoin-foundation-quarterly-update-april-2025).
- Join us for [FIL Toronto](/events/fil-toronto-consensus-2025)! Connect with the ecosystem, explore the latest updates, and discover how you can help build the decentralized web. FIL Toronto kicks off with the in-person leg of [FDS-6](https://lu.ma/eyk46hz0) and continues with more [ecosystem events](https://lu.ma/lkcswk34) and [coworking](https://lu.ma/g7vz0rt8) against the backdrop of Consensus. [Register now](https://lu.ma/eyk46hz0).
- Check out the latest episode of [DWeb Decoded](https://youtu.be/BA1Y9ZtaqjM?feature=shared), where Jonathan Schwartz, CEO of GLIF, the podcast to reflect on his years building in the Filecoin ecosystem—from early experiments with IPFS to launching key infrastructure like the Filecoin wallet, multisig tools, and GLIF.
