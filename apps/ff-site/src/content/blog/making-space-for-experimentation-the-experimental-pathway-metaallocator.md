---
title: "Making Space for Experimentation: The Experimental Pathway Metaallocator"
created-on: 2025-04-14T14:25:00.000Z
updated-on: 2025-04-14T14:25:00.000Z
published-on: 2025-04-14T14:25:00.000Z
category: ecosystem
description: Over the past year, the Fil+ governance team, in close
  collaboration with FIDL and the broader community, has been working to
  streamline Datacap allocation by moving toward a Pathway Metaallocator model—a
  more accountable, transparent, and efficient system for approving storage
  clients.
image:
  src: /assets/images/fil-header-1-.webp
seo:
  description: "The Filecoin Plus (Fil+) program is adding a long-awaited
    Experimental Pathway Metaallocator."
---

The Filecoin Plus (Fil+) program is adding a long-awaited Experimental Pathway Metaallocator. Over the past year, the Fil+ governance team, in close collaboration with FIDL and the broader community, has been working to streamline Datacap allocation by moving toward a Pathway Metaallocator model—a more accountable, transparent, and efficient system for approving storage clients. Earlier this year, the [Manual Pathway Metaallocator](https://www.fidl.tech/news/manual-pathway-metaallocator) launched as the first implementation of this model. Now, together, we're introducing the Experimental Pathway Metaallocator as the next major step.

## Why We Need Room to Experiment

The governance team has long recognized that building robust, scalable allocation systems requires experimentation. Innovation in decentralized governance depends on infrastructure that supports iteration, learning, and risk-taking—without compromising the stability of the wider network.

The Experimental Pathway Metaallocator creates exactly that kind of space. It offers Fil+ contributors and developers a dedicated pathway to test new allocation methods, UI models, and incentive structures. By lowering the barrier to entry, we can encourage more builders to engage with Datacap allocation logic, while maintaining appropriate safeguards.

## Built for Builders, Designed for Safety

Proposed in this [governance discussion](https://github.com/filecoin-project/Allocator-Governance/issues/313), the Experimental Pathway was intentionally scoped to allow for innovation with guardrails.

From the governance perspective, the value of this pathway is twofold: it opens up access to a broader group of contributors, and it gives the network better visibility into what kind of ideas, tooling, and practices are emerging at the edges.

Key parameters include:

- Lower Datacap ceilings: Allocators in this pathway receive up to 1 PiB, minimizing risk while allowing space for real experimentation.

- Faster review cycles: Tighter feedback loops support active iteration and learning.

- Transparency through reporting: Participants are expected to publish metrics and insights throughout their process.

## The Role of FIDL in the Experimental Pathway Metaallocator

Filecoin Incentive Design Labs (FIDL) is supporting this pathway by stewarding its structure and providing essential tooling and feedback loops. The shared goal with governance is to help the ecosystem onboard valuable data and test new approaches to allocation in a structured, observable way.

Specifically, FIDL is:

- Supporting prospective allocators through the application and onboarding process.

- Providing dashboards and metrics to track outcomes.

- Sharing learnings with the Fil+ governance community to support evidence-based decisions.

The Experimental Pathway offers a lightweight mechanism to validate ideas before they graduate into one of the main Fil+ allocation routes—Manual, Market-based, or Automated.

Organizations that have previously submitted applications but haven’t yet reached the MVP threshold are especially encouraged to apply. This is a chance to test ideas and gather the evidence needed to progress toward full deployment.

## Looking Ahead: Toward Full Pathway Allocation

The Experimental Pathway is more than a sandbox. It’s a deliberate mechanism for extending participation and accelerating innovation across Fil+. As governance continues to move all Datacap allocation into pathway-based systems, having an on-ramp for experimentation ensures we don’t just scale—but scale thoughtfully.

If you’re building new allocation logic, testing novel incentives, or prototyping better interfaces—this is the place to start.

To apply, use the form on [FIDL’s website](https://www.fidl.tech/apply-for-datacap_1). You can also follow the development and governance discussions here:

- [Experimental Pathway Metaallocator proposal](https://github.com/filecoin-project/Allocator-Governance/issues/313)
- [Manual Pathway Metaallocator](https://github.com/filecoin-project/Allocator-Governance/issues/282)
