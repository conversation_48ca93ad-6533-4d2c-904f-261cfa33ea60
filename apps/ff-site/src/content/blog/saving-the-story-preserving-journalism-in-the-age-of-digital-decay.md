---
title: "Saving the Story: Preserving Journalism in the Age of Digital Decay"
created-on: 2025-06-18T02:55:00.000Z
updated-on: 2025-06-18T02:55:00.000Z
published-on: 2025-06-18T02:55:00.000Z
category: use-cases
description: Today, we’re excited to share that Filecoin Foundation’s sister
  non-profit, Filecoin Foundation for the Decentralized Web (FFDW), is working
  with Fasila, an organization dedicated to supporting local journalism, to help
  fortify media infrastructure for the future.
image:
  src: /assets/images/060925-fasila-main.webp
seo:
  description: Today, we’re excited to share that Filecoin Foundation’s sister
    non-profit, Filecoin Foundation for the Decentralized Web (FFDW), is working
    with Fasila to help fortify media infrastructures for the future.
---

In an increasingly digital era, the stories we tell — and how we preserve them — matter more than ever. Journalism remains a cornerstone of democratic society: documenting our shared history, holding power to account, and amplifying unheard voices. But as media becomes increasingly digital, it also becomes more fragile. From censorship to misinformation to the quiet disappearance of archives, the digital record of our times is under threat.

The Filecoin network offers a powerful alternative: it allows anyone to store data in a verifiable and resilient way. For journalism and media, that means content can be archived and remain accessible even if the original publisher goes offline. By distributing data across multiple nodes, Filecoin eliminates single points of failure, while cryptographic proofs ensure that the content’s integrity is preserved over time. This infrastructure enables media organizations and independent journalists to safeguard their work against technological breakdowns and ensure the public record remains intact.

Today, we’re excited to share that Filecoin Foundation’s sister non-profit, Filecoin Foundation for the Decentralized Web (FFDW), is working with [Fasila](https://www.fasila-inc.com/), an organization dedicated to supporting local journalism, to help fortify media infrastructure for the future.

![](/assets/images/060925-fasila-stats.webp)

## The Fragility of Digital Records

Traditional news archives are often stored on centralized servers — vulnerable to corporate decisions, hacks, or policy changes. Entire bodies of work can vanish overnight when a platform shutters or a server fails. This fragility isn’t just theoretical. It’s happened before, and it’s happening now. 

Just this year, ABC News shuttered [FiveThirtyEight](https://www.theguardian.com/us-news/2025/mar/05/abc-news-538-shut-down), the data-driven news platform known for its election and polling analysis, removing the entire archive. Last year, [MTV News](https://variety.com/2024/digital/news/mtv-news-website-archives-pulled-offline-1236047163/) closed down and more than two decades’ worth of content was taken offline. Renowned tech journalist Kara Swisher lost years of her reporting on All Things Digital. 

It’s part of a growing pattern.

The [‘Not Your Parents Web’](/blog/the-web-isn-t-forever-new-research-findings-from-not-your-parents-web-project) project found that **the median lifespan of a web page is only 2.3 years**. Research data from [Pew Research Center](https://www.pewresearch.org/wp-content/uploads/sites/20/2024/05/pl_2024.05.17_link-rot_report.pdf) supports these findings: **38% of web pages that existed in 2013 are no longer accessible.** A [2021 study](https://www.cjr.org/analysis/linkrot-content-drift-new-york-times.php) by Harvard Law School and the New York Times looked at 2.2 million URLS and found that 25% of links in the New York Times articles were completely broken — no longer pointing to accurate sources. Recently, the [Internet Archive](https://blog.archive.org/2025/02/06/update-on-the-2024-2025-end-of-term-web-archive/) and [Harvard Law’s Library Innovation Lab](https://hls.harvard.edu/amicus-libris/the-data-gov-archive-at-the-harvard-law-school-library-innovation-lab/) embarked on initiatives to preserve government websites and datasets during the recent U.S. presidential administration. 

All of these examples underscore a hard truth: the web is not forever, despite what we once believed. Media publications and journalists can no longer assume the digital world will remember what they publish.

Human beings have been documenting history since the beginning, from cave paintings to medieval manuscripts. Today, we have more tools than ever to communicate and preserve our culture and history for future generations — but only if we take deliberate steps to safeguard it.

## Decentralized Media in Action

Journalists and media organizations around the world are already using Filecoin in powerful ways to protect their work and help people trust what they see online.  

Here’s a quick look at how decentralized storage is supporting resilience in modern journalism. 

**Fighting Link Rot and Maintaining Document Access**: It’s vital we maintain access to critical documents –– like court decisions and FOIA records –– that are prone to broken links and decayed access. [MuckRock](https://www.muckrock.com/news/archives/2024/sep/11/featured-add-on-push-to-ipfs-filecoin/) integrated IPFS and Filecoin into DocumentCloud, a platform hosting over 5.5 million verified documents. More than 500,000 files from newsrooms, nonprofits, and researchers have been preserved on Filecoin –– ensuring continual access to key public records. 

**Resilience for Sensitive Archives**: Sensitive archives –– like evidence of war crimes or human rights offenses –– are some of the most vulnerable digital records. [Starling Lab](https://www.starlinglab.org/) uses Filecoin and other DWeb protocols to capture, store, and verify sensitive digital records, like evidence of war crimes and genocide survivor testimony. In The DJ and the War Crimes, a Rolling Stone investigative story, Starling Lab implemented over a dozen cryptographic technologies, including Filecoin and IPFS, to authenticate and preserve the [entire investigative archive](https://investigation.rollingstone.com/dj-photo-war-crimes-bosnia/archive/the-photograph/). 

**Combating Misinformation and Deepfakes:** In an age of generative AI, distinguishing real from fake content is increasingly difficult, especially during high-stakes events like elections. [Numbers Protocol](https://www.numbersprotocol.io/) is using Filecoin to support projects like [VoteTW 2024](https://votetw2024.numbersprotocol.io/En), a project that allowed voters to verify the origin and integrity of images and videos during Taiwan’s elections. 

**Supporting Citizen Journalists:** Independent and frontline journalists often work in high-risk environments with few tools to protect the integrity and longevity of their work.[Guardian Project](https://proofmode.org/blog/cuttingroom) in collaboration with Okthanks and WITNESS, developed ProofMode — a mobile tool that captures and cryptographically verifies photos and videos. ProofMode recently integrated C2PA standards into the CuttingRoom Reporter mobile app, empowering journalists and other content creators to capture verifiable video directly from their smartphones. 

**Ensuring Media Redundancy**: News outlets often lose control of their archives due to corporate ownership changes, shutdown, or technical failure. Filecoin Foundation recently partnered with [The Defiant](https://thedefiant.io/news/defi/the-defiant-to-preserve-article-archives-on-filecoin) to create redundant, tamper-resistant backups of its reporting, ensuring continued access for the long term.

**Decentralized Distribution Platforms**: Journalism publishing via platforms like Medium or Substack is subject to centralized control and platform risks. [Distributed Press](https://distributed.press/en/), with support from FFDW, is building beginner-friendly, open-source publishing tools that give creators a decentralized, no-code alternative to centralized platforms — empowering writers to publish freely and retain control of their work.

![](/assets/images/060925-fasila-relationship-1-.webp)

## Announcing: FFDW + Fasila

As part of the mission to preserve humanity’s most important information, FFDW is working with [Fasila](https://www.fasila-inc.com/), an organization rooted in local journalism and storytelling. Through this collaboration, Fasila will support more than 20 journalists in preserving their most critical reporting. Stories from their journalist partners — along with associated digital records— will be stored on the Filecoin network and made accessible through IPFS, ensuring these vital narratives are not lost to time, censorship, or technological failure. 

The archived stories will be showcased on a dedicated web page, enriched with metadata and contextual descriptions to support discovery and analysis by relevant communities, as well as researchers, educators, journalists, and filmmakers. By opening access to these archives, the initiative highlights the importance of resilient storage in protecting essential stories, promotes open access to historical records, and offers a replicable model for preserving critical content for future generations.

This project marks another tangible step in safeguarding cultural memory in the digital age — and demonstrates the power of decentralized storage to equip and empower journalists today and tomorrow. Follow FFDW for updates on this project.

![](/assets/images/060925-fasila-how-1-.webp)

## Learn More About Media and Decentralization

If you're curious in learning more about how decentralized technologies are reshaping journalism and media, tune into these episodes of _DWeb Decoded_. Each one explores a unique perspective on the intersection of blockchain, media, and emerging tech.

- [AI Enhanced Blockchain-Based Lifeforms with Primavera De Filippi](https://www.youtube.com/watch?v=wli8UmjlrV8&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=88): Legal scholar and internet activist Primavera De Filippi dives into the intersection of blockchain, AI, and the arts — unpacking implications for media regulation, authorship, and ownership in decentralized ecosystems. 
- [Blockchain-Based Content Licensing with Arikia Millikan](https://www.youtube.com/watch?v=JzAQnRp7bwQ&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=19): Arikia Millikan, founder of CTRL+X, shares how her platform empowers journalists and creators with blockchain-based tools to license, distribute, and monetize their work on their own terms.
- [Crypto Cinema, Web3 Journalism, and Decentralized Media with Cami Russo](https://www.youtube.com/watch?v=3nVyrlf4tqM&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=61&pp=iAQB): Cami Russo, founder of The Defiant, explores how Web3 is transforming documentary filmmaking and journalism — from funding to distribution — through decentralized, crypto-native platforms.
- [Decentralized Publishing with Distributed Press](https://www.youtube.com/watch?v=zZqPomT1Teo&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=93&pp=iAQB): This episode spotlights Distributed Press, a project building open-source, beginner-friendly tools for decentralized publishing — offering creators an alternative to centralized platforms like Medium or Substack.
- [How Numbers Protocol Combats Misinfo with Vera Wu](https://www.youtube.com/watch?v=EpR_PbgeX4Y&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=56&pp=iAQB): Vera Wu of Numbers Protocol explains how blockchain can help verify the authenticity of media content — a critical tool in the fight against misinformation and AI-generated fakes.
- [Protocols, Not Platforms with Mike Masnick](https://www.youtube.com/watch?v=9-xxIJC8wJA&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=91&pp=iAQB): Tech policy thinker Mike Masnick breaks down why “protocols, not platforms” could be the future of digital media — shifting control away from gatekeepers toward open, decentralized systems. 
- [Starling Lab’s Fight for Digital Integrity with Adam Rose](https://www.youtube.com/watch?v=xSGG4eMoA-o&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=54&pp=iAQB): Adam Rose of Starling Lab discusses how cryptographic technologies like IPFS and Filecoin are being used to preserve sensitive digital records — from war crimes evidence to investigative journalism.
- [Using Blockchain & AI to Reinvent Journalism with Brad Keoun](https://www.youtube.com/watch?v=Yl58v4CB9Uk&list=PLp3zrT1ewY0micCUXk2G1B1-ukbpuclJy&index=36&pp=iAQB): Brad Keoun shares insights into how AI and decentralized infrastructure could reinvent journalism — enabling more resilient, trustworthy, and independent reporting in the digital age.

## The Path Ahead

A healthier media ecosystem isn’t just a technology challenge — it’s a societal one. These stories are how we understand our world and remember what matters. Decentralized infrastructure like Filecoin lays the groundwork to support this.  

Whether you're a journalist, a developer, or a digital preservationist, we invite you to join us in our mission to protect humanity’s most important information. Let’s build the tools that protect truth, preserve history, and enable everyone to participate in a more open web.
