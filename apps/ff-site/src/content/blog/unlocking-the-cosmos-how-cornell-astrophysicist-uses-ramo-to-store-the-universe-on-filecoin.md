---
title: "Unlocking the Cosmos: How Cornell Astrophysicist Use<PERSON> <PERSON><PERSON> to Store the
  Universe on Filecoin"
created-on: 2025-06-30T16:12:00.000Z
updated-on: 2025-06-30T16:12:00.000Z
published-on: 2025-06-30T16:12:00.000Z
category: use-cases
description: "Astrophysics meets decentralized storage: why <PERSON><PERSON> is becoming a
  go-to solution for researchers working with petabyte-scale simulations."
image:
  src: /assets/images/062525-cornellramo.webp
seo:
  description: "Astrophysics meets decentralized storage: why <PERSON><PERSON> is becoming a
    go-to solution for researchers working with petabyte-scale simulations."
---

When Dr. <PERSON>, astrophysicist at Cornell University, set out to simulate the behavior of interstellar gas on the fastest supercomputer in the world, he faced a critical problem common to many researchers: where to store the results. His simulations generate petabytes of data—far more than institutional systems can reliably afford to retain in the context of academic budgets and far too valuable to risk losing. Enter Ramo Cloud, a network that helps onboard valuable data to Filecoin, the world’s largest decentralized storage protocol. <PERSON><PERSON> seeks out data-rich clients, prepares their datasets, and works with storage providers to ensure seamless integration into the network.

## The Challenge: Science at the Limits of Storage

Fielding’s simulations aren’t just big—they’re record-breaking. One frame of his 3D model of galactic plasma dynamics is over 80 terabytes. A complete run easily exceeds 5 petabytes of output.

But working on a national supercomputer comes with constraints. Storage space is limited. Administrators purge older files without warning. Researchers are forced to analyze data _in real time_ or risk losing it forever. "You spend years preparing a simulation, only to watch data disappear before you can fully examine it," <PERSON> explains.

## The Cost of Repetition

Re-running these simulations isn’t cheap. The cost of computing alone can reach hundreds of thousands of dollars—sometimes up to a million—just in electricity. When data is lost, rerunning is often the only option. That creates a frustrating and inefficient cycle for scientists trying to push the boundaries of knowledge. "You can't share results. You can’t validate findings. And collaboration becomes impossible," he notes.

## Discovering Ramo

When Fielding was introduced to Ramo, he quickly realized that it might be the solution he needed. He was able to set up a secure, efficient transfer pipeline using Globus, a standard in high-performance computing environments. "It took two minutes to get going," he says. "It’s unbelievably simple."

Ramo is now a core part of his workflow. After running and analyzing a simulation, Fielding archives the large raw outputs with Ramo and keeps only the distilled subset for further study. The result: critical research data is preserved without overwhelming institutional systems.

## Why Filecoin and Decentralized Storage Make Sense

What makes decentralized storage so compelling for researchers isn’t just the cost-effectiveness—it’s the ability to share, verify, and preserve data in ways traditional cloud platforms can’t match. Data stored on Filecoin is immutable, meaning once it’s uploaded, any changes are publicly visible. That ensures research integrity and supports reproducibility, which is essential for credible science.

More importantly, decentralized storage enables transparent sharing across teams. Instead of emailing links or setting up shared folders in siloed institutional clouds, researchers can give collaborators verifiable access to specific data snapshots. This allows for joint projects across institutions, even continents, with a built-in trust layer: no one can quietly alter the data, and everyone can prove who published it first. That’s a powerful shift in the research workflow, especially in high-stakes fields like astrophysics, climate science, and genomics.

## A Scalable Model for Science

This isn’t a one-off experiment. Fielding plans to use Ramo for all future simulations. And he believes this isn’t just his problem. Many university labs around the world face the same pressure: valuable data, limited storage, no sustainable backup. "I can think of a dozen university supercomputers right now where researchers would love to put their data somewhere safe," he says.

Ramo's innovation lies in more than just ease of use. Their data preparation and packing process is finely tuned to the needs of decentralized storage, ensuring that even multi-terabyte research outputs are efficiently chunked, validated, and stored. Combined with their understanding of various data verticals—including but not limited to science—this makes them an ideal on-ramp for valuable datasets across sectors.

As for the future? Ramo continues to grow its pipeline of high-value data, helping clients turn datasets into durable, verifiable digital assets. And as Vukasin Vukoje, CEO of web3mine –the company behind Ramo– underscores, "At this point, the network has probably surpassed the data footprints of many Fortune 500 environments—and we’re just getting started."

If you are looking to store massive datasets visit use.ramo.computer to get started. For hardware operators with idle storage capacity, visit provide.ramo.io to learn how you, too, can provide resources on the Ramo network and help shape the future of the cloud.
