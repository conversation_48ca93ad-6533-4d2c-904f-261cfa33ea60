{"name": "filecoin-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@phosphor-icons/react": "^2.1.10", "next": "^15.3.4", "nuqs": "^2.4.3", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0"}, "devDependencies": {"@filecoin-foundation/eslint-config": "0.0.0", "@filecoin-foundation/typescript-config": "0.0.0", "@filecoin-foundation/ui": "0.0.0", "@filecoin-foundation/utils": "0.0.0", "@phosphor-icons/react": "^2.1.10", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.8", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "tailwindcss": "^4.1.11", "typescript": "^5.8.3"}}