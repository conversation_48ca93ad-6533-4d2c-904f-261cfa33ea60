@import 'tailwindcss';
@source "../../../../../node_modules/@filecoin-foundation/ui";

@plugin "@tailwindcss/typography";

@theme {
  --color-brand-50: oklch(0.98 0.0157 216.67);
  --color-brand-100: oklch(0.95 0.035 219.18);
  --color-brand-200: oklch(0.92 0.0628 215.11);
  --color-brand-300: oklch(0.88 0.1002 212.38);
  --color-brand-400: oklch(0.83 0.1306 218.59);
  --color-brand-500: oklch(0.76 0.1503 232.56);
  --color-brand-600: oklch(0.69 0.1736 245.19);
  --color-brand-700: oklch(0.65 0.193 251.78);
  --color-brand-800: oklch(0.53 0.1592 252.62);
  --color-brand-900: oklch(0.47 0.1224 248.85);
  --color-brand-950: oklch(0.33 0.0795 249.37);

  --color-icon-success: var(--color-green-500);

  --gradient-brand-dark: linear-gradient(
    90deg,
    var(--color-brand-500),
    var(--color-brand-700)
  );

  --gradient-brand-light: linear-gradient(
    90deg,
    var(--color-brand-300),
    var(--color-brand-500)
  );

  --spacing-readable: 60ch;
}

@utility brand-outline {
  outline: 2px solid var(--color-brand-600);
  outline-offset: 0px;
  border-color: transparent;
}

@utility icon-button {
  @apply rounded-full border-2 border-zinc-200;

  .share-article-list & {
    @apply scale-80;
  }
}

@layer components {
  /* BUTTON */
  .button {
    @apply rounded-full border-2 px-6 py-4 text-base font-medium;

    &:focus {
      @apply outline-2 outline-offset-0;
    }

    &.button--primary {
      @apply relative border-transparent;

      &:not(:disabled) {
        &::before {
          background: var(--gradient-brand-dark) border-box;
        }

        &::after {
          @apply opacity-0 transition-opacity duration-200;
          background: var(--gradient-brand-light) border-box;
        }

        &:hover::after,
        &:focus::after {
          @apply opacity-100;
        }

        &::before,
        &::after {
          @apply absolute -inset-0.5 rounded-full border-2 border-transparent;
          content: '';
          mask:
            linear-gradient(var(--color-white)) content-box,
            linear-gradient(var(--color-white));
          mask-composite: exclude;
        }

        &:focus {
          @apply outline-brand-400 transition-none;
        }
      }

      .light-section &,
      .gray-section & {
        @apply text-zinc-900;

        & .button-arrow-icon {
          @apply text-zinc-600;
        }

        & .button-custom-icon {
          @apply text-blue-600;
        }

        &:hover .button-custom-icon,
        &:focus .button-custom-icon {
          @apply text-blue-400 transition-colors duration-200;
        }

        &:disabled {
          @apply border-zinc-300 bg-zinc-100 text-zinc-400;
        }
      }

      .dark-section & {
        @apply text-zinc-100;

        & .button-arrow-icon {
          @apply text-zinc-300;
        }

        & .button-custom-icon {
          @apply text-blue-500;
        }

        &:hover .button-custom-icon,
        &:focus .button-custom-icon {
          @apply text-blue-300 transition-colors duration-200;
        }

        &:disabled {
          @apply border-zinc-500 bg-zinc-800 text-zinc-500;
        }
      }
    }

    &.button--ghost {
      @apply border-zinc-100;

      &:hover {
        @apply bg-zinc-200 transition-colors;
      }

      &:focus {
        @apply outline-zinc-400;
      }

      .light-section &,
      .gray-section & {
        @apply bg-zinc-100 text-zinc-900;

        &:not(:disabled):hover,
        &:focus {
          @apply border-zinc-200 bg-zinc-200;
        }

        &:disabled {
          @apply text-zinc-400;
        }

        & .button-arrow-icon {
          @apply text-zinc-500;
        }
      }

      .dark-section & {
        @apply border-zinc-800 bg-zinc-800 text-zinc-50;

        &:not(:disabled):hover,
        &:focus {
          @apply border-zinc-600 bg-zinc-600;
        }

        &:disabled {
          @apply bg-zinc-800 text-zinc-500;
        }
      }
    }
  }

  /* CARD */
  .card-image {
    @apply border border-zinc-200;
  }

  /* COPY TO CLIPBOARD */
  .copy-to-clipboard-button {
    @apply icon-button;
  }

  /* CTALINK */
  .cta-link {
    @apply text-zinc-600;
  }

  /* NAVIGATION */
  .navigation-link {
    .light-section & {
      @apply text-zinc-600;
    }

    .dark-section & {
      @apply text-zinc-300;
    }
  }

  /* NOTIFICATION DIALOG */
  .notification {
    @apply rounded-lg border border-zinc-200 bg-zinc-50 text-zinc-950;

    .notification-close-button {
      @apply rounded-md text-zinc-700 hover:text-zinc-950;
    }
  }

  /* SECTION SUB CONTENT */
  .section-sub-content-heading-text {
    .light-section &,
    .gray-section & {
      @apply text-zinc-950;
    }

    .dark-section & {
      @apply text-zinc-50;
    }
  }

  .section-sub-content-description-text {
    .light-section &,
    .gray-section & {
      @apply text-zinc-600;
    }

    .dark-section & {
      @apply text-zinc-400;
    }
  }

  /* SECTION DIVIDER */
  .section-divider {
    .light-section &,
    .gray-section & {
      @apply border-zinc-200;
    }

    .dark-section & {
      @apply border-zinc-400/20;
    }
  }

  /* SHARE ARTICLE */
  .share-article-list {
    @apply gap-2;
  }

  /* SOCIAL LINKS */
  .social-link {
    @apply icon-button;
  }

  /* SLIDE OVER */
  .slide-over-panel {
    @apply bg-white;
  }

  /* SIDE BY SIDE CONTENT */
  .split-section-heading-text {
    .dark-section & {
      @apply text-zinc-200;
    }

    .light-section &,
    .gray-section & {
      @apply text-zinc-600;
    }
  }

  .split-section-description-text {
    @apply text-lg leading-7 text-balance;

    .dark-section & {
      @apply text-zinc-50;
    }

    .light-section &,
    .gray-section & {
      @apply text-zinc-950;
    }
  }

  /* LOGO SECTION */
  .logo-section-logo-item {
    @apply text-zinc-400 transition-colors duration-200;

    .dark-section & {
      @apply hover:text-zinc-50;
    }

    .light-section &,
    .gray-section & {
      @apply hover:opacity-80;
    }
  }

  /* TEXT LINKS */
  .text-link {
    @apply focus:brand-outline font-normal no-underline hover:underline;

    .light-section &,
    .gray-section & {
      @apply text-brand-800;
    }

    .dark-section & {
      @apply text-zinc-400;
    }
  }

  /* TOOLTIP */
  .tooltip {
    @apply rounded-xl border border-zinc-200 bg-white text-zinc-950 shadow-sm;

    .tooltip-arrow {
      @apply fill-zinc-300;
    }
  }
}
