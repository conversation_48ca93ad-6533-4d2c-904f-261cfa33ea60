import type { SimpleCardData } from '@/components/SimpleCard'

export const blockExplorers: Array<SimpleCardData> = [
  {
    title: 'Filfox',
    description:
      'Popular explorer for viewing messages, addresses, and network stats.',
    cta: {
      href: 'https://filfox.info/',
      text: 'Visit Filfox',
    },
  },
  {
    title: 'Beryx',
    description:
      'A real-time explorer built for developers, featuring FEVM insights.',
    cta: {
      href: 'https://beryx.xyz/',
      text: 'Visit Beryx',
    },
  },
  {
    title: 'Blockscout (FEVM)',
    description: 'EVM-compatible explorer to view smart contracts activity.',
    cta: {
      href: 'https://filecoin.blockscout.com/',
      text: 'Visit Blockscout (FEVM)',
    },
  },
  {
    title: 'Filscan',
    description: 'Overview of storage deals, messages, and chain activity.',
    cta: {
      href: 'https://filscan.io/',
      text: 'Visit Filscan',
    },
  },
  {
    title: 'Filutils',
    description:
      'A feature-rich tool for querying actors and accessing onchain data.',
    cta: {
      href: 'https://filutils.com/',
      text: 'Visit Filutils',
    },
  },
]
