import type { SimpleCardData } from '@/components/SimpleCard'

export const filecoinEarningsInsights: Array<SimpleCardData> = [
  {
    title: 'Earning filecoin and fiat',
    description:
      'Providers are compensated for reliable storage via the network’s programmable reward system.',
    cta: {
      href: '/',
      text: 'Learn more',
    },
  },
  {
    title: 'More storage, more rewards',
    description:
      'Growing your storage capacity unlocks greater block rewards, reflecting your increased contribution to the network’s foundation.',
    cta: {
      href: '/',
      text: 'Learn more',
    },
  },
  {
    title: '18 to 24 months',
    description:
      'Most providers recover their hardware investments in under two years, depending on uptime and operational efficiency.',
    cta: {
      href: '/',
      text: 'Learn more',
    },
  },
]
