---
title: Filecoin News 110
draft: false
excerpt: "\U0001F680 Filecoin & Avalanche launch a cross-chain data bridge via FVM—offload data with cryptographic proofs, lower costs, and unlock multi-chain scale."
share_image: /assets/images/FilecoinNewsHeader.webp
image:
  url: /assets/images/FilecoinNewsHeader.webp
author: <PERSON>
publishedOn: 2025-05-27T04:00:00.000Z
categories:
  - updates
  - events
dim_image: true
---

## ✨ Highlights ✨

### 🚀 Avalanche x Filecoin Data Bridge Unlocks Cross-Chain Value

Filecoin Foundation and Ava Labs have launched a native cross-chain data bridge between Avalanche C-Chain and Filecoin via the Filecoin Virtual Machine (FVM). This powerful integration combines Avalanche’s high-speed execution with Filecoin’s verifiable storage layer — enabling modular, enterprise-grade infrastructure.

Built by FIL-B and FilOz, the prototype allows data generated on Avalanche to be offloaded to Filecoin, with CID-based proofs verifying integrity and access onchain. It’s a builder-friendly solution for scalable, trustless storage that doesn’t compromise performance.

Why it matters:

- Verifiable & composable: Offload data without leaving Avalanche, with cryptographic proof via Filecoin.
- Compliance-grade retention: Store KYC records, logs, and more — without L1 bloat.
- Optimized for cost & scale: Reduce storage costs while unlocking new demand for Filecoin providers.

[Check out the full documentation and demo](https://github.com/FIL-Builders/dataBridgeDemo). 

## 🌎 Community Updates 🌎

### 🔥 Storacha Migration Tool: Break Free from S3

Storacha just dropped a TypeScript-based tool that lets you migrate files and directories from AWS S3 to decentralized storage—no downloads needed. With real-time progress tracking, batch processing, and verifiable CIDs, it’s the fast, dev-friendly way to future-proof your data infrastructure. Built for performance, privacy, and control. Check out the [GitHub repo](https://github.com/HarshS1611/storacha-migration-tool) to get started. 

### 🛠️ Protocol Labs Launches PL Genesis: Modular Worlds Hackathon

Protocol Labs has unveiled [PL Genesis: Modular Worlds](https://www.plgenesis.com/), a global hackathon designed to reimagine the foundational layers of the internet. With $200,000 in prizes, mentorship from ecosystem leaders, and a shot at joining the Founders Forge accelerator, this is an open invitation to prototype the future.

Builders across the globe are invited to develop resilient infrastructure, explore novel cryptoeconomic primitives, and ground AI in verifiable truth. Partners include Near Protocol, WeatherXM, Filecoin Foundation, Checker Network, and more. [Join the hackathon](https://www.plgenesis.com/) and help shape the next era of human coordination.

### 🎉 Akave Turns 1: A Year of Building the Future of Data Infra

In just one year, [Akave](http://akave.ai)—a unified data layer that brings encryption and Filecoin integration to enterprises, DePIN, and AI—has raised $3.45M, launched the Yucca testnet, and delivered the first-ever decentralized storage integration with Snowflake. With 29K+ signups, 55+ projects onboarded, and paid customers like Intuizi and Heurist, Akave is powering a new wave of data innovation. Happy birthday Akave!

## 🎉 Events 🎉

### 🐶 Meme-a-thon at ETHPrague: Code, Corgis & Comedy

On Friday, May 30th, join Filecoin Code N Corgi for a one-of-a-kind Meme-a-thon just steps from the ETHGlobal Prague venue! This lighthearted gathering brings together Filecoin builders, devs, and meme lovers to celebrate the ecosystem through humor — think dev jokes, educational comics, and community inside references. Expect lightning talks, plushie corgis (yes, Prague editions 🧸), and a showcase of the spiciest Filecoin memes with prizes on the line. Food, drinks, and good vibes included. [Reserve your spot](https://lu.ma/vyqhbucj)! 

### 📡 FIL Dev Summit 6 Virtual Kickoff: Recap & Recordings

The FIL Dev Summit 6 (FDS-6) Virtual Kickoff Sessions set the stage for a pivotal year in the Filecoin ecosystem. Hosted by FilOz and Filecoin Foundation, this five-day virtual series offered deep dives into network growth, storage adoption, technical innovation, and community governance—leading into the in-person event in Toronto that occurred on May 12–13.

Highlights across the week included:

- Day 1: Filecoin State of the Union — A look at market shifts, network metrics, Proof of Data Possession, and Filecoin’s role in AI and Web3 infra.
- Day 2: Storage Adoption — Winning strategies for client onboarding, ecosystem coordination, and enterprise-grade readiness.
- Day 3: Onchain Tech Stack — PDP, FWS, stablecoin payments, and composable storage services powering a smart data economy.
- Day 4: Tools & Builders — UX advances, hackathons, and dev enablement initiatives like F3 APIs and FIL Frame.
- Day 5: Value Accrual & Funding — A focus on real usage, token utility, and stable value flows like USDFC and RAMO.
- Day 6: Governance & Community — New voting tools, improved onboarding paths, and calls for broader participation in Filecoin governance.

[Watch the full session recordings](https://www.youtube.com/@FilecoinProject/playlists)!
