{"extends": "@filecoin-foundation/typescript-config/base.json", "compilerOptions": {"baseUrl": ".", "noEmit": true, "jsx": "preserve", "paths": {"@/*": ["./src/app/*"], "@/assets/*": ["./public/assets/*"], "@/components/*": ["./src/app/_components/*"], "@/constants/*": ["./src/app/_constants/*"], "@/data/*": ["./src/app/_data/*"], "@/hooks/*": ["./src/app/_hooks/*"], "@/styles/*": ["./src/app/_styles/*"], "@/utils/*": ["./src/app/_utils/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx", "cypress/**/*.ts"], "exclude": ["node_modules"]}