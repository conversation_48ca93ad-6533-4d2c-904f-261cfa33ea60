{"name": "uxit", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "unlighthouse": "tsx ./scripts/generateSiteAuditReports.ts"}, "dependencies": {"@filecoin-foundation/ui": "0.0.0", "@unlighthouse/cli": "^0.17.1", "@unlighthouse/core": "^0.17.1", "next": "^15.3.4", "puppeteer": "^24.11.2", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@filecoin-foundation/eslint-config": "0.0.0", "@filecoin-foundation/typescript-config": "0.0.0", "@types/node": "^24.0.8", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "postcss": "^8.5.6", "tsx": "^4.20.3", "typescript": "^5.8.3"}}