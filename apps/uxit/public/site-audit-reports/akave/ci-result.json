[{"path": "/", "score": 0.96, "performance": 0.92, "accessibility": 0.96, "best-practices": 0.96, "seo": 1}, {"path": "/about-us", "score": 0.91, "performance": 0.66, "accessibility": 0.96, "best-practices": 1, "seo": 1}, {"path": "/akave-cloud", "score": 0.96, "performance": 0.95, "accessibility": 0.96, "best-practices": 1, "seo": 0.92}, {"path": "/akavecloud-contact", "score": 0.96, "performance": 0.87, "accessibility": 0.96, "best-practices": 1, "seo": 1}, {"path": "/blog", "score": 0.97, "performance": 0.92, "accessibility": 0.95, "best-practices": 1, "seo": 1}, {"path": "/blog/announcing-the-first-decentralized-storage-integration-with-snowflake", "score": 0.96, "performance": 0.87, "accessibility": 0.97, "best-practices": 1, "seo": 1}, {"path": "/ecosystem", "score": 0.9, "performance": 0.72, "accessibility": 0.96, "best-practices": 1, "seo": 0.92}, {"path": "/ecosystem/xtrace", "score": 0.9, "performance": 0.74, "accessibility": 0.95, "best-practices": 1, "seo": 0.92}, {"path": "/enterprise/datadrop", "score": 0.95, "performance": 0.9, "accessibility": 0.91, "best-practices": 1, "seo": 1}, {"path": "/test", "score": 0.92, "performance": 0.92, "accessibility": 0.85, "best-practices": 1, "seo": 0.91}, {"path": "/testnet", "score": 0.95, "performance": 0.78, "accessibility": 1, "best-practices": 1, "seo": 1}]