window.__unlighthouse_payload = {"reports":[{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"6666cd","url":"https://fil.org","$url":"https://fil.org/","path":"/","definition":{"name":"_index","path":"/"}},"reportId":"6666cd","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports","tasksTime":{"inspectHtmlTask":1746541996732,"runLighthouseTask":1746542000405},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation | Decentralized Storage Solutions","description":"Explore Filecoin Foundation's mission to preserve humanity’s most important information.","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Explore Filecoin Foundation's mission to preserve humanity’s most important information.","title":"Filecoin Foundation | Decentralized Storage Solutions"},"internalLinks":31,"externalLinks":9,"htmlSize":145746},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:33:51.753Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,160 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,0,DIV,1,DIV,0,HEADER,0,DIV,0,DIV,1,P","selector":"header.grid > div.grid > div.flex > p","boundingRect":{"top":320,"bottom":536,"left":24,"right":388,"width":364,"height":216},"snippet":"<p class=\"\">","nodeLabel":"Filecoin is the world’s largest decentralized storage network. Filecoin Foundat…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":645.308,"percent":"30%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1514.0338000000002,"percent":"70%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.95,"scoreDisplayMode":"numeric","numericValue":2159.3418,"numericUnit":"millisecond","displayValue":"2.2 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.89,"scoreDisplayMode":"numeric","numericValue":1800.618,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":49,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2195.21418,"numericUnit":"millisecond","displayValue":"2.2 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181611084279,"data":"reports/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181611459279,"data":"reports/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181611834279,"data":"reports/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181612209279,"data":"reports/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181612584279,"data":"reports/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181612959279,"data":"reports/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181613334279,"data":"reports/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181613709279,"data":"reports/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1800.618,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8c7676c.webp&w=750&q=100","node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,0,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":700,"bottom":905,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A grand library with modern server racks and a large network of spheres ab…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8c7676c.webp&amp;w=6…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8…\">","nodeLabel":"A grand library with modern server racks and a large network of spheres above, …"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,0,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":700,"bottom":905,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A grand library with modern server racks and a large network of spheres ab…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8c7676c.webp&amp;w=6…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8…\">","nodeLabel":"A grand library with modern server racks and a large network of spheres above, …"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Home.f8c7676c.webp&w=750&q=100","totalBytes":153940,"wastedBytes":46265,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.97}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"46b393","url":"https://fil.org/about","$url":"https://fil.org/about","path":"/about","definition":{"name":"about","path":"/about"}},"reportId":"46b393","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/about","tasksTime":{"inspectHtmlTask":1746541996985,"runLighthouseTask":1746542000668},"seo":{"favicon":"/favicon.ico","title":"About Filecoin Foundation – Mission & Values","description":"Filecoin Foundation facilitates the open source governance of the Filecoin network, funds research and development projects for decentralized technologies, and supports the growth of the Filecoin community and ecosystem.","og":{"image":"https://fil.org/_next/static/media/Filorg_About.8830eb97.webp","description":"Filecoin Foundation facilitates the open source governance of the Filecoin network, funds research and development projects for decentralized technologies, and supports the growth of the Filecoin community and ecosystem.","title":"About Filecoin Foundation – Mission & Values"},"internalLinks":20,"externalLinks":22,"htmlSize":178449},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:24.931Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,070 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,0,DIV,1,P","selector":"header.grid > div.grid > div.flex > p","boundingRect":{"top":224,"bottom":440,"left":24,"right":388,"width":364,"height":216},"snippet":"<p class=\"\">","nodeLabel":"Filecoin is the world’s largest decentralized storage network. Filecoin Foundat…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":625.776,"percent":"30%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1441.4144999999999,"percent":"70%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.96,"scoreDisplayMode":"numeric","numericValue":2067.1904999999997,"numericUnit":"millisecond","displayValue":"2.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1769.1719999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":31,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2092.06785,"numericUnit":"millisecond","displayValue":"2.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181584905817,"data":"reports/about/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181585280817,"data":"reports/about/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181585655817,"data":"reports/about/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181586030817,"data":"reports/about/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181586405817,"data":"reports/about/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181586780817,"data":"reports/about/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181587155817,"data":"reports/about/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181587530817,"data":"reports/about/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1769.1719999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8830eb97.webp&w=750&q=100","node":{"type":"node","lhId":"1-51-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":538,"bottom":743,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A digital rendering of the Earth with interconnected points, highlighting …\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8830eb97.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8…\">","nodeLabel":"A digital rendering of the Earth with interconnected points, highlighting globa…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-51-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":538,"bottom":743,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A digital rendering of the Earth with interconnected points, highlighting …\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8830eb97.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8…\">","nodeLabel":"A digital rendering of the Earth with interconnected points, highlighting globa…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_About.8830eb97.webp&w=750&q=100","totalBytes":78046,"wastedBytes":23456,"wastedPercent":30.0541181041181},{"node":{"type":"node","lhId":"1-53-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,3,SECTION,2,DIV,0,UL,1,LI,0,IMG","selector":"div.flex > ul.grid > li.rounded-lg > img.aspect-video","boundingRect":{"top":1489,"bottom":1688,"left":29,"right":383,"width":354,"height":199},"snippet":"<img alt=\"A high-tech R&amp;D lab with workstations and a large spherical network struct…\" loading=\"lazy\" width=\"1792\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-video w-full rounded-sm object-cover sm:w-60 sm:shrink-0 md:w-80 lg…\" style=\"color:transparent\" sizes=\"(min-width: 640px) 250px, (min-width: 768px) 330px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_RandD.7eaa2049.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_RandD.7…\">","nodeLabel":"A high-tech R&D lab with workstations and a large spherical network structure a…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_RandD.7eaa2049.webp&w=750&q=75","totalBytes":47188,"wastedBytes":15547,"wastedPercent":32.947668997669},{"node":{"type":"node","lhId":"1-52-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,3,SECTION,2,DIV,0,UL,0,LI,0,IMG","selector":"div.flex > ul.grid > li.rounded-lg > img.aspect-video","boundingRect":{"top":1108,"bottom":1307,"left":29,"right":383,"width":354,"height":199},"snippet":"<img alt=\"Interconnected network of blue and white spheres with icons of people, rep…\" loading=\"lazy\" width=\"1792\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-video w-full rounded-sm object-cover sm:w-60 sm:shrink-0 md:w-80 lg…\" style=\"color:transparent\" sizes=\"(min-width: 640px) 250px, (min-width: 768px) 330px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance1.18335b7a.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Interconnected network of blue and white spheres with icons of people, represen…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance1.18335b7a.webp&w=750&q=75","totalBytes":42958,"wastedBytes":14154,"wastedPercent":32.947668997669}]},"displayValue":3,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"126ac9","url":"https://fil.org/blog","$url":"https://fil.org/blog","path":"/blog","definition":{"name":"blog","path":"/blog"}},"reportId":"126ac9","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/blog","tasksTime":{"inspectHtmlTask":1746541997219,"runLighthouseTask":1746542000693},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Blog | Ecosystem Updates & Announcements","description":"Read the latest updates, insights, and announcements from the Filecoin ecosystem and Filecoin Foundation. Stay informed about the future of decentralized storage technologies.","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Read the latest updates, insights, and announcements from the Filecoin ecosystem and Filecoin Foundation. Stay informed about the future of decentralized storage technologies.","title":"Filecoin Foundation Blog | Ecosystem Updates & Announcements"},"internalLinks":18,"externalLinks":6,"htmlSize":1465885},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.78},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.94},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:53.677Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"4,070 ms","metricSavings":{"LCP":1550},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,1,HEADER,0,DIV,1,DIV,0,IMG","selector":"header.grid > div.grid > div.relative > img.page-header-image","boundingRect":{"top":622,"bottom":827,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"page-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ffilecoinai.webp&amp;w=640&amp;q=100 640w, /_…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffilecoinai.webp&amp;w=750…\">","nodeLabel":"header.grid > div.grid > div.relative > img.page-header-image"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":653.1045,"percent":"16%"},{"phase":"Load Delay","timing":1537.************,"percent":"38%"},{"phase":"Load Time","timing":1116.8930686311937,"percent":"27%"},{"phase":"Render Delay","timing":757.7101852228634,"percent":"19%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.48,"scoreDisplayMode":"numeric","numericValue":4065.1205,"numericUnit":"millisecond","displayValue":"4.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.26,"scoreDisplayMode":"numeric","numericValue":3855.4065,"numericUnit":"millisecond","displayValue":"3.9 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":41,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.87,"scoreDisplayMode":"numeric","numericValue":4086.0919000000004,"numericUnit":"millisecond","displayValue":"4.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181612989231,"data":"reports/blog/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181613364231,"data":"reports/blog/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181613739231,"data":"reports/blog/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181614114231,"data":"reports/blog/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181614489231,"data":"reports/blog/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181614864231,"data":"reports/blog/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181615239231,"data":"reports/blog/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181615614231,"data":"reports/blog/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.83,"scoreDisplayMode":"numeric","numericValue":3855.4065,"numericUnit":"millisecond","displayValue":"3.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-58-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,DIV,1,DIV,2,DIV,0,SECTION,0,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1288,"bottom":1491,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image object-cover h-full w-full\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2F042425-pdp_blogheader.webp&amp;w=640&amp;q=1…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2F042425-pdp_blogheader…\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2F042425-pdp_blogheader.webp&w=750&q=100","totalBytes":125468,"wastedBytes":39514,"wastedPercent":31.**************},{"node":{"type":"node","lhId":"1-57-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,1,HEADER,0,DIV,1,DIV,0,IMG","selector":"header.grid > div.grid > div.relative > img.page-header-image","boundingRect":{"top":622,"bottom":827,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"page-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ffilecoinai.webp&amp;w=640&amp;q=100 640w, /_…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffilecoinai.webp&amp;w=750…\">","nodeLabel":"header.grid > div.grid > div.relative > img.page-header-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffilecoinai.webp&w=750&q=100","totalBytes":72628,"wastedBytes":20985,"wastedPercent":28.89387835703001},{"node":{"type":"node","lhId":"1-59-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,DIV,1,DIV,2,DIV,0,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1807,"bottom":2011,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image object-cover h-full w-full\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2F0215-ff-24-.webp&amp;w=640&amp;q=100 640w, /…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2F0215-ff-24-.webp&amp;w=75…\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2F0215-ff-24-.webp&w=750&q=100","totalBytes":23264,"wastedBytes":13196,"wastedPercent":56.72155688622755}]},"displayValue":3,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.93}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"f34fc6","url":"https://fil.org/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science","$url":"https://fil.org/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science","path":"/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science","definition":{"name":"blog-slug","path":"/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science"}},"reportId":"f34fc6","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science","tasksTime":{"inspectHtmlTask":1746541997220,"runLighthouseTask":1746542000986},"seo":{"favicon":"/favicon.ico","title":"Case Study: DeSci Labs and Filecoin: Enabling a Future of Open Science | Filecoin Foundation","description":"Explore how DeSci Labs uses Filecoin to enable a future of open science. A detailed case study.","og":{"image":"https://fil.org/assets/images/0719-cs-desci.png","description":"Explore how DeSci Labs uses Filecoin to enable a future of open science. A detailed case study.","title":"Case Study: DeSci Labs and Filecoin: Enabling a Future of Open Science | Filecoin Foundation"},"internalLinks":19,"externalLinks":14,"htmlSize":104458},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.96},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:37.232Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,830 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image","boundingRect":{"top":442,"bottom":647,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"article-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 768px) 680px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2F0719-cs-desci.png&amp;w=640&amp;q=100 640w, …\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2F0719-cs-desci.png&amp;w=7…\">","nodeLabel":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":659.741,"percent":"36%"},{"phase":"Load Delay","timing":424.6331671288958,"percent":"23%"},{"phase":"Load Time","timing":503.75644230149555,"percent":"27%"},{"phase":"Render Delay","timing":245.10539056960852,"percent":"13%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1833.2359999999999,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.89,"scoreDisplayMode":"numeric","numericValue":1833.2359999999999,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":1.5,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":53,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.97,"scoreDisplayMode":"numeric","numericValue":2679.0910000000003,"numericUnit":"millisecond","displayValue":"2.7 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181598086533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181598461533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181598836533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181599211533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181599586533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181599961533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181600336533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181600711533,"data":"reports/blog/case-study-desci-labs-and-filecoin-enabling-a-future-of-open-science/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1833.2359999999999,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"c10f77","url":"https://fil.org/digest","$url":"https://fil.org/digest","path":"/digest","definition":{"name":"digest","path":"/digest"}},"reportId":"c10f77","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/digest","tasksTime":{"inspectHtmlTask":1746541997677,"runLighthouseTask":1746542000667},"seo":{"favicon":"/favicon.ico","title":"Filecoin Ecosystem Digest - Innovations in Decentralized Storage | Filecoin Foundation","description":"Explore how decentralized storage is shaping a better web through expert articles and community insights in our Filecoin Ecosystem Digest","og":{"image":"https://fil.org/_next/static/media/Filorg_Digest_Cover_Issue_1.2a2fa6d1.webp","description":"Explore how decentralized storage is shaping a better web through expert articles and community insights in our Filecoin Ecosystem Digest","title":"Filecoin Ecosystem Digest - Innovations in Decentralized Storage | Filecoin Foundation"},"internalLinks":24,"externalLinks":7,"htmlSize":213977},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:24.586Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,190 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":576,"bottom":781,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Cover of the Filecoin Ecosystem Digest showing a stylized globe with inter…\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_Cover_Issue_1.2…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_…\">","nodeLabel":"Cover of the Filecoin Ecosystem Digest showing a stylized globe with interconne…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":653.5255,"percent":"30%"},{"phase":"Load Delay","timing":518.2003231405347,"percent":"24%"},{"phase":"Load Time","timing":655.5690043882423,"percent":"30%"},{"phase":"Render Delay","timing":362.2546724712231,"percent":"17%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.95,"scoreDisplayMode":"numeric","numericValue":2189.5495,"numericUnit":"millisecond","displayValue":"2.2 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1745.5335,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":38,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2189.5495,"numericUnit":"millisecond","displayValue":"2.2 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181584611407,"data":"reports/digest/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181584986407,"data":"reports/digest/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181585361407,"data":"reports/digest/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181585736407,"data":"reports/digest/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181586111407,"data":"reports/digest/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181586486407,"data":"reports/digest/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181586861407,"data":"reports/digest/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181587236407,"data":"reports/digest/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1745.5335,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_Cover_Issue_1.2a2fa6d1.webp&w=750&q=100","node":{"type":"node","lhId":"1-40-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":576,"bottom":781,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Cover of the Filecoin Ecosystem Digest showing a stylized globe with inter…\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_Cover_Issue_1.2…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_…\">","nodeLabel":"Cover of the Filecoin Ecosystem Digest showing a stylized globe with interconne…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false},{"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest-editor.webp&w=750&q=100","node":{"type":"node","lhId":"1-41-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,SECTION,0,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1250,"bottom":1453,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest-editor.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"displayedAspectRatio":"354 x 200\n        (1.77)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false},{"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-start.webp&w=750&q=100","node":{"type":"node","lhId":"1-42-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1785,"bottom":1989,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-start.webp&amp;w=640…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-s…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"displayedAspectRatio":"354 x 200\n        (1.77)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-41-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,SECTION,0,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1250,"bottom":1453,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest-editor.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fletter-from-the-guest-editor.webp&w=750&q=100","totalBytes":146262,"wastedBytes":47697,"wastedPercent":32.61072261072261},{"node":{"type":"node","lhId":"1-42-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1785,"bottom":1989,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 470px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-start.webp&amp;w=640…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-s…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Fstorage-is-just-the-start.webp&w=750&q=100","totalBytes":94208,"wastedBytes":30722,"wastedPercent":32.61072261072261},{"node":{"type":"node","lhId":"1-40-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":576,"bottom":781,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Cover of the Filecoin Ecosystem Digest showing a stylized globe with inter…\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_Cover_Issue_1.2…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_…\">","nodeLabel":"Cover of the Filecoin Ecosystem Digest showing a stylized globe with interconne…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Digest_Cover_Issue_1.2a2fa6d1.webp&w=750&q=100","totalBytes":57054,"wastedBytes":17147,"wastedPercent":30.0541181041181}]},"displayValue":3,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"fb637d","url":"https://fil.org/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems","$url":"https://fil.org/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems","path":"/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems","definition":{"name":"digest-slug","path":"/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems"}},"reportId":"fb637d","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems","tasksTime":{"inspectHtmlTask":1746541997721,"runLighthouseTask":1746542001105},"seo":{"favicon":"/favicon.ico","title":"From Participant to Organizer: How Community-Led Activities Drive Innovation in Decentralized Systems | Filecoin Foundation","description":"Explore how community-led events drive innovation in decentralized ecosystems. Learn strategies for engagement, regional impact, and the transformative power of collaboration.","og":{"image":"https://fil.org/assets/images/from-participant-to-organizer.webp","description":"Explore how community-led events drive innovation in decentralized ecosystems. Learn strategies for engagement, regional impact, and the transformative power of collaboration.","title":"From Participant to Organizer: How Community-Led Activities Drive Innovation in Decentralized Systems | Filecoin Foundation"},"internalLinks":18,"externalLinks":11,"htmlSize":110555},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.86},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:25.526Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"4,030 ms","metricSavings":{"LCP":1550},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-H1","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,H1","selector":"div.m-auto > header.space-y-6 > div.space-y-6 > h1.text-balance","boundingRect":{"top":210,"bottom":490,"left":24,"right":388,"width":364,"height":280},"snippet":"<h1 class=\"text-balance text-4xl font-bold\">","nodeLabel":"From Participant to Organizer: How Community-Led Activities Drive Innovation in…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":655.6405,"percent":"16%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":3374.9999999999995,"percent":"84%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.49,"scoreDisplayMode":"numeric","numericValue":4030.6404999999995,"numericUnit":"millisecond","displayValue":"4.0 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.88,"scoreDisplayMode":"numeric","numericValue":1846.9365,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":5,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":55,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.87,"scoreDisplayMode":"numeric","numericValue":4068.1404999999995,"numericUnit":"millisecond","displayValue":"4.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181586038612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181586413612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181586788612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181587163612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181587538612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181587913612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181588288612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181588663612,"data":"reports/digest/from-participant-to-organizer-how-community-led-activities-drive-innovation-in-decentralized-systems/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2281.7889919201716,"numericUnit":"millisecond","displayValue":"2.3 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-38-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image","boundingRect":{"top":584,"bottom":789,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"article-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 768px) 680px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ffrom-participant-to-organizer.webp&amp;w…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffrom-participant-to-o…\">","nodeLabel":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffrom-participant-to-organizer.webp&w=750&q=100","totalBytes":141454,"wastedBytes":40986,"wastedPercent":28.974747474747474}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.97}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"25b4e7","url":"https://fil.org/ecosystem-explorer","$url":"https://fil.org/ecosystem-explorer","path":"/ecosystem-explorer","definition":{"name":"ecosystem-explorer","path":"/ecosystem-explorer"}},"reportId":"25b4e7","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/ecosystem-explorer","tasksTime":{"inspectHtmlTask":1746541997856,"runLighthouseTask":1746542002048},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Ecosystem – Explore our Projects","description":"Discover the diverse landscape of projects that make up the Filecoin ecosystem. Filter Filecoin projects by finance, storage, network, and other categories.","og":{"image":"https://fil.org/_next/static/media/Filorg_Ecosystem_Explorer.31fa40e1.webp","description":"Discover the diverse landscape of projects that make up the Filecoin ecosystem. Filter Filecoin projects by finance, storage, network, and other categories.","title":"Filecoin Foundation Ecosystem – Explore our Projects"},"internalLinks":39,"externalLinks":6,"htmlSize":224940},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.94},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.93},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:33:41.725Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,790 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":0,"bottom":0,"left":0,"right":0,"width":0,"height":0},"snippet":"<img alt=\"Magnifying glass focusing on a network of interconnected blue and white sp…\" width=\"1800\" height=\"1029\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosystem_Explorer.31f…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosyst…\">","nodeLabel":"Magnifying glass focusing on a network of interconnected blue and white spheres…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":656.582,"percent":"37%"},{"phase":"Load Delay","timing":745.5418739125781,"percent":"42%"},{"phase":"Load Time","timing":248.2403406855094,"percent":"14%"},{"phase":"Render Delay","timing":142.11328540191244,"percent":"8%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1792.4775,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1792.4775,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":13.5,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":77,"numericUnit":"millisecond","displayValue":"80 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.96,"scoreDisplayMode":"numeric","numericValue":2952.1611900000007,"numericUnit":"millisecond","displayValue":"3.0 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181601049905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181601424905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181601799905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181602174905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181602549905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181602924905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181603299905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181603674905,"data":"reports/ecosystem-explorer/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2108.8795002332504,"numericUnit":"millisecond","displayValue":"2.1 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"exception","description":"Error: Minified React error #418; visit https://react.dev/errors/418?args[]=HTML&args[]= for the full message or use the non-minified dev environment for full errors and additional helpful warnings.\n    at rP (https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:34101)\n    at oU (https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:83629)\n    at ic (https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:112388)\n    at https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:108436\n    at u5 (https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:108537)\n    at iH (https://fil.org/_next/static/chunks/87c73c54-939c697572df3c5f.js:1:129981)\n    at MessagePort.x (https://fil.org/_next/static/chunks/3055-79bb28df9265dfe3.js:14:35169)","sourceLocation":{"type":"source-location","url":"https://fil.org/_next/static/chunks/3055-79bb28df9265dfe3.js","urlProvider":"network","line":3,"column":24172}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosystem_Explorer.31fa40e1.webp&w=750&q=100","node":{"type":"node","lhId":"1-81-IMG","path":"1,HTML,1,BODY,14,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":530,"bottom":735,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Magnifying glass focusing on a network of interconnected blue and white sp…\" width=\"1800\" height=\"1029\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosystem_Explorer.31f…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosyst…\" style=\"color: transparent;\">","nodeLabel":"Magnifying glass focusing on a network of interconnected blue and white spheres…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-83-IMG","path":"1,HTML,1,BODY,14,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,DIV,1,DIV,2,DIV,0,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1707,"bottom":1911,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image object-contain card-image-spacing h-full w-full\" sizes=\"(min-width: 640px) 320px, (min-width: 768px) 440px, (min-width: 1024px) 28…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Facaisia-logo_white.png&amp;w=640&amp;q=100 6…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Facaisia-logo_white.pn…\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Facaisia-logo_white.png&w=750&q=100","totalBytes":23854,"wastedBytes":15142,"wastedPercent":63.**************},{"node":{"type":"node","lhId":"1-81-IMG","path":"1,HTML,1,BODY,14,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":530,"bottom":735,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Magnifying glass focusing on a network of interconnected blue and white sp…\" width=\"1800\" height=\"1029\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosystem_Explorer.31f…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosyst…\" style=\"color: transparent;\">","nodeLabel":"Magnifying glass focusing on a network of interconnected blue and white spheres…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Ecosystem_Explorer.31fa40e1.webp&w=750&q=100","totalBytes":44524,"wastedBytes":13381,"wastedPercent":30.0541181041181}]},"displayValue":2,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.94}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"53773a","url":"https://fil.org/ecosystem-explorer/democracys-library","$url":"https://fil.org/ecosystem-explorer/democracys-library","path":"/ecosystem-explorer/democracys-library","definition":{"name":"ecosystem-explorer-slug","path":"/ecosystem-explorer/democracys-library"}},"reportId":"53773a","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/ecosystem-explorer/democracys-library","tasksTime":{"inspectHtmlTask":1746541997896,"runLighthouseTask":1746542001667},"seo":{"favicon":"/favicon.ico","title":"Democracy's Library - Filecoin Ecosystem Project | Filecoin Foundation","description":"Democracy's Library provides access to public government data using decentralized storage.","og":{"image":"https://fil.org/_next/static/media/Filorg_Ecosystem_Explorer.31fa40e1.webp","description":"Democracy's Library provides access to public government data using decentralized storage.","title":"Democracy's Library - Filecoin Ecosystem Project | Filecoin Foundation"},"internalLinks":19,"externalLinks":13,"htmlSize":96296},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:50.714Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,790 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,ARTICLE,1,DIV,0,DIV,0,DIV,1,DIV,0,P","selector":"div.max-w-readable > div.space-y-6 > div.prose > p","boundingRect":{"top":562,"bottom":870,"left":24,"right":388,"width":364,"height":308},"snippet":"<p>","nodeLabel":"The Internet Archive’s Democracy’s Library project is creating a free, open col…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":645.973,"percent":"36%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1144.5439999999999,"percent":"64%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1790.5169999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1790.5169999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":32,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1790.5169999999998,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181610149951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181610524951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181610899951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181611274951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181611649951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181612024951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181612399951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181612774951,"data":"reports/ecosystem-explorer/democracys-library/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1790.5169999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"53b096","url":"https://fil.org/employee-privacy-policy","$url":"https://fil.org/employee-privacy-policy","path":"/employee-privacy-policy","definition":{"name":"employee-privacy-policy","path":"/employee-privacy-policy"}},"reportId":"53b096","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/employee-privacy-policy","tasksTime":{"inspectHtmlTask":1746541998315,"runLighthouseTask":1746542002049},"seo":{"favicon":"/favicon.ico","title":"Employee and Job Applicant Privacy Policy | Filecoin Foundation","description":"Learn how Filecoin Foundation collects, uses, and discloses personal information for employees and job applicants.","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Learn how Filecoin Foundation collects, uses, and discloses personal information for employees and job applicants.","title":"Employee and Job Applicant Privacy Policy | Filecoin Foundation"},"internalLinks":18,"externalLinks":6,"htmlSize":99294},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.96},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:33:38.708Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,860 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,ARTICLE,2,DIV,1,P","selector":"div.flex > article.prose > div.prose > p","boundingRect":{"top":360,"bottom":668,"left":24,"right":388,"width":364,"height":308},"snippet":"<p>","nodeLabel":"This employee Privacy Policy explains how Filecoin Foundation (collectively “Fi…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":698.693,"percent":"37%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1166.193,"percent":"63%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1864.886,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.88,"scoreDisplayMode":"numeric","numericValue":1864.886,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":49,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1864.886,"numericUnit":"millisecond","displayValue":"1.9 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181598112069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181598487069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181598862069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181599237069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181599612069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181599987069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181600362069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181600737069,"data":"reports/employee-privacy-policy/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1864.886,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"16908b","url":"https://fil.org/events","$url":"https://fil.org/events","path":"/events","definition":{"name":"events","path":"/events"}},"reportId":"16908b","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/events","tasksTime":{"inspectHtmlTask":1746541998889,"runLighthouseTask":1746542042379},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Events – Connect & Collaborate","description":"Explore upcoming Filecoin Foundation, Web3, and community events. Connect with community members worldwide to collaborate and innovate.","og":{"image":"https://fil.org/_next/static/media/Filorg_Events1.839ce70c.webp","description":"Explore upcoming Filecoin Foundation, Web3, and community events. Connect with community members worldwide to collaborate and innovate.","title":"Filecoin Foundation Events – Connect & Collaborate"},"internalLinks":22,"externalLinks":8,"htmlSize":141316},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.94},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:05.166Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,830 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,1,HEADER,0,DIV,1,DIV,0,IMG","selector":"header.grid > div.grid > div.relative > img.page-header-image","boundingRect":{"top":590,"bottom":795,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"page-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ftoronto25.webp&amp;w=640&amp;q=100 640w, /_n…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ftoronto25.webp&amp;w=750&amp;…\">","nodeLabel":"header.grid > div.grid > div.relative > img.page-header-image"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":670.194,"percent":"37%"},{"phase":"Load Delay","timing":791.************,"percent":"43%"},{"phase":"Load Time","timing":208.48640547476452,"percent":"11%"},{"phase":"Render Delay","timing":162.6064430024603,"percent":"9%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1832.5749999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.89,"scoreDisplayMode":"numeric","numericValue":1832.5749999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":46,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1832.5749999999998,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181625018187,"data":"reports/events/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181625393187,"data":"reports/events/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181625768187,"data":"reports/events/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181626143187,"data":"reports/events/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181626518187,"data":"reports/events/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181626893187,"data":"reports/events/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181627268187,"data":"reports/events/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181627643187,"data":"reports/events/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":1981.5645614717846,"numericUnit":"millisecond","displayValue":"2.0 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-46-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,DIV,1,DIV,2,DIV,0,SECTION,0,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1132,"bottom":1335,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image object-cover h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 450px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ffil-euro-summer-webp.webp&amp;w=640&amp;q=10…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffil-euro-summer-webp.…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffil-euro-summer-webp.webp&w=750&q=100","totalBytes":147328,"wastedBytes":66199,"wastedPercent":44.93333333333334},{"node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,DIV,1,DIV,2,DIV,0,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1651,"bottom":1855,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image object-cover h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 350px, (min-width: 768px) 450px, (min-width: 1024px) 36…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Ffil-dev-summit.png&amp;w=640&amp;q=100 640w,…\" src=\"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffil-dev-summit.png&amp;w=…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://fil.org/_next/image?url=%2Fassets%2Fimages%2Ffil-dev-summit.png&w=750&q=100","totalBytes":122878,"wastedBytes":38698,"wastedPercent":31.**************}]},"displayValue":2,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.98}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"597882","url":"https://fil.org/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce","$url":"https://fil.org/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce","path":"/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce","definition":{"name":"events-slug","path":"/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce"}},"reportId":"597882","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce","tasksTime":{"inspectHtmlTask":1746541998613,"runLighthouseTask":1746542040435},"seo":{"favicon":"/favicon.ico","title":"Filecoin Orbit Playground #16 - AI and Data Storage for workforce - Filecoin Event | Filecoin Foundation","description":"Join Filecoin Playground #16 at SCBX Next Tech, Siam Paragon, Bangkok, on Dec 23! Explore AI, decentralized storage, and workforce innovation. Limited seats—register now to network and learn!","og":{"image":"https://fil.org/_next/static/media/Filorg_Events1.839ce70c.webp","description":"Join Filecoin Playground #16 at SCBX Next Tech, Siam Paragon, Bangkok, on Dec 23! Explore AI, decentralized storage, and workforce innovation. Limited seats—register now to network and learn!","title":"Filecoin Orbit Playground #16 - AI and Data Storage for workforce - Filecoin Event | Filecoin Foundation"},"internalLinks":18,"externalLinks":6,"htmlSize":82571},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:28.656Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,990 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,0,DIV,2,P","selector":"header.grid > div.grid > div.flex > p","boundingRect":{"top":434,"bottom":650,"left":24,"right":388,"width":364,"height":216},"snippet":"<p class=\"\">","nodeLabel":"On December 23rd 2024, from 17:00 - 20:00 at Next Tech Zone, 4th floor, Siam Pa…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":652.583,"percent":"33%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1340.4529999999995,"percent":"67%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.97,"scoreDisplayMode":"numeric","numericValue":1993.0359999999996,"numericUnit":"millisecond","displayValue":"2.0 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.94,"scoreDisplayMode":"numeric","numericValue":1618.2332,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":44,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":1993.0359999999996,"numericUnit":"millisecond","displayValue":"2.0 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181648079045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181648454045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181648829045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181649204045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181649579045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181649954045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181650329045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181650704045,"data":"reports/events/filecoin-orbit-playground-16-ai-and-data-storage-for-workforce/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1618.2332,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"39aea1","url":"https://fil.org/filecoin-plus","$url":"https://fil.org/filecoin-plus","path":"/filecoin-plus","definition":{"name":"filecoin-plus","path":"/filecoin-plus"}},"reportId":"39aea1","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/filecoin-plus","tasksTime":{"inspectHtmlTask":1746541998620,"runLighthouseTask":1746542040044},"seo":{"favicon":"/favicon.ico","title":"Filecoin Plus (Fil+) - DataCap Allocation & Securing Deals | Filecoin Foundation","description":"Fil+ aims to increase the amount of useful data stored on the Filecoin network. Learn how to participate. Discover the projects that apply for and receive DataCap.","og":{"image":"https://fil.org/_next/static/media/Filorg_Fil_Plus.c2981e5e.webp","description":"Fil+ aims to increase the amount of useful data stored on the Filecoin network. Learn how to participate. Discover the projects that apply for and receive DataCap.","title":"Filecoin Plus (Fil+) - DataCap Allocation & Securing Deals | Filecoin Foundation"},"internalLinks":18,"externalLinks":12,"htmlSize":103291},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:16.727Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,190 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":394,"bottom":599,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Long row of modern, sleek server racks in a bright, white room, with blue …\" width=\"2130\" height=\"1218\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus.c2981e5e.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"Long row of modern, sleek server racks in a bright, white room, with blue LED l…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":684.9135,"percent":"31%"},{"phase":"Load Delay","timing":307.8864563281629,"percent":"14%"},{"phase":"Load Time","timing":688.2299128082849,"percent":"31%"},{"phase":"Render Delay","timing":505.6902308635522,"percent":"23%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.95,"scoreDisplayMode":"numeric","numericValue":2186.7201,"numericUnit":"millisecond","displayValue":"2.2 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.89,"scoreDisplayMode":"numeric","numericValue":1812.8055,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":39,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2186.7201,"numericUnit":"millisecond","displayValue":"2.2 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181636104570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181636479570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181636854570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181637229570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181637604570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181637979570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181638354570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181638729570,"data":"reports/filecoin-plus/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1812.8055,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus.c2981e5e.webp&w=750&q=100","node":{"type":"node","lhId":"1-37-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":394,"bottom":599,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Long row of modern, sleek server racks in a bright, white room, with blue …\" width=\"2130\" height=\"1218\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus.c2981e5e.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"Long row of modern, sleek server racks in a bright, white room, with blue LED l…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"2130 x 1218\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-37-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":394,"bottom":599,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Long row of modern, sleek server racks in a bright, white room, with blue …\" width=\"2130\" height=\"1218\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus.c2981e5e.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"Long row of modern, sleek server racks in a bright, white room, with blue LED l…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus.c2981e5e.webp&w=750&q=100","totalBytes":42966,"wastedBytes":39239,"wastedPercent":91.32531298904539}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"ed54ba","url":"https://fil.org/filecoin-plus/allocators","$url":"https://fil.org/filecoin-plus/allocators","path":"/filecoin-plus/allocators","definition":{"name":"filecoin-plus-slug","path":"/filecoin-plus/allocators"}},"reportId":"ed54ba","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/filecoin-plus/allocators","tasksTime":{"inspectHtmlTask":1746541998831,"runLighthouseTask":1746542040206},"seo":{"favicon":"/favicon.ico","title":"Filecoin Allocators: Empowering Decentralized Storage & Data Solutions","description":"Allocators play a key role in the Fil+ program by verifying and onboarding real client data. Find the right allocator for your data storage needs!","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Allocators play a key role in the Fil+ program by verifying and onboarding real client data. Find the right allocator for your data storage needs!","title":"Filecoin Allocators: Empowering Decentralized Storage & Data Solutions"},"internalLinks":18,"externalLinks":84,"htmlSize":199003},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.84},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.95},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:03.364Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"4,270 ms","metricSavings":{"LCP":1750},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":464,"bottom":669,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An abstract, minimalist graphic featuring a large folder with a checkmark …\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus_Allocators.c0…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"An abstract, minimalist graphic featuring a large folder with a checkmark symbo…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":690.528,"percent":"16%"},{"phase":"Load Delay","timing":1216.8052193850185,"percent":"28%"},{"phase":"Load Time","timing":1443.6572948334767,"percent":"34%"},{"phase":"Render Delay","timing":920.0330857815052,"percent":"22%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.42,"scoreDisplayMode":"numeric","numericValue":4271.0236,"numericUnit":"millisecond","displayValue":"4.3 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.88,"scoreDisplayMode":"numeric","numericValue":1841.8760000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":33,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.85,"scoreDisplayMode":"numeric","numericValue":4271.0236,"numericUnit":"millisecond","displayValue":"4.3 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181623639257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181624014257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181624389257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181624764257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181625139257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181625514257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181625889257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181626264257,"data":"reports/filecoin-plus/allocators/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1841.8760000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus_Allocators.c017af9d.webp&w=750&q=100","node":{"type":"node","lhId":"1-116-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":464,"bottom":669,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An abstract, minimalist graphic featuring a large folder with a checkmark …\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus_Allocators.c0…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"An abstract, minimalist graphic featuring a large folder with a checkmark symbo…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-116-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":464,"bottom":669,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An abstract, minimalist graphic featuring a large folder with a checkmark …\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus_Allocators.c0…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plu…\">","nodeLabel":"An abstract, minimalist graphic featuring a large folder with a checkmark symbo…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Fil_Plus_Allocators.c017af9d.webp&w=750&q=100","totalBytes":68792,"wastedBytes":20675,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.94}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"dad9f1","url":"https://fil.org/governance","$url":"https://fil.org/governance","path":"/governance","definition":{"name":"governance","path":"/governance"}},"reportId":"dad9f1","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/governance","tasksTime":{"inspectHtmlTask":1746541999320,"runLighthouseTask":1746542048330},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Community-Driven Governance","description":"Our goal is to facilitate a decentralized, innovative, and transparent system to support community decision-making and collective ownership of Filecoin technology.","og":{"image":"https://fil.org/_next/static/media/Filorg_Governance2.f50721d0.webp","description":"Our goal is to facilitate a decentralized, innovative, and transparent system to support community decision-making and collective ownership of Filecoin technology.","title":"Filecoin Foundation Community-Driven Governance"},"internalLinks":17,"externalLinks":10,"htmlSize":91632},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:34:22.686Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,800 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":645.738,"percent":"36%"},{"phase":"Load Delay","timing":354.25273238696536,"percent":"20%"},{"phase":"Load Time","timing":512.8504713120169,"percent":"29%"},{"phase":"Render Delay","timing":284.50579630101765,"percent":"16%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1797.347,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1797.347,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":40,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1797.3470000000002,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181642026958,"data":"reports/governance/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181642401958,"data":"reports/governance/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181642776958,"data":"reports/governance/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181643151958,"data":"reports/governance/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181643526958,"data":"reports/governance/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181643901958,"data":"reports/governance/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181644276958,"data":"reports/governance/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181644651958,"data":"reports/governance/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1797.347,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.webp&w=750&q=100","node":{"type":"node","lhId":"1-35-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-35-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.webp&w=750&q=100","totalBytes":124940,"wastedBytes":37550,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.97}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"519295","url":"https://fil.org/governance/govhub","$url":"https://fil.org/governance/govhub","path":"/governance/govhub","definition":{"name":"governance-slug","path":"/governance/govhub"}},"reportId":"519295","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/governance/govhub","tasksTime":{"inspectHtmlTask":1746541998886,"runLighthouseTask":1746542040618},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Community-Driven Governance","description":"Our goal is to facilitate a decentralized, innovative, and transparent system to support community decision-making and collective ownership of Filecoin technology.","og":{"image":"https://fil.org/_next/static/media/Filorg_Governance2.f50721d0.webp","description":"Our goal is to facilitate a decentralized, innovative, and transparent system to support community decision-making and collective ownership of Filecoin technology.","title":"Filecoin Foundation Community-Driven Governance"},"internalLinks":17,"externalLinks":10,"htmlSize":91632},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.93},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:34:30.514Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":0,"scoreDisplayMode":"metricSavings","numericValue":851.0070000000001,"numericUnit":"millisecond","displayValue":"Potential savings of 850 ms","metricSavings":{"LCP":850,"FCP":850},"details":{"type":"opportunity","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"wastedMs","valueType":"timespanMs","label":"Time Spent"}],"items":[{"url":"https://fil.org/governance/govhub","wastedMs":851.0070000000001},{"url":"https://fil.org/governance","wastedMs":0}],"overallSavingsMs":851.0070000000001},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"3,100 ms","metricSavings":{"LCP":600},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":650.994,"percent":"21%"},{"phase":"Load Delay","timing":1232.0994678780457,"percent":"40%"},{"phase":"Load Time","timing":893.4943151144043,"percent":"29%"},{"phase":"Render Delay","timing":324.4777170075495,"percent":"10%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.75,"scoreDisplayMode":"numeric","numericValue":3101.0654999999997,"numericUnit":"millisecond","displayValue":"3.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1783.0900000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":32,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.95,"scoreDisplayMode":"numeric","numericValue":3109.39905,"numericUnit":"millisecond","displayValue":"3.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181649893186,"data":"reports/governance/govhub/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181650268186,"data":"reports/governance/govhub/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181650643186,"data":"reports/governance/govhub/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181651018186,"data":"reports/governance/govhub/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181651393186,"data":"reports/governance/govhub/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181651768186,"data":"reports/governance/govhub/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181652143186,"data":"reports/governance/govhub/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181652518186,"data":"reports/governance/govhub/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1783.0900000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.webp&w=750&q=100","node":{"type":"node","lhId":"1-35-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-35-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":456,"bottom":661,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Digital illustration of a futuristic bridge construction with cranes and v…\" width=\"1793\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governa…\">","nodeLabel":"Digital illustration of a futuristic bridge construction with cranes and vehicl…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Governance2.f50721d0.webp&w=750&q=100","totalBytes":124940,"wastedBytes":37550,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.95}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"74dc2f","url":"https://fil.org/grants","$url":"https://fil.org/grants","path":"/grants","definition":{"name":"grants","path":"/grants"}},"reportId":"74dc2f","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/grants","tasksTime":{"inspectHtmlTask":1746541999168,"runLighthouseTask":1746542041147},"seo":{"favicon":"/favicon.ico","title":"Filecoin Foundation Grants & Funding Opportunities","description":"Filecoin Foundation awards grant funding for a wide range of projects –– from developer and data tooling and integrations to applications and research.","og":{"image":"https://fil.org/_next/static/media/Filorg_Grants.276497fc.webp","description":"Filecoin Foundation awards grant funding for a wide range of projects –– from developer and data tooling and integrations to applications and research.","title":"Filecoin Foundation Grants & Funding Opportunities"},"internalLinks":23,"externalLinks":14,"htmlSize":145835},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.97},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:34:04.456Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,390 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":562,"bottom":767,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Futuristic garden with blue plants and a waterfall, set in a modern archit…\" width=\"1792\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.276497fc.webp&amp;w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.…\">","nodeLabel":"Futuristic garden with blue plants and a waterfall, set in a modern architectur…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":669.374,"percent":"28%"},{"phase":"Load Delay","timing":531.6132932471685,"percent":"22%"},{"phase":"Load Time","timing":780.1945476428755,"percent":"33%"},{"phase":"Render Delay","timing":410.129759109956,"percent":"17%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.92,"scoreDisplayMode":"numeric","numericValue":2391.3116,"numericUnit":"millisecond","displayValue":"2.4 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.88,"scoreDisplayMode":"numeric","numericValue":1852.6009999999997,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":1.5,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":53,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.96,"scoreDisplayMode":"numeric","numericValue":2984.04496,"numericUnit":"millisecond","displayValue":"3.0 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181623832918,"data":"reports/grants/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181624207918,"data":"reports/grants/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181624582918,"data":"reports/grants/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181624957918,"data":"reports/grants/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181625332918,"data":"reports/grants/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181625707918,"data":"reports/grants/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181626082918,"data":"reports/grants/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181626457918,"data":"reports/grants/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1852.6009999999997,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.276497fc.webp&w=750&q=100","node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":562,"bottom":767,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Futuristic garden with blue plants and a waterfall, set in a modern archit…\" width=\"1792\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.276497fc.webp&amp;w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.…\">","nodeLabel":"Futuristic garden with blue plants and a waterfall, set in a modern architectur…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":562,"bottom":767,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Futuristic garden with blue plants and a waterfall, set in a modern archit…\" width=\"1792\" height=\"1025\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.276497fc.webp&amp;w…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.…\">","nodeLabel":"Futuristic garden with blue plants and a waterfall, set in a modern architectur…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Grants.276497fc.webp&w=750&q=100","totalBytes":108614,"wastedBytes":32643,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.96}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"119d6c","url":"https://fil.org/orbit","$url":"https://fil.org/orbit","path":"/orbit","definition":{"name":"orbit","path":"/orbit"}},"reportId":"119d6c","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/orbit","tasksTime":{"inspectHtmlTask":1746541999238,"runLighthouseTask":1746542078367},"seo":{"favicon":"/favicon.ico","title":"Filecoin Orbit Community Program – Events, Hackathons, Initiatives","description":"Open the doors to a better internet for everyone. Join the Filecoin Orbit community of ambassadors, promoting revolutionary technologies, like Filecoin and IPFS.","og":{"image":"https://fil.org/_next/static/media/Filorg_Orbit.0e4ed3c4.webp","description":"Open the doors to a better internet for everyone. Join the Filecoin Orbit community of ambassadors, promoting revolutionary technologies, like Filecoin and IPFS.","title":"Filecoin Orbit Community Program – Events, Hackathons, Initiatives"},"internalLinks":18,"externalLinks":16,"htmlSize":188740},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.97},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:07.038Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,370 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,0,DIV,2,P","selector":"header.grid > div.grid > div.flex > p","boundingRect":{"top":432,"bottom":600,"left":24,"right":388,"width":364,"height":168},"snippet":"<p class=\"\">","nodeLabel":"Whether you're passionate about hosting events, organizing hackathons, or shari…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":651.215,"percent":"27%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1716.9408000000003,"percent":"73%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.92,"scoreDisplayMode":"numeric","numericValue":2368.1558000000005,"numericUnit":"millisecond","displayValue":"2.4 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1788.1870000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":41,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2368.1558000000005,"numericUnit":"millisecond","displayValue":"2.4 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181686349258,"data":"reports/orbit/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181686724258,"data":"reports/orbit/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181687099258,"data":"reports/orbit/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181687474258,"data":"reports/orbit/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181687849258,"data":"reports/orbit/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181688224258,"data":"reports/orbit/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181688599258,"data":"reports/orbit/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181688974258,"data":"reports/orbit/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1940.3116557482858,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0e4ed3c4.webp&w=750&q=100","node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":764,"bottom":969,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An illustration of a stylized Earth surrounded by numerous smaller spheres…\" width=\"1793\" height=\"1024\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0e4ed3c4.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0…\">","nodeLabel":"An illustration of a stylized Earth surrounded by numerous smaller spheres and …"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 428\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":764,"bottom":969,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An illustration of a stylized Earth surrounded by numerous smaller spheres…\" width=\"1793\" height=\"1024\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0e4ed3c4.webp&amp;w=…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0…\">","nodeLabel":"An illustration of a stylized Earth surrounded by numerous smaller spheres and …"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Orbit.0e4ed3c4.webp&w=750&q=100","totalBytes":127636,"wastedBytes":38151,"wastedPercent":29.890693146417448},{"node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,UL,2,LI,0,IMG","selector":"div.flex > ul.grid > li.rounded-lg > img.aspect-video","boundingRect":{"top":1848,"bottom":2047,"left":29,"right":383,"width":354,"height":199},"snippet":"<img alt=\"Audience seated in a large auditorium, attentively watching a presentation.\" loading=\"lazy\" width=\"2000\" height=\"1333\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-video w-full rounded-sm object-cover sm:w-60 sm:shrink-0 md:w-80 lg…\" style=\"color:transparent\" sizes=\"(min-width: 640px) 250px, (min-width: 768px) 330px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Faudience-seated-large-auditor…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Faudience-seate…\">","nodeLabel":"Audience seated in a large auditorium, attentively watching a presentation."},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Faudience-seated-large-auditorium.11eebcb8.jpg&w=750&q=75","totalBytes":54032,"wastedBytes":22947,"wastedPercent":42.469100000000005},{"node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,2,DIV,0,UL,1,LI,0,IMG","selector":"div.flex > ul.grid > li.rounded-lg > img.aspect-video","boundingRect":{"top":1491,"bottom":1690,"left":29,"right":383,"width":354,"height":199},"snippet":"<img alt=\"Group of five people standing on stage holding certificates of appreciatio…\" loading=\"lazy\" width=\"2000\" height=\"1333\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-video w-full rounded-sm object-cover sm:w-60 sm:shrink-0 md:w-80 lg…\" style=\"color:transparent\" sizes=\"(min-width: 640px) 250px, (min-width: 768px) 330px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fspeakers-appreciation-group-p…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fspeakers-appre…\">","nodeLabel":"Group of five people standing on stage holding certificates of appreciation wit…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fspeakers-appreciation-group-photo.042de860.jpg&w=750&q=75","totalBytes":36598,"wastedBytes":15543,"wastedPercent":42.469100000000005}]},"displayValue":3,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.98}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"18d71e","url":"https://fil.org/privacy-policy","$url":"https://fil.org/privacy-policy","path":"/privacy-policy","definition":{"name":"privacy-policy","path":"/privacy-policy"}},"reportId":"18d71e","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/privacy-policy","tasksTime":{"inspectHtmlTask":1746541999278,"runLighthouseTask":1746542041560},"seo":{"favicon":"/favicon.ico","title":"Privacy Policy | Filecoin Foundation","description":"Discover how Filecoin Foundation safeguards your data. Read our comprehensive Privacy Policy for detailed information.","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Discover how Filecoin Foundation safeguards your data. Read our comprehensive Privacy Policy for detailed information.","title":"Privacy Policy | Filecoin Foundation"},"internalLinks":19,"externalLinks":8,"htmlSize":113752},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.96},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:29.740Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,790 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,ARTICLE,2,DIV,1,P","selector":"div.flex > article.prose > div.prose > p","boundingRect":{"top":280,"bottom":644,"left":24,"right":388,"width":364,"height":364},"snippet":"<p>","nodeLabel":"Filecoin Foundation and Filecoin Foundation for the Decentralized Web (collecti…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":655.2795,"percent":"37%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1138.8600000000001,"percent":"63%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1794.1395000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1794.1395000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":35,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1794.1395000000002,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181649129945,"data":"reports/privacy-policy/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181649504945,"data":"reports/privacy-policy/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181649879945,"data":"reports/privacy-policy/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181650254945,"data":"reports/privacy-policy/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181650629945,"data":"reports/privacy-policy/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181651004945,"data":"reports/privacy-policy/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181651379945,"data":"reports/privacy-policy/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181651754945,"data":"reports/privacy-policy/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1794.1395000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"e91e63","url":"https://fil.org/security","$url":"https://fil.org/security","path":"/security","definition":{"name":"security","path":"/security"}},"reportId":"e91e63","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/security","tasksTime":{"inspectHtmlTask":1746541999472,"runLighthouseTask":1746542045343},"seo":{"favicon":"/favicon.ico","title":"Filecoin Security | Audits, Incident Response and Monitoring, Resources","description":"We are deeply committed to the integrity and security of the Filecoin ecosystem. Robust security practices are vital for developing, maintaining, and operating the Filecoin network.","og":{"image":"https://fil.org/_next/static/media/Filorg_Security.fbbd80aa.webp","description":"We are deeply committed to the integrity and security of the Filecoin ecosystem. Robust security practices are vital for developing, maintaining, and operating the Filecoin network.","title":"Filecoin Security | Audits, Incident Response and Monitoring, Resources"},"internalLinks":22,"externalLinks":8,"htmlSize":102561},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.96},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:32.145Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,670 ms","metricSavings":{"LCP":150},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":572,"bottom":777,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An illustration depicting a large, old-fashioned key unlocking a futuristi…\" width=\"1792\" height=\"1024\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security.fbbd80aa.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Securit…\">","nodeLabel":"An illustration depicting a large, old-fashioned key unlocking a futuristic pad…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":656.6335,"percent":"25%"},{"phase":"Load Delay","timing":861.5652802223871,"percent":"32%"},{"phase":"Load Time","timing":798.4452671609201,"percent":"30%"},{"phase":"Render Delay","timing":351.22285261669276,"percent":"13%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.86,"scoreDisplayMode":"numeric","numericValue":2667.8669,"numericUnit":"millisecond","displayValue":"2.7 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1789.2509999999997,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":37,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.97,"scoreDisplayMode":"numeric","numericValue":2667.8669,"numericUnit":"millisecond","displayValue":"2.7 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181651503094,"data":"reports/security/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181651878094,"data":"reports/security/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181652253094,"data":"reports/security/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181652628094,"data":"reports/security/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181653003094,"data":"reports/security/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181653378094,"data":"reports/security/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181653753094,"data":"reports/security/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181654128094,"data":"reports/security/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1789.2509999999997,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security.fbbd80aa.webp&w=750&q=100","node":{"type":"node","lhId":"1-39-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":572,"bottom":777,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An illustration depicting a large, old-fashioned key unlocking a futuristi…\" width=\"1792\" height=\"1024\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security.fbbd80aa.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Securit…\">","nodeLabel":"An illustration depicting a large, old-fashioned key unlocking a futuristic pad…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-39-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":572,"bottom":777,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"An illustration depicting a large, old-fashioned key unlocking a futuristi…\" width=\"1792\" height=\"1024\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security.fbbd80aa.webp…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Securit…\">","nodeLabel":"An illustration depicting a large, old-fashioned key unlocking a futuristic pad…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security.fbbd80aa.webp&w=750&q=100","totalBytes":79916,"wastedBytes":24018,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.98}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"0edd10","url":"https://fil.org/security/maturity-model","$url":"https://fil.org/security/maturity-model","path":"/security/maturity-model","definition":{"name":"security-slug","path":"/security/maturity-model"}},"reportId":"0edd10","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/security/maturity-model","tasksTime":{"inspectHtmlTask":1746542000287,"runLighthouseTask":1746542079227},"seo":{"favicon":"/favicon.ico","title":"Web3 Security Maturity Model | Filecoin Foundation","description":"The Web3 Security Maturity Model by Filecoin helps decentralized projects perform self-assessments to improve security across development and operations.","og":{"image":"https://fil.org/_next/static/media/Filorg_Security5.a38e1089.webp","description":"The Web3 Security Maturity Model by Filecoin helps decentralized projects perform self-assessments to improve security across development and operations.","title":"Web3 Security Maturity Model | Filecoin Foundation"},"internalLinks":18,"externalLinks":24,"htmlSize":171710},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:54.864Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,820 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,0,DIV,1,P","selector":"header.grid > div.grid > div.flex > p","boundingRect":{"top":264,"bottom":480,"left":24,"right":388,"width":364,"height":216},"snippet":"<p class=\"\">","nodeLabel":"The Web3 Security Maturity Model, developed by Filecoin Foundation’s security t…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":639.711,"percent":"35%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1176.6539999999998,"percent":"65%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.98,"scoreDisplayMode":"numeric","numericValue":1816.3649999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.89,"scoreDisplayMode":"numeric","numericValue":1816.3649999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":44,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1816.3649999999998,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181674203132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181674578132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181674953132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181675328132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181675703132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181676078132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181676453132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181676828132,"data":"reports/security/maturity-model/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1816.3649999999998,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with incorrect aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""},{"key":"url","valueType":"url","label":"URL"},{"key":"displayedAspectRatio","valueType":"text","label":"Aspect Ratio (Displayed)"},{"key":"actualAspectRatio","valueType":"text","label":"Aspect Ratio (Actual)"}],"items":[{"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security5.a38e1089.webp&w=750&q=100","node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":504,"bottom":709,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A futuristic digital shield with a lock icon in the center, surrounded by …\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security5.a38e1089.web…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Securit…\">","nodeLabel":"A futuristic digital shield with a lock icon in the center, surrounded by binar…"},"displayedAspectRatio":"362 x 203\n        (1.78)","actualAspectRatio":"750 x 429\n        (1.75)","doRatiosMatch":false}]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-49-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,IMG","selector":"div.flex > header.grid > div.grid > img.page-header-image","boundingRect":{"top":504,"bottom":709,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"A futuristic digital shield with a lock icon in the center, surrounded by …\" width=\"2000\" height=\"1143\" decoding=\"async\" data-nimg=\"1\" class=\"page-header-image aspect-video\" style=\"color:transparent\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security5.a38e1089.web…\" src=\"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Securit…\">","nodeLabel":"A futuristic digital shield with a lock icon in the center, surrounded by binar…"},"url":"https://fil.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2FFilorg_Security5.a38e1089.webp&w=750&q=100","totalBytes":76644,"wastedBytes":23035,"wastedPercent":30.0541181041181}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"41345d","url":"https://fil.org/terms-of-use","$url":"https://fil.org/terms-of-use","path":"/terms-of-use","definition":{"name":"terms-of-use","path":"/terms-of-use"}},"reportId":"41345d","artifactPath":"","artifactUrl":"/site-audit-reports/ff/reports/terms-of-use","tasksTime":{"inspectHtmlTask":1746541999545,"runLighthouseTask":1746542078170},"seo":{"favicon":"/favicon.ico","title":"Terms of Use | Filecoin Foundation","description":"Understand the terms and conditions of using Filecoin Foundation services. Read our detailed Terms of Use for more information.","og":{"image":"https://fil.org/_next/static/media/Filorg_Home.f8c7676c.webp","description":"Understand the terms and conditions of using Filecoin Foundation services. Read our detailed Terms of Use for more information.","title":"Terms of Use | Filecoin Foundation"},"internalLinks":18,"externalLinks":6,"htmlSize":89434},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.96},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:34:54.796Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,790 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,ARTICLE,2,DIV,2,P","selector":"div.flex > article.prose > div.prose > p","boundingRect":{"top":440,"bottom":692,"left":24,"right":388,"width":364,"height":252},"snippet":"<p>","nodeLabel":"Please review these Terms carefully before using the Website. We may change the…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":647.4195,"percent":"36%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1139.5815,"percent":"64%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1787.001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1787.001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":48,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1787.001,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181674118015,"data":"reports/terms-of-use/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181674493015,"data":"reports/terms-of-use/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181674868015,"data":"reports/terms-of-use/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181675243015,"data":"reports/terms-of-use/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181675618015,"data":"reports/terms-of-use/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181675993015,"data":"reports/terms-of-use/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181676368015,"data":"reports/terms-of-use/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181676743015,"data":"reports/terms-of-use/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1787.001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}}],"scanMeta":{"favicon":"/favicon.ico","monitor":{"status":"completed","timeRunning":121170,"doneTargets":42,"allTargets":42,"donePercStr":"100","errorPerc":"0.00","timeRemaining":0,"pagesPerSecond":"0.35","cpuUsage":"39.3%","memoryUsage":"97.5%","workers":9},"routes":21,"score":0.9757142857142854},"options":{"client":{"groupRoutesKey":"route.definition.name","columns":{"overview":[{"label":"Screenshot Timeline","key":"report.audits.screenshot-thumbnails","cols":6}],"performance":[{"cols":1,"label":"FCP","tooltip":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","key":"report.audits.first-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"LCP","tooltip":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more](https://web.dev/lighthouse-largest-contentful-paint/)","key":"report.audits.largest-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"CLS","tooltip":"Cumulative Layout Shift measures the movement of visible elements within the viewport.","sortKey":"numericValue","key":"report.audits.cumulative-layout-shift"},{"cols":1,"label":"FID","warning":true,"tooltip":"Warning: This is deprecated in favour if INP which is not yet supported by Unlighthouse. The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more](https://web.dev/lighthouse-max-potential-fid/).","sortKey":"numericValue","key":"report.audits.max-potential-fid"},{"cols":1,"label":"TBT","tooltip":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more](https://web.dev/lighthouse-total-blocking-time/).","sortKey":"numericValue","key":"report.audits.total-blocking-time"},{"cols":1,"label":"SI","sortKey":"numericValue","tooltip":"The speed index is a page load performance metric that shows you how quickly the contents of a page are visibly populated. [Learn more](https://web.dev/speed-index/).","key":"report.audits.speed-index"}],"accessibility":[{"cols":3,"label":"Color Contrast","tooltip":"Background and foreground colors do not have a sufficient contrast ratio.","sortKey":"length:details.items","key":"report.audits.color-contrast"},{"cols":1,"label":"Headings","tooltip":"Heading elements appear in a sequentially-descending order","sortKey":"length:details.items","key":"report.audits.heading-order"},{"cols":1,"label":"ARIA","tooltip":"An aggregate of all ARIA audits.","sortKey":"displayValue","sortable":true,"key":"report.computed.ariaIssues"},{"cols":1,"label":"Labels","tooltip":"Form elements have associated labels","sortKey":"length:details.items","key":"report.audits.label"},{"cols":1,"label":"Image Alts","tooltip":"Image elements have [alt] attributes","sortKey":"length:details.items","key":"report.audits.image-alt"},{"cols":1,"label":"Link Names","tooltip":"Links do not have a discernible name","sortKey":"length:details.items","key":"report.audits.link-name"}],"best-practices":[{"cols":2,"label":"Errors","tooltip":"No browser errors logged to the console","sortKey":"length:details.items","key":"report.audits.errors-in-console"},{"cols":2,"label":"Inspector Issues","tooltip":"No issues in the `Issues` panel in Chrome Devtools","sortKey":"length:details.items","key":"report.audits.inspector-issues"},{"cols":2,"label":"Images Responsive","tooltip":"Serves images with appropriate resolution","sortKey":"length:details.items","key":"report.audits.image-size-responsive"},{"cols":2,"label":"Image Aspect Ratio","tooltip":"Displays images with correct aspect ratio","sortKey":"length:details.items","key":"report.audits.image-aspect-ratio"}],"seo":[{"cols":1,"label":"Indexable","tooltip":"Page isn’t blocked from indexing","key":"report.audits.is-crawlable"},{"cols":1,"label":"Internal link","sortable":true,"key":"seo.internalLinks"},{"cols":1,"label":"External link","sortable":true,"key":"seo.externalLinks"},{"cols":2,"label":"Description","key":"seo.description"},{"cols":2,"label":"Share Image","key":"seo.og.image"}]}},"site":"https://fil.org","lighthouseOptions":{"onlyCategories":["performance","accessibility","best-practices","seo"]},"scanner":{"customSampling":{},"ignoreI18nPages":true,"maxRoutes":200,"skipJavascript":true,"samples":3,"throttle":true,"crawler":false,"dynamicSampling":1,"sitemap":["https://fil.org/sitemap.xml"],"robotsTxt":true,"device":"mobile","exclude":["/cdn-cgi/*"],"_robotsTxtRules":[]},"routerPrefix":"/site-audit-reports/ff/"}}