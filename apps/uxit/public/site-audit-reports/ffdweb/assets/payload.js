window.__unlighthouse_payload = {"reports":[{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"6666cd","url":"https://ffdweb.org","$url":"https://ffdweb.org/","path":"/","definition":{"name":"_index","path":"/"}},"reportId":"6666cd","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports","tasksTime":{"inspectHtmlTask":1746542122265,"runLighthouseTask":1746542124567},"seo":{"favicon":"/favicon.ico","title":"FFDW | Rebuilding the Internet for Good","description":"Filecoin Foundation for the Decentralized Web is a nonprofit organization committed to preserving humanity’s most important information by funding the development of open-source tools","og":{"image":"https://ffdweb.org/_next/static/media/homepage.57aece90.webp","description":"Filecoin Foundation for the Decentralized Web is a nonprofit organization committed to preserving humanity’s most important information by funding the development of open-source tools","title":"FFDW | Rebuilding the Internet for Good"},"internalLinks":34,"externalLinks":4,"htmlSize":293014},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.95},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:56.597Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,610 ms","metricSavings":{"LCP":100},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.mt-16 > header.grid > div.flex > img.aspect-square","boundingRect":{"top":210,"bottom":574,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"An abstract 3D visualization of interconnected data cubes and nodes in gre…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhomepage.57aece90.webp&amp;w=16&amp;q…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhomepage.57…\">","nodeLabel":"An abstract 3D visualization of interconnected data cubes and nodes in green, b…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":656.468,"percent":"25%"},{"phase":"Load Delay","timing":109.05517416283692,"percent":"4%"},{"phase":"Load Time","timing":1040.1298938433065,"percent":"40%"},{"phase":"Render Delay","timing":801.179431993857,"percent":"31%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.87,"scoreDisplayMode":"numeric","numericValue":2606.8325000000004,"numericUnit":"millisecond","displayValue":"2.6 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.87,"scoreDisplayMode":"numeric","numericValue":1903.1606000000002,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":37,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2636.8373600000004,"numericUnit":"millisecond","displayValue":"2.6 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181735950491,"data":"reports/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181736325491,"data":"reports/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181736700491,"data":"reports/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181737075491,"data":"reports/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181737450491,"data":"reports/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181737825491,"data":"reports/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181738200491,"data":"reports/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181738575491,"data":"reports/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1903.1606000000002,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-42-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.mt-16 > header.grid > div.flex > img.aspect-square","boundingRect":{"top":210,"bottom":574,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"An abstract 3D visualization of interconnected data cubes and nodes in gre…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhomepage.57aece90.webp&amp;w=16&amp;q…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhomepage.57…\">","nodeLabel":"An abstract 3D visualization of interconnected data cubes and nodes in green, b…"},"url":"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhomepage.57aece90.webp&w=750&q=100&dpl=dpl_9HMs761ULochfxxo4MzNJkqegM6B","totalBytes":154926,"wastedBytes":43167,"wastedPercent":27.863288888888892}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"46b393","url":"https://ffdweb.org/about","$url":"https://ffdweb.org/about","path":"/about","definition":{"name":"about","path":"/about"}},"reportId":"46b393","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/about","tasksTime":{"inspectHtmlTask":1746542122518,"runLighthouseTask":1746542124748},"seo":{"favicon":"/favicon.ico","title":"About FFDW | Building and Supporting the Decentralized Community","description":"Discover how Filecoin Foundation for the Decentralized Web (FFDW) accelerates open, decentralized technologies and safeguards vital data. Learn about our mission to empower communities, preserve cultural knowledge, and shape a fairer, more resilient internet for everyone.","og":{"image":"https://ffdweb.org/_next/static/media/about.88b8532a.webp","description":"Discover how Filecoin Foundation for the Decentralized Web (FFDW) accelerates open, decentralized technologies and safeguards vital data. Learn about our mission to empower communities, preserve cultural knowledge, and shape a fairer, more resilient internet for everyone.","title":"About FFDW | Building and Supporting the Decentralized Community"},"internalLinks":19,"externalLinks":9,"htmlSize":157074},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.97},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:43.157Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,460 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":493,"bottom":857,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A data center visualization showing server racks illuminated in green and …\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.88b8532a.webp&amp;w=16&amp;q=10…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.88b85…\">","nodeLabel":"A data center visualization showing server racks illuminated in green and purpl…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":656.4441,"percent":"27%"},{"phase":"Load Delay","timing":97.82337729861695,"percent":"4%"},{"phase":"Load Time","timing":1094.1014375451919,"percent":"45%"},{"phase":"Render Delay","timing":608.0751851561913,"percent":"25%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.9,"scoreDisplayMode":"numeric","numericValue":2456.4441,"numericUnit":"millisecond","displayValue":"2.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1730.2424,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":40,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2456.4441000000006,"numericUnit":"millisecond","displayValue":"2.5 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181722738563,"data":"reports/about/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181723113563,"data":"reports/about/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181723488563,"data":"reports/about/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181723863563,"data":"reports/about/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181724238563,"data":"reports/about/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181724613563,"data":"reports/about/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181724988563,"data":"reports/about/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181725363563,"data":"reports/about/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1730.2424,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-32-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":493,"bottom":857,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A data center visualization showing server racks illuminated in green and …\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.88b8532a.webp&amp;w=16&amp;q=10…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.88b85…\">","nodeLabel":"A data center visualization showing server racks illuminated in green and purpl…"},"url":"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fabout.88b8532a.webp&w=750&q=100&dpl=dpl_9HMs761ULochfxxo4MzNJkqegM6B","totalBytes":96414,"wastedBytes":26864,"wastedPercent":27.863288888888892}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"126ac9","url":"https://ffdweb.org/blog","$url":"https://ffdweb.org/blog","path":"/blog","definition":{"name":"blog","path":"/blog"}},"reportId":"126ac9","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/blog","tasksTime":{"inspectHtmlTask":1746542122860,"runLighthouseTask":1746542125053},"seo":{"favicon":"/favicon.ico","title":"FFDW Blog | Latest Web3 Insights & Updates","description":"Stay informed on the newest advancements in decentralized tech, human rights data preservation, and social impact. Discover fresh perspectives from FFDW.","og":{"image":"https://ffdweb.org/_next/static/media/blog.2f56edbc.webp","description":"Stay informed on the newest advancements in decentralized tech, human rights data preservation, and social impact. Discover fresh perspectives from FFDW.","title":"FFDW Blog | Latest Web3 Insights & Updates"},"internalLinks":19,"externalLinks":4,"htmlSize":817210},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.94},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:57.823Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,530 ms","metricSavings":{"LCP":50},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,0,DIV,1,DIV,0,IMG","selector":"header.grid > div.grid > div.relative > img.page-header-image","boundingRect":{"top":572,"bottom":777,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"page-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 1024px) 490px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fhero-ffdw.webp&amp;w=640&amp;q=100 640w, /_n…\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2Fhero-ffdw.webp&amp;w=7…\">","nodeLabel":"header.grid > div.grid > div.relative > img.page-header-image"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":654.3453,"percent":"26%"},{"phase":"Load Delay","timing":138.4950376134277,"percent":"5%"},{"phase":"Load Time","timing":734.8880618077305,"percent":"29%"},{"phase":"Render Delay","timing":1001.6198005788417,"percent":"40%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.89,"scoreDisplayMode":"numeric","numericValue":2529.3482,"numericUnit":"millisecond","displayValue":"2.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.73,"scoreDisplayMode":"numeric","numericValue":2339.4259999999995,"numericUnit":"millisecond","displayValue":"2.3 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":30,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2536.84821,"numericUnit":"millisecond","displayValue":"2.5 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181737171552,"data":"reports/blog/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181737546552,"data":"reports/blog/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181737921552,"data":"reports/blog/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181738296552,"data":"reports/blog/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181738671552,"data":"reports/blog/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181739046552,"data":"reports/blog/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181739421552,"data":"reports/blog/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181739796552,"data":"reports/blog/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2339.4259999999995,"numericUnit":"millisecond","displayValue":"2.3 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"7ab6bf","url":"https://ffdweb.org/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology","$url":"https://ffdweb.org/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology","path":"/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology","definition":{"name":"blog-slug","path":"/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology"}},"reportId":"7ab6bf","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology","tasksTime":{"inspectHtmlTask":1746542123049,"runLighthouseTask":1746542124924},"seo":{"favicon":"/favicon.ico","title":"Empowering a More Informed, Transparent Society with Decentralized Technology | FFDW","description":"Discover how MuckRock leverages Filecoin and IPFS to preserve public documents, empowering journalists and researchers to build trust through transparent information access.","og":{"image":"https://ffdweb.org/assets/images/64423821fcf3ec505f7f19bd_0202-muckrock-spotlight.png","description":"Discover how MuckRock leverages Filecoin and IPFS to preserve public documents, empowering journalists and researchers to build trust through transparent information access.","title":"Empowering a More Informed, Transparent Society with Decentralized Technology | FFDW"},"internalLinks":20,"externalLinks":25,"htmlSize":160812},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:55.608Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,750 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-H1","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,0,DIV,1,H1","selector":"div.m-auto > header.space-y-6 > div.space-y-6 > h1.text-balance","boundingRect":{"top":253,"bottom":453,"left":24,"right":388,"width":364,"height":200},"snippet":"<h1 class=\"text-balance text-4xl font-bold\">","nodeLabel":"Empowering a More Informed, Transparent Society with Decentralized Technology"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":657.8781,"percent":"38%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1094.2903000000001,"percent":"62%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1752.1684,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1752.1684,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":37,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1752.1684,"numericUnit":"millisecond","displayValue":"1.8 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181735124580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181735499580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181735874580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181736249580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181736624580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181736999580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181737374580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181737749580,"data":"reports/blog/empowering-a-more-informed-transparent-society-with-decentralized-technology/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1752.1684,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"c10f77","url":"https://ffdweb.org/digest","$url":"https://ffdweb.org/digest","path":"/digest","definition":{"name":"digest","path":"/digest"}},"reportId":"c10f77","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/digest","tasksTime":{"inspectHtmlTask":1746542123319,"runLighthouseTask":1746542125521},"seo":{"favicon":"/favicon.ico","title":"FFDW DWeb Digest | In-Depth Exploration of the Decentralized Web","description":"Explore FFDW DWeb Digest for expert insights on cognitive liberty, privacy, and crypto policy. Discover the evolving landscape of digital autonomy and blockchain.","og":{"image":"https://ffdweb.org/_next/static/media/digest.4c4725b8.webp","description":"Explore FFDW DWeb Digest for expert insights on cognitive liberty, privacy, and crypto policy. Discover the evolving landscape of digital autonomy and blockchain.","title":"FFDW DWeb Digest | In-Depth Exploration of the Decentralized Web"},"internalLinks":30,"externalLinks":7,"htmlSize":399433},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.83},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:58.534Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"4,470 ms","metricSavings":{"LCP":1950},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A dimly lit interior of a print shop with multiple printing machines, colo…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdigest.4c4725b8.webp&amp;w=16&amp;q=1…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdigest.4c47…\">","nodeLabel":"A dimly lit interior of a print shop with multiple printing machines, colorful …"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":650.105,"percent":"15%"},{"phase":"Load Delay","timing":1575.4355748022522,"percent":"35%"},{"phase":"Load Time","timing":1499.2234867661841,"percent":"34%"},{"phase":"Render Delay","timing":742.1634384315635,"percent":"17%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.38,"scoreDisplayMode":"numeric","numericValue":4466.9275,"numericUnit":"millisecond","displayValue":"4.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.86,"scoreDisplayMode":"numeric","numericValue":1948.0044000000003,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":38,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.83,"scoreDisplayMode":"numeric","numericValue":4466.9275,"numericUnit":"millisecond","displayValue":"4.5 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181737888838,"data":"reports/digest/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181738263838,"data":"reports/digest/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181738638838,"data":"reports/digest/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181739013838,"data":"reports/digest/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181739388838,"data":"reports/digest/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181739763838,"data":"reports/digest/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181740138838,"data":"reports/digest/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181740513838,"data":"reports/digest/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1948.0044000000003,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-40-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A dimly lit interior of a print shop with multiple printing machines, colo…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdigest.4c4725b8.webp&amp;w=16&amp;q=1…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdigest.4c47…\">","nodeLabel":"A dimly lit interior of a print shop with multiple printing machines, colorful …"},"url":"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdigest.4c4725b8.webp&w=750&q=100&dpl=dpl_9HMs761ULochfxxo4MzNJkqegM6B","totalBytes":239250,"wastedBytes":66663,"wastedPercent":27.863288888888892},{"node":{"type":"node","lhId":"1-43-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,1,SECTION,1,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1671,"bottom":1875,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 340px, (min-width: 768px) 470px, (min-width: 1024px) 48…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-02.png&amp;w=640&amp;q=100 …\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-0…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-02.png&w=750&q=100","totalBytes":172250,"wastedBytes":49166,"wastedPercent":28.543601895734593},{"node":{"type":"node","lhId":"1-41-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,SECTION,1,SECTION,0,ARTICLE,0,DIV,0,IMG","selector":"section.grid > article.card > div.relative > img.card-image","boundingRect":{"top":1136,"bottom":1339,"left":25,"right":387,"width":362,"height":204},"snippet":"<img alt=\"\" loading=\"lazy\" decoding=\"async\" data-nimg=\"fill\" class=\"card-image h-full w-full\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 640px) 340px, (min-width: 768px) 470px, (min-width: 1024px) 48…\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-01.png&amp;w=640&amp;q=100 …\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-0…\">","nodeLabel":"section.grid > article.card > div.relative > img.card-image"},"url":"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-01.png&w=750&q=100","totalBytes":71852,"wastedBytes":20509,"wastedPercent":28.543601895734593}]},"displayValue":3,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.96}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"822210","url":"https://ffdweb.org/digest/twiddler-configurability-for-me-but-not-for-thee","$url":"https://ffdweb.org/digest/twiddler-configurability-for-me-but-not-for-thee","path":"/digest/twiddler-configurability-for-me-but-not-for-thee","definition":{"name":"digest-slug","path":"/digest/twiddler-configurability-for-me-but-not-for-thee"}},"reportId":"822210","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/digest/twiddler-configurability-for-me-but-not-for-thee","tasksTime":{"inspectHtmlTask":1746542123636,"runLighthouseTask":1746542125171},"seo":{"favicon":"/favicon.ico","title":"Twiddler: Configurability for Me, But Not For Thee | FFDW","description":"Digital platforms control users by constantly adjusting their systems while denying users the same power, leading to declining user experience and increased profits.","og":{"image":"https://ffdweb.org/assets/images/DWD-Illustration-12.png","description":"Digital platforms control users by constantly adjusting their systems while denying users the same power, leading to declining user experience and increased profits.","title":"Twiddler: Configurability for Me, But Not For Thee | FFDW"},"internalLinks":19,"externalLinks":8,"htmlSize":146017},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.98},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.98},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:55.406Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,940 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image","boundingRect":{"top":506,"bottom":711,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"article-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 768px) 680px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-12.png&amp;w=640&amp;q=100 …\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-1…\">","nodeLabel":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":655.8926,"percent":"34%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":699.3743983122703,"percent":"36%"},{"phase":"Render Delay","timing":585.7150516877298,"percent":"30%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.97,"scoreDisplayMode":"numeric","numericValue":1940.98205,"numericUnit":"millisecond","displayValue":"1.9 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1725.8711000000003,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":30,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":1962.4931450000001,"numericUnit":"millisecond","displayValue":"2.0 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181735168171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181735543171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181735918171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181736293171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181736668171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181737043171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181737418171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181737793171,"data":"reports/digest/twiddler-configurability-for-me-but-not-for-thee/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1725.8711000000003,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements are not in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-H3","path":"1,HTML,1,BODY,2,FOOTER,1,DIV,1,NAV,0,DIV,0,H3","selector":"div.grid > nav.grid > div.grid > h3.text-balance","boundingRect":{"top":16642,"bottom":16663,"left":24,"right":388,"width":364,"height":21},"snippet":"<h3 class=\"text-balance text-md font-bold\">","nodeLabel":"Browse","explanation":"Fix any of the following:\n  Heading order invalid"}}],"debugData":{"type":"debugdata","impact":"moderate","tags":["cat.semantics","best-practice"]}}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-31-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,HEADER,1,DIV,0,IMG","selector":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image","boundingRect":{"top":506,"bottom":711,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"\" decoding=\"async\" data-nimg=\"fill\" class=\"article-header-image object-cover\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 768px) 680px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-12.png&amp;w=640&amp;q=100 …\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-1…\">","nodeLabel":"div.m-auto > header.space-y-6 > div.relative > img.article-header-image"},"url":"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2FDWD-Illustration-12.png&w=750&q=100","totalBytes":96422,"wastedBytes":26802,"wastedPercent":27.796603475513425}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"6da8c9","url":"https://ffdweb.org/faqs","$url":"https://ffdweb.org/faqs","path":"/faqs","definition":{"name":"faqs","path":"/faqs"}},"reportId":"6da8c9","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/faqs","tasksTime":{"inspectHtmlTask":1746542123792,"runLighthouseTask":1746542126107},"seo":{"favicon":"/favicon.ico","title":"FFDW FAQs | Answers About Our Mission & the Decentralized Web","description":"Discover how FFDW supports open, decentralized tech, funds projects, and fosters a resilient internet. Get quick answers to your pressing questions.","og":{"image":"https://ffdweb.org/_next/static/media/homepage.57aece90.webp","description":"Discover how FFDW supports open, decentralized tech, funds projects, and fosters a resilient internet. Get quick answers to your pressing questions.","title":"FFDW FAQs | Answers About Our Mission & the Decentralized Web"},"internalLinks":22,"externalLinks":12,"htmlSize":114244},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:56.127Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,770 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-H2","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,2,DIV,0,H2","selector":"div.flex > div.flex > div.prose > h2#what-is-the-difference-between-filecoin-foundation-and-filecoin-foundation-for-the-decentralized-web","boundingRect":{"top":600,"bottom":728,"left":24,"right":388,"width":364,"height":128},"snippet":"<h2 id=\"what-is-the-difference-between-filecoin-foundation-and-filecoin-foundation…\">","nodeLabel":"What is the difference between Filecoin Foundation and Filecoin Foundation for …"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":650.887,"percent":"37%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1114.3860000000002,"percent":"63%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1765.2730000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1765.2730000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":1,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":52,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2560.52383,"numericUnit":"millisecond","displayValue":"2.6 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181735743529,"data":"reports/faqs/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181736118529,"data":"reports/faqs/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181736493529,"data":"reports/faqs/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181736868529,"data":"reports/faqs/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181737243529,"data":"reports/faqs/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181737618529,"data":"reports/faqs/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181737993529,"data":"reports/faqs/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181738368529,"data":"reports/faqs/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1765.2730000000001,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"5218ee","url":"https://ffdweb.org/learning-resources","$url":"https://ffdweb.org/learning-resources","path":"/learning-resources","definition":{"name":"learning-resources","path":"/learning-resources"}},"reportId":"5218ee","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/learning-resources","tasksTime":{"inspectHtmlTask":1746542123846,"runLighthouseTask":1746542126900},"seo":{"favicon":"/favicon.ico","title":"FFDW Learning Resources | Decentralized Tech & Social Impact","description":"Discover case studies, tutorials, and tools on decentralized technologies driving social change. Learn, share, and help shape the future with FFDW.","og":{"image":"https://ffdweb.org/_next/static/media/learning-resources.a5e7412d.webp","description":"Discover case studies, tutorials, and tools on decentralized technologies driving social change. Learn, share, and help shape the future with FFDW.","title":"FFDW Learning Resources | Decentralized Tech & Social Impact"},"internalLinks":18,"externalLinks":5,"htmlSize":211503},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.97},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:57.867Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"2,490 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A neon-style illustration of a workshop pegboard with tools, featuring col…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flearning-resources.a5e7412d.w…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flearning-re…\">","nodeLabel":"A neon-style illustration of a workshop pegboard with tools, featuring colorful…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":648.5445,"percent":"26%"},{"phase":"Load Delay","timing":769.2203585117772,"percent":"31%"},{"phase":"Load Time","timing":833.4539797711277,"percent":"33%"},{"phase":"Render Delay","timing":241.27656171709532,"percent":"10%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.9,"scoreDisplayMode":"numeric","numericValue":2492.4954000000002,"numericUnit":"millisecond","displayValue":"2.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1788.2330000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":43,"numericUnit":"millisecond","displayValue":"40 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2492.4954000000007,"numericUnit":"millisecond","displayValue":"2.5 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181737199108,"data":"reports/learning-resources/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181737574108,"data":"reports/learning-resources/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181737949108,"data":"reports/learning-resources/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181738324108,"data":"reports/learning-resources/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181738699108,"data":"reports/learning-resources/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181739074108,"data":"reports/learning-resources/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181739449108,"data":"reports/learning-resources/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181739824108,"data":"reports/learning-resources/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1788.2330000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A neon-style illustration of a workshop pegboard with tools, featuring col…\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flearning-resources.a5e7412d.w…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flearning-re…\">","nodeLabel":"A neon-style illustration of a workshop pegboard with tools, featuring colorful…"},"url":"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flearning-resources.a5e7412d.webp&w=750&q=100&dpl=dpl_9HMs761ULochfxxo4MzNJkqegM6B","totalBytes":144438,"wastedBytes":40245,"wastedPercent":27.863288888888892}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.99}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"18d71e","url":"https://ffdweb.org/privacy-policy","$url":"https://ffdweb.org/privacy-policy","path":"/privacy-policy","definition":{"name":"privacy-policy","path":"/privacy-policy"}},"reportId":"18d71e","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/privacy-policy","tasksTime":{"inspectHtmlTask":1746542124109,"runLighthouseTask":1746542127371},"seo":{"favicon":"/favicon.ico","title":"FFDW | Privacy Policy","description":"Discover how Filecoin Foundation safeguards your data. Read our comprehensive Privacy Policy for detailed information.","og":{"image":"https://ffdweb.org/_next/static/media/homepage.57aece90.webp","description":"Discover how Filecoin Foundation safeguards your data. Read our comprehensive Privacy Policy for detailed information.","title":"FFDW | Privacy Policy"},"internalLinks":18,"externalLinks":5,"htmlSize":126084},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.93},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:35:56.331Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"3,070 ms","metricSavings":{"LCP":550},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,ARTICLE,1,DIV,1,P","selector":"div.flex > article.prose > div.prose > p","boundingRect":{"top":274,"bottom":582,"left":24,"right":388,"width":364,"height":308},"snippet":"<p>","nodeLabel":"Filecoin Foundation and Filecoin Foundation for the Decentralized Web (collecti…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":644.329,"percent":"21%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":2425.5059999999994,"percent":"79%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.76,"scoreDisplayMode":"numeric","numericValue":3069.8349999999996,"numericUnit":"millisecond","displayValue":"3.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1729.9969999999998,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":50,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.95,"scoreDisplayMode":"numeric","numericValue":3069.835,"numericUnit":"millisecond","displayValue":"3.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181735743937,"data":"reports/privacy-policy/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181736118937,"data":"reports/privacy-policy/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181736493937,"data":"reports/privacy-policy/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181736868937,"data":"reports/privacy-policy/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181737243937,"data":"reports/privacy-policy/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181737618937,"data":"reports/privacy-policy/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181737993937,"data":"reports/privacy-policy/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181738368937,"data":"reports/privacy-policy/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1729.9969999999998,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.98}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"35a286","url":"https://ffdweb.org/projects","$url":"https://ffdweb.org/projects","path":"/projects","definition":{"name":"projects","path":"/projects"}},"reportId":"35a286","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/projects","tasksTime":{"inspectHtmlTask":1746542124208,"runLighthouseTask":1746542164933},"seo":{"favicon":"/favicon.ico","title":"FFDW Projects | Explore Partnerships Advancing the Decentralized Web","description":"Explore how FFDW collaborates with nonprofits to build open-source solutions, preserve vital data, and shape a decentralized internet. See our partners in action.","og":{"image":"https://ffdweb.org/_next/static/media/projects.4f479a26.webp","description":"Explore how FFDW collaborates with nonprofits to build open-source solutions, preserve vital data, and shape a decentralized internet. See our partners in action.","title":"FFDW Projects | Explore Partnerships Advancing the Decentralized Web"},"internalLinks":18,"externalLinks":4,"htmlSize":132091},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.96},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:36:30.571Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,530 ms","metricSavings":{"LCP":50},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A digital visualization of Earth as a glowing green and purple globe with …\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects.4f479a26.webp&amp;w=16&amp;q…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects.4f…\">","nodeLabel":"A digital visualization of Earth as a glowing green and purple globe with conne…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":638.5485,"percent":"25%"},{"phase":"Load Delay","timing":794.5848603151393,"percent":"31%"},{"phase":"Load Time","timing":817.4634032544946,"percent":"32%"},{"phase":"Render Delay","timing":279.44493643036594,"percent":"11%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.89,"scoreDisplayMode":"numeric","numericValue":2530.0416999999998,"numericUnit":"millisecond","displayValue":"2.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1783.5885,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":48,"numericUnit":"millisecond","displayValue":"50 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.98,"scoreDisplayMode":"numeric","numericValue":2530.0416999999998,"numericUnit":"millisecond","displayValue":"2.5 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181769905064,"data":"reports/projects/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181770280064,"data":"reports/projects/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181770655064,"data":"reports/projects/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181771030064,"data":"reports/projects/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181771405064,"data":"reports/projects/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181771780064,"data":"reports/projects/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181772155064,"data":"reports/projects/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181772530064,"data":"reports/projects/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1783.5885,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-40-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,HEADER,1,DIV,0,IMG","selector":"div.flex > header.grid > div.flex > img.aspect-square","boundingRect":{"top":451,"bottom":815,"left":24,"right":388,"width":364,"height":364},"snippet":"<img alt=\"A digital visualization of Earth as a glowing green and purple globe with …\" width=\"1200\" height=\"1200\" decoding=\"async\" data-nimg=\"1\" class=\"aspect-square h-auto w-full max-w-96 object-contain\" style=\"color:transparent\" sizes=\"390px\" srcset=\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects.4f479a26.webp&amp;w=16&amp;q…\" src=\"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects.4f…\">","nodeLabel":"A digital visualization of Earth as a glowing green and purple globe with conne…"},"url":"https://ffdweb.org/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprojects.4f479a26.webp&w=750&q=100&dpl=dpl_9HMs761ULochfxxo4MzNJkqegM6B","totalBytes":126944,"wastedBytes":35371,"wastedPercent":27.863288888888892}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.97}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"2f339f","url":"https://ffdweb.org/projects/cc-pk-movement-for-a-better-internet","$url":"https://ffdweb.org/projects/cc-pk-movement-for-a-better-internet","path":"/projects/cc-pk-movement-for-a-better-internet","definition":{"name":"projects-slug","path":"/projects/cc-pk-movement-for-a-better-internet"}},"reportId":"2f339f","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/projects/cc-pk-movement-for-a-better-internet","tasksTime":{"inspectHtmlTask":1746542124392,"runLighthouseTask":1746542164980},"seo":{"favicon":"/favicon.ico","title":"Movement for a Better Internet | FFDW","description":"FFDW supports the Movement for a Better Internet, a collaborative initiative working to shape the future of the internet around public interest values and improve the web for everyone.","og":{"image":"https://ffdweb.org/assets/images/partnerlogo_movementbetterinternet.png","description":"FFDW supports the Movement for a Better Internet, a collaborative initiative working to shape the future of the internet around public interest values and improve the web for everyone.","title":"Movement for a Better Internet | FFDW"},"internalLinks":19,"externalLinks":5,"htmlSize":91757},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:36:18.718Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,730 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,DIV,1,DIV,0,DIV,1,DIV,0,IMG","selector":"div.m-auto > div > div.relative > img.object-contain","boundingRect":{"top":372,"bottom":577,"left":24,"right":388,"width":364,"height":205},"snippet":"<img alt=\"Movement for a Better Internet's logo\" decoding=\"async\" data-nimg=\"fill\" class=\"object-contain\" style=\"position: absolute; height: 100%; width: 100%; inset: 0px;\" sizes=\"(min-width: 768px) 640px, 100vw\" srcset=\"/_next/image?url=%2Fassets%2Fimages%2Fpartnerlogo_movementbetterinternet.p…\" src=\"https://ffdweb.org/_next/image?url=%2Fassets%2Fimages%2Fpartnerlogo_moveme…\">","nodeLabel":"Movement for a Better Internet's logo"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":656.6137,"percent":"38%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":535.4793957157525,"percent":"31%"},{"phase":"Render Delay","timing":537.4074042842476,"percent":"31%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1729.5005,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1729.5005,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":33,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1729.5005,"numericUnit":"millisecond","displayValue":"1.7 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181758112849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181758487849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181758862849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181759237849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181759612849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181759987849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181760362849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181760737849,"data":"reports/projects/cc-pk-movement-for-a-better-internet/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1729.5005,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"41345d","url":"https://ffdweb.org/terms-of-use","$url":"https://ffdweb.org/terms-of-use","path":"/terms-of-use","definition":{"name":"terms-of-use","path":"/terms-of-use"}},"reportId":"41345d","artifactPath":"","artifactUrl":"/site-audit-reports/ffdweb/reports/terms-of-use","tasksTime":{"inspectHtmlTask":1746542124482,"runLighthouseTask":1746542165050},"seo":{"favicon":"/favicon.ico","title":"FFDW | Terms of Use","description":"Understand the terms and conditions of using Filecoin Foundation services. Read our detailed Terms of Use for more information.","og":{"image":"https://ffdweb.org/_next/static/media/homepage.57aece90.webp","description":"Understand the terms and conditions of using Filecoin Foundation services. Read our detailed Terms of Use for more information.","title":"FFDW | Terms of Use"},"internalLinks":18,"externalLinks":4,"htmlSize":103395},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.99},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":1},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":1},{"key":"seo","id":"seo","title":"SEO","score":1}],"fetchTime":"2025-05-06T14:36:18.718Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"1,740 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-P","path":"1,HTML,1,BODY,1,MAIN,0,DIV,1,ARTICLE,1,DIV,2,P","selector":"div.flex > article.prose > div.prose > p","boundingRect":{"top":462,"bottom":742,"left":24,"right":388,"width":364,"height":280},"snippet":"<p>","nodeLabel":"Please review these Terms carefully before using the Website. We may change the…"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":663.0781,"percent":"38%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":1073.5480000000002,"percent":"62%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.99,"scoreDisplayMode":"numeric","numericValue":1736.6261000000002,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.91,"scoreDisplayMode":"numeric","numericValue":1736.6261,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":34,"numericUnit":"millisecond","displayValue":"30 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":1736.6261000000002,"numericUnit":"millisecond","displayValue":"1.7 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181758117817,"data":"reports/terms-of-use/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181758492817,"data":"reports/terms-of-use/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181758867817,"data":"reports/terms-of-use/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181759242817,"data":"reports/terms-of-use/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181759617817,"data":"reports/terms-of-use/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181759992817,"data":"reports/terms-of-use/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181760367817,"data":"reports/terms-of-use/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181760742817,"data":"reports/terms-of-use/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1736.6261,"numericUnit":"millisecond","displayValue":"1.7 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":1}}],"scanMeta":{"favicon":"/favicon.ico","monitor":{"status":"completed","timeRunning":79300,"doneTargets":24,"allTargets":24,"donePercStr":"100","errorPerc":"0.00","timeRemaining":0,"pagesPerSecond":"0.30","cpuUsage":"42.8%","memoryUsage":"98.0%","workers":9},"routes":12,"score":0.9883333333333334},"options":{"client":{"groupRoutesKey":"route.definition.name","columns":{"overview":[{"label":"Screenshot Timeline","key":"report.audits.screenshot-thumbnails","cols":6}],"performance":[{"cols":1,"label":"FCP","tooltip":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","key":"report.audits.first-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"LCP","tooltip":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more](https://web.dev/lighthouse-largest-contentful-paint/)","key":"report.audits.largest-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"CLS","tooltip":"Cumulative Layout Shift measures the movement of visible elements within the viewport.","sortKey":"numericValue","key":"report.audits.cumulative-layout-shift"},{"cols":1,"label":"FID","warning":true,"tooltip":"Warning: This is deprecated in favour if INP which is not yet supported by Unlighthouse. The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more](https://web.dev/lighthouse-max-potential-fid/).","sortKey":"numericValue","key":"report.audits.max-potential-fid"},{"cols":1,"label":"TBT","tooltip":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more](https://web.dev/lighthouse-total-blocking-time/).","sortKey":"numericValue","key":"report.audits.total-blocking-time"},{"cols":1,"label":"SI","sortKey":"numericValue","tooltip":"The speed index is a page load performance metric that shows you how quickly the contents of a page are visibly populated. [Learn more](https://web.dev/speed-index/).","key":"report.audits.speed-index"}],"accessibility":[{"cols":3,"label":"Color Contrast","tooltip":"Background and foreground colors do not have a sufficient contrast ratio.","sortKey":"length:details.items","key":"report.audits.color-contrast"},{"cols":1,"label":"Headings","tooltip":"Heading elements appear in a sequentially-descending order","sortKey":"length:details.items","key":"report.audits.heading-order"},{"cols":1,"label":"ARIA","tooltip":"An aggregate of all ARIA audits.","sortKey":"displayValue","sortable":true,"key":"report.computed.ariaIssues"},{"cols":1,"label":"Labels","tooltip":"Form elements have associated labels","sortKey":"length:details.items","key":"report.audits.label"},{"cols":1,"label":"Image Alts","tooltip":"Image elements have [alt] attributes","sortKey":"length:details.items","key":"report.audits.image-alt"},{"cols":1,"label":"Link Names","tooltip":"Links do not have a discernible name","sortKey":"length:details.items","key":"report.audits.link-name"}],"best-practices":[{"cols":2,"label":"Errors","tooltip":"No browser errors logged to the console","sortKey":"length:details.items","key":"report.audits.errors-in-console"},{"cols":2,"label":"Inspector Issues","tooltip":"No issues in the `Issues` panel in Chrome Devtools","sortKey":"length:details.items","key":"report.audits.inspector-issues"},{"cols":2,"label":"Images Responsive","tooltip":"Serves images with appropriate resolution","sortKey":"length:details.items","key":"report.audits.image-size-responsive"},{"cols":2,"label":"Image Aspect Ratio","tooltip":"Displays images with correct aspect ratio","sortKey":"length:details.items","key":"report.audits.image-aspect-ratio"}],"seo":[{"cols":1,"label":"Indexable","tooltip":"Page isn’t blocked from indexing","key":"report.audits.is-crawlable"},{"cols":1,"label":"Internal link","sortable":true,"key":"seo.internalLinks"},{"cols":1,"label":"External link","sortable":true,"key":"seo.externalLinks"},{"cols":2,"label":"Description","key":"seo.description"},{"cols":2,"label":"Share Image","key":"seo.og.image"}]}},"site":"https://ffdweb.org","lighthouseOptions":{"onlyCategories":["performance","accessibility","best-practices","seo"]},"scanner":{"customSampling":{},"ignoreI18nPages":true,"maxRoutes":200,"skipJavascript":true,"samples":3,"throttle":true,"crawler":false,"dynamicSampling":1,"sitemap":["https://ffdweb.org/sitemap.xml"],"robotsTxt":true,"device":"mobile","exclude":["/cdn-cgi/*"],"_robotsTxtRules":[]},"routerPrefix":"/site-audit-reports/ffdweb/"}}