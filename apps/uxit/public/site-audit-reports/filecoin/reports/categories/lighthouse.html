<!--
@license
Copyright 2018 Google LLC
SPDX-License-Identifier: Apache-2.0
-->
<!doctype html>
<html lang="en">
<head>
  <base href="/site-audit-reports/filecoin/">
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1">
  <link rel="icon" href='data:image/svg+xml;utf8,<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><path d="m14 7 10-7 10 7v10h5v7h-5l5 24H9l5-24H9v-7h5V7Z" fill="%23F63"/><path d="M31.561 24H14l-1.689 8.105L31.561 24ZM18.983 48H9l1.022-4.907L35.723 32.27l1.663 7.98L18.983 48Z" fill="%23FFA385"/><path fill="%23FF3" d="M20.5 10h7v7h-7z"/></svg>'>
  <title>Lighthouse Report</title>
  <style>body {margin: 0}</style>
</head>
<body>
  <noscript>Lighthouse report requires JavaScript. Please enable.</noscript>

  <div id="lh-log"></div>

  <script>window.__LIGHTHOUSE_JSON__ = {"lighthouseVersion":"12.5.1","requestedUrl":"https://filecoin.io/categories/","mainDocumentUrl":"https://filecoin.io/categories/","finalDisplayedUrl":"https://filecoin.io/categories/","finalUrl":"https://filecoin.io/categories/","fetchTime":"2025-05-06T14:37:19.614Z","gatherMode":"navigation","runWarnings":[],"userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36","environment":{"networkUserAgent":"Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","hostUserAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/********* Safari/537.36","benchmarkIndex":2897.5,"credits":{"axe-core":"4.10.3"}},"audits":{"is-on-https":{"id":"is-on-https","title":"Uses HTTPS","description":"All sites should be protected with HTTPS, even ones that don't handle sensitive data. This includes avoiding [mixed content](https://developers.google.com/web/fundamentals/security/prevent-mixed-content/what-is-mixed-content), where some resources are loaded over HTTP despite the initial request being served over HTTPS. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs. [Learn more about HTTPS](https://developer.chrome.com/docs/lighthouse/pwa/is-on-https/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"redirects-http":{"id":"redirects-http","title":"Redirects HTTP traffic to HTTPS","description":"Make sure that you redirect all HTTP traffic to HTTPS in order to enable secure web features for all your users. [Learn more](https://developer.chrome.com/docs/lighthouse/pwa/redirects-http/).","score":null,"scoreDisplayMode":"notApplicable"},"viewport":{"id":"viewport","title":"Does not have a `\u003cmeta name=\"viewport\">` tag with `width` or `initial-scale`","description":"A `\u003cmeta name=\"viewport\">` not only optimizes your app for mobile screen sizes, but also prevents [a 300 millisecond delay to user input](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/). [Learn more about using the viewport meta tag](https://developer.chrome.com/docs/lighthouse/pwa/viewport/).","score":0,"scoreDisplayMode":"metricSavings","explanation":"No `\u003cmeta name=\"viewport\">` tag found","metricSavings":{"INP":300},"guidanceLevel":3},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":1,"scoreDisplayMode":"numeric","numericValue":888.8973,"numericUnit":"millisecond","displayValue":"0.9 s","scoringOptions":{"p10":1800,"median":3000}},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"numeric","numericValue":888.8973,"numericUnit":"millisecond","displayValue":"0.9 s","scoringOptions":{"p10":2500,"median":4000}},"first-meaningful-paint":{"id":"first-meaningful-paint","title":"First Meaningful Paint","description":"First Meaningful Paint measures when the primary content of a page is visible. [Learn more about the First Meaningful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-meaningful-paint/).","score":null,"scoreDisplayMode":"notApplicable"},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":888.8973,"numericUnit":"millisecond","displayValue":"0.9 s","scoringOptions":{"p10":3387,"median":5800}},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181818980606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":750,"timestamp":181819355606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":1125,"timestamp":181819730606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":1500,"timestamp":181820105606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":1875,"timestamp":181820480606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":2250,"timestamp":181820855606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":2625,"timestamp":181821230606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"},{"timing":3000,"timestamp":181821605606,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"}]}},"final-screenshot":{"id":"final-screenshot","title":"Final Screenshot","description":"The last screenshot captured of the pageload.","score":1,"scoreDisplayMode":"informative","details":{"type":"screenshot","timing":231,"timestamp":181818836719,"data":"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAHyAPoDASIAAhEBAxEB/8QAGQABAQADAQAAAAAAAAAAAAAAAAcDBAUI/8QAJxABAAICAgIBAwQDAAAAAAAAAAECAwQFEQYSITFBURMUIjIjYXH/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAwDAQACEQMRAD8A9UXvXHS172itKx3NpnqIj8sFN3VyaFd6mzhtpWxfrRni8fpzj679/b6evXz3+HL8r1+Q2NfRjj65cmKu1W23hw7E4MmTD6Wjqt4mOpi80t9Y7isx9+pmW14V5XseJcjxWa+9a1uFjj9bBj5CldX51aY/W1eu5vGWtp9u+prafmf6yFi0tvX3tauxpZ8efBaZiMmO0WrMxMxMdx+JiY/7BTd1b72TSpsYrbeOlct8MXib1paZiLTH1iJmJ6n/AFKb7PDeW15rPn0/3tNClsdqYJ5G0zetMurbr5vMRM0psVn6RM2nuZ77aseJeQZuSrs7uHZyTSmpEXjfn3tGLfy5ZrN/aLT1iyV67+8fmIBS8vL8dh276uXf1abNJxRbFbLWLVnJaa4+4+se0xMR+Zj4byeeR8HzuXzjY5Pi8WWuvlrxWKcmPPXHNqYNrNkz1mPaJms48nXX3+YYvEfH/IdfybiuQ5udjNj19fbwTfJtza/+T9tNJvX3ms/OPLH8fj+tvWvfUBSAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/Z"}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":16,"numericUnit":"millisecond","displayValue":"20 ms"},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"errors-in-console":{"id":"errors-in-console","title":"No browser errors logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"server-response-time":{"id":"server-response-time","title":"Initial server response time was short","description":"Keep the server response time for the main document short because all other requests depend on it. [Learn more about the Time to First Byte metric](https://developer.chrome.com/docs/lighthouse/performance/time-to-first-byte/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":134.802,"numericUnit":"millisecond","displayValue":"Root document took 130 ms","metricSavings":{"FCP":50,"LCP":50},"details":{"type":"opportunity","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"responseTime","valueType":"timespanMs","label":"Time Spent"}],"items":[{"url":"https://filecoin.io/categories/","responseTime":134.802}],"overallSavingsMs":34.80199999999999},"guidanceLevel":1},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":1,"scoreDisplayMode":"numeric","numericValue":888.8973000000001,"numericUnit":"millisecond","displayValue":"0.9 s"},"user-timings":{"id":"user-timings","title":"User Timing marks and measures","description":"Consider instrumenting your app with the User Timing API to measure your app's real-world performance during key user experiences. [Learn more about User Timing marks](https://developer.chrome.com/docs/lighthouse/performance/user-timings/).","score":null,"scoreDisplayMode":"notApplicable","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"critical-request-chains":{"id":"critical-request-chains","title":"Avoid chaining critical requests","description":"The Critical Request Chains below show you what resources are loaded with a high priority. Consider reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load. [Learn how to avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains/).","score":null,"scoreDisplayMode":"notApplicable","displayValue":"","details":{"type":"criticalrequestchain","chains":{"5726E2839233723F03CA24C0CC6AEDB9":{"request":{"url":"https://filecoin.io/categories/","startTime":181818.607056,"endTime":181818.746025,"responseReceivedTime":181818.742412,"transferSize":1297}}},"longestChain":{"duration":138.9690000116825,"length":1,"transferSize":1297}},"guidanceLevel":1},"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"deprecations":{"id":"deprecations","title":"Avoids deprecated APIs","description":"Deprecated APIs will eventually be removed from the browser. [Learn more about deprecated APIs](https://developer.chrome.com/docs/lighthouse/best-practices/deprecations/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"third-party-cookies":{"id":"third-party-cookies","title":"Avoids third-party cookies","description":"Chrome is moving towards a new experience that allows users to choose to browse without third-party cookies. [Learn more about third-party cookies](https://developers.google.com/privacy-sandbox/cookies).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"mainthread-work-breakdown":{"id":"mainthread-work-breakdown","title":"Minimizes main-thread work","description":"Consider reducing the time spent parsing, compiling and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to minimize main-thread work](https://developer.chrome.com/docs/lighthouse/performance/mainthread-work-breakdown/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":15.797999999999995,"numericUnit":"millisecond","displayValue":"0.0 s","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"groupLabel","valueType":"text","label":"Category"},{"key":"duration","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"group":"other","groupLabel":"Other","duration":9.007999999999996},{"group":"styleLayout","groupLabel":"Style & Layout","duration":4.768},{"group":"scriptEvaluation","groupLabel":"Script Evaluation","duration":1.5429999999999997},{"group":"paintCompositeRender","groupLabel":"Rendering","duration":0.341},{"group":"scriptParseCompile","groupLabel":"Script Parsing & Compilation","duration":0.08},{"group":"parseHTML","groupLabel":"Parse HTML & CSS","duration":0.058}],"sortedBy":["duration"]},"guidanceLevel":1},"bootup-time":{"id":"bootup-time","title":"JavaScript execution time","description":"Consider reducing the time spent parsing, compiling, and executing JS. You may find delivering smaller JS payloads helps with this. [Learn how to reduce Javascript execution time](https://developer.chrome.com/docs/lighthouse/performance/bootup-time/).","score":null,"scoreDisplayMode":"notApplicable","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"TBT":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":1},"uses-rel-preconnect":{"id":"uses-rel-preconnect","title":"Preconnect to required origins","description":"Consider adding `preconnect` or `dns-prefetch` resource hints to establish early connections to important third-party origins. [Learn how to preconnect to required origins](https://developer.chrome.com/docs/lighthouse/performance/uses-rel-preconnect/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"sortedBy":["wastedMs"]},"guidanceLevel":3},"font-display":{"id":"font-display","title":"All text remains visible during webfont loads","description":"Leverage the `font-display` CSS feature to ensure text is user-visible while webfonts are loading. [Learn more about `font-display`](https://developer.chrome.com/docs/lighthouse/performance/font-display/).","score":1,"scoreDisplayMode":"metricSavings","warnings":[],"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"diagnostics":{"id":"diagnostics","title":"Diagnostics","description":"Collection of useful page vitals.","score":1,"scoreDisplayMode":"informative","details":{"type":"debugdata","items":[{"numRequests":1,"numScripts":0,"numStylesheets":0,"numFonts":0,"numTasks":108,"numTasksOver10ms":0,"numTasksOver25ms":0,"numTasksOver50ms":0,"numTasksOver100ms":0,"numTasksOver500ms":0,"rtt":0.0277,"throughput":2871851.640436377,"maxRtt":0.0277,"maxServerLatency":134.89729999999997,"totalByteWeight":1297,"totalTaskTime":15.797999999999993,"mainDocumentTransferSize":1297}]}},"network-requests":{"id":"network-requests","title":"Network Requests","description":"Lists the network requests that were made during page load.","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"protocol","valueType":"text","label":"Protocol"},{"key":"networkRequestTime","valueType":"ms","granularity":1,"label":"Network Request Time"},{"key":"networkEndTime","valueType":"ms","granularity":1,"label":"Network End Time"},{"key":"transferSize","valueType":"bytes","displayUnit":"kb","granularity":1,"label":"Transfer Size"},{"key":"resourceSize","valueType":"bytes","displayUnit":"kb","granularity":1,"label":"Resource Size"},{"key":"statusCode","valueType":"text","label":"Status Code"},{"key":"mimeType","valueType":"text","label":"MIME Type"},{"key":"resourceType","valueType":"text","label":"Resource Type"}],"items":[{"url":"https://filecoin.io/categories/","sessionTargetType":"page","protocol":"h2","rendererStartTime":0,"networkRequestTime":0.7899999916553497,"networkEndTime":139.75900000333786,"finished":true,"transferSize":1297,"resourceSize":12,"statusCode":200,"mimeType":"text/html","resourceType":"Document","priority":"VeryHigh","experimentalFromMainFrame":true,"entity":"filecoin.io"}],"debugData":{"type":"debugdata","networkStartTimeTs":181818606266}}},"network-rtt":{"id":"network-rtt","title":"Network Round Trip Times","description":"Network round trip times (RTT) have a large impact on performance. If the RTT to an origin is high, it's an indication that servers closer to the user could improve performance. [Learn more about the Round Trip Time](https://hpbn.co/primer-on-latency-and-bandwidth/).","score":1,"scoreDisplayMode":"informative","numericValue":0.0277,"numericUnit":"millisecond","displayValue":"0 ms","details":{"type":"table","headings":[{"key":"origin","valueType":"text","label":"URL"},{"key":"rtt","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"origin":"https://filecoin.io","rtt":0.0277}],"sortedBy":["rtt"]}},"network-server-latency":{"id":"network-server-latency","title":"Server Backend Latencies","description":"Server latencies can impact web performance. If the server latency of an origin is high, it's an indication the server is overloaded or has poor backend performance. [Learn more about server response time](https://hpbn.co/primer-on-web-performance/#analyzing-the-resource-waterfall).","score":1,"scoreDisplayMode":"informative","numericValue":134.89729999999997,"numericUnit":"millisecond","displayValue":"130 ms","details":{"type":"table","headings":[{"key":"origin","valueType":"text","label":"URL"},{"key":"serverResponseTime","valueType":"ms","granularity":1,"label":"Time Spent"}],"items":[{"origin":"https://filecoin.io","serverResponseTime":134.89729999999997}],"sortedBy":["serverResponseTime"]}},"main-thread-tasks":{"id":"main-thread-tasks","title":"Tasks","description":"Lists the toplevel main thread tasks that executed during page load.","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"startTime","valueType":"ms","granularity":1,"label":"Start Time"},{"key":"duration","valueType":"ms","granularity":1,"label":"End Time"}],"items":[{"duration":5.318,"startTime":142.345}]}},"metrics":{"id":"metrics","title":"Metrics","description":"Collects all available metrics.","score":1,"scoreDisplayMode":"informative","numericValue":889,"numericUnit":"millisecond","details":{"type":"debugdata","items":[{"firstContentfulPaint":889,"largestContentfulPaint":889,"interactive":889,"speedIndex":889,"totalBlockingTime":0,"maxPotentialFID":16,"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0,"timeToFirstByte":735,"observedTimeOrigin":0,"observedTimeOriginTs":181818605606,"observedNavigationStart":0,"observedNavigationStartTs":181818605606,"observedFirstPaint":185,"observedFirstPaintTs":181818790501,"observedFirstContentfulPaint":185,"observedFirstContentfulPaintTs":181818790501,"observedFirstContentfulPaintAllFrames":185,"observedFirstContentfulPaintAllFramesTs":181818790501,"observedLargestContentfulPaint":185,"observedLargestContentfulPaintTs":181818790501,"observedLargestContentfulPaintAllFrames":185,"observedLargestContentfulPaintAllFramesTs":181818790501,"observedTraceEnd":2549,"observedTraceEndTs":181821154338,"observedLoad":143,"observedLoadTs":181818748435,"observedDomContentLoaded":143,"observedDomContentLoadedTs":181818748197,"observedCumulativeLayoutShift":0,"observedCumulativeLayoutShiftMainFrame":0,"observedFirstVisualChange":166,"observedFirstVisualChangeTs":181818771606,"observedLastVisualChange":231,"observedLastVisualChangeTs":181818836606,"observedSpeedIndex":218,"observedSpeedIndexTs":181818823765},{"lcpInvalidated":false}]}},"resource-summary":{"id":"resource-summary","title":"Resources Summary","description":"Aggregates all network requests and groups them by type","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"label","valueType":"text","label":"Resource Type"},{"key":"requestCount","valueType":"numeric","label":"Requests"},{"key":"transferSize","valueType":"bytes","label":"Transfer Size"}],"items":[{"resourceType":"document","label":"Document","requestCount":1,"transferSize":1297},{"resourceType":"total","label":"Total","requestCount":1,"transferSize":1297},{"resourceType":"stylesheet","label":"Stylesheet","requestCount":0,"transferSize":0},{"resourceType":"image","label":"Image","requestCount":0,"transferSize":0},{"resourceType":"media","label":"Media","requestCount":0,"transferSize":0},{"resourceType":"font","label":"Font","requestCount":0,"transferSize":0},{"resourceType":"script","label":"Script","requestCount":0,"transferSize":0},{"resourceType":"other","label":"Other","requestCount":0,"transferSize":0},{"resourceType":"third-party","label":"Third-party","requestCount":0,"transferSize":0}]}},"third-party-summary":{"id":"third-party-summary","title":"Minimize third-party usage","description":"Third-party code can significantly impact load performance. Limit the number of redundant third-party providers and try to load third-party code after your page has primarily finished loading. [Learn how to minimize third-party impact](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"TBT":0},"guidanceLevel":1},"third-party-facades":{"id":"third-party-facades","title":"Lazy load third-party resources with facades","description":"Some third-party embeds can be lazy loaded. Consider replacing them with a facade until they are required. [Learn how to defer third-parties with a facade](https://developer.chrome.com/docs/lighthouse/performance/third-party-facades/).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"TBT":0},"guidanceLevel":3},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":1,"scoreDisplayMode":"informative","displayValue":"890 ms","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-BODY","path":"0,HTML,1,BODY","selector":"body","boundingRect":{"top":8,"bottom":815,"left":8,"right":404,"width":396,"height":807},"snippet":"\u003cbody>","nodeLabel":"body"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":734.8973,"percent":"83%"},{"phase":"Load Delay","timing":0,"percent":"0%"},{"phase":"Load Time","timing":0,"percent":"0%"},{"phase":"Render Delay","timing":154,"percent":"17%"}]}]},"guidanceLevel":1},"lcp-lazy-loaded":{"id":"lcp-lazy-loaded","title":"Largest Contentful Paint image was not lazily loaded","description":"Above-the-fold images that are lazily loaded render later in the page lifecycle, which can delay the largest contentful paint. [Learn more about optimal lazy loading](https://web.dev/articles/lcp-lazy-loading).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"LCP":0},"guidanceLevel":3},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"long-tasks":{"id":"long-tasks","title":"Avoid long main-thread tasks","description":"Lists the longest tasks on the main thread, useful for identifying worst contributors to input delay. [Learn how to avoid long main-thread tasks](https://web.dev/articles/optimize-long-tasks)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"TBT":0},"details":{"type":"table","headings":[],"items":[],"debugData":{"type":"debugdata","urls":[],"tasks":[]}},"guidanceLevel":1},"non-composited-animations":{"id":"non-composited-animations","title":"Avoid non-composited animations","description":"Animations which are not composited can be janky and increase CLS. [Learn how to avoid non-composited animations](https://developer.chrome.com/docs/lighthouse/performance/non-composited-animations/)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"unsized-images":{"id":"unsized-images","title":"Image elements have explicit `width` and `height`","description":"Set an explicit width and height on image elements to reduce layout shifts and improve CLS. [Learn how to set image dimensions](https://web.dev/articles/optimize-cls#images_without_dimensions)","score":1,"scoreDisplayMode":"metricSavings","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":4},"valid-source-maps":{"id":"valid-source-maps","title":"Page has valid source maps","description":"Source maps translate minified code to the original source code. This helps developers debug in production. In addition, Lighthouse is able to provide further insights. Consider deploying source maps to take advantage of these benefits. [Learn more about source maps](https://developer.chrome.com/docs/devtools/javascript/source-maps/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"prioritize-lcp-image":{"id":"prioritize-lcp-image","title":"Preload Largest Contentful Paint image","description":"If the LCP element is dynamically added to the page, you should preload the image in order to improve LCP. [Learn more about preloading LCP elements](https://web.dev/articles/optimize-lcp#optimize_when_the_resource_is_discovered).","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"LCP":0},"guidanceLevel":4},"csp-xss":{"id":"csp-xss","title":"Ensure CSP is effective against XSS attacks","description":"A strong Content Security Policy (CSP) significantly reduces the risk of cross-site scripting (XSS) attacks. [Learn how to use a CSP to prevent XSS](https://developer.chrome.com/docs/lighthouse/best-practices/csp-xss/)","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"description","valueType":"text","subItemsHeading":{"key":"description"},"label":"Description"},{"key":"directive","valueType":"code","subItemsHeading":{"key":"directive"},"label":"Directive"},{"key":"severity","valueType":"text","subItemsHeading":{"key":"severity"},"label":"Severity"}],"items":[{"directive":"script-src","description":"`script-src` directive is missing. This can allow the execution of unsafe scripts.","severity":"High"},{"directive":"object-src","description":"Missing `object-src` allows the injection of plugins that execute unsafe scripts. Consider setting `object-src` to `'none'` if you can.","severity":"High"}]}},"has-hsts":{"id":"has-hsts","title":"Use a strong HSTS policy","description":"Deployment of the HSTS header significantly reduces the risk of downgrading HTTP connections and eavesdropping attacks. A rollout in stages, starting with a low max-age is recommended. [Learn more about using a strong HSTS policy.](https://developer.chrome.com/docs/lighthouse/best-practices/has-hsts)","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"description","valueType":"text","subItemsHeading":{"key":"description"},"label":"Description"},{"key":"directive","valueType":"code","subItemsHeading":{"key":"directive"},"label":"Directive"},{"key":"severity","valueType":"text","subItemsHeading":{"key":"severity"},"label":"Severity"}],"items":[{"directive":"preload","description":"No `preload` directive found","severity":"Medium"}]}},"origin-isolation":{"id":"origin-isolation","title":"Ensure proper origin isolation with COOP","description":"The Cross-Origin-Opener-Policy (COOP) can be used to isolate the top-level window from other documents such as pop-ups. [Learn more about deploying the COOP header.](https://web.dev/articles/why-coop-coep#coop)","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"description","valueType":"text","subItemsHeading":{"key":"description"},"label":"Description"},{"key":"directive","valueType":"code","subItemsHeading":{"key":"directive"},"label":"Directive"},{"key":"severity","valueType":"text","subItemsHeading":{"key":"severity"},"label":"Severity"}],"items":[{"description":"No COOP header found","severity":"High"}]}},"clickjacking-mitigation":{"id":"clickjacking-mitigation","title":"Mitigate clickjacking with XFO or CSP","description":"The `X-Frame-Options` (XFO) header or the `frame-ancestors` directive in the `Content-Security-Policy` (CSP) header control where a page can be embedded. These can mitigate clickjacking attacks by blocking some or all sites from embedding the page. [Learn more about mitigating clickjacking](https://developer.chrome.com/docs/lighthouse/best-practices/clickjacking-mitigation).","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"description","valueType":"text","subItemsHeading":{"key":"description"},"label":"Description"},{"key":"severity","valueType":"text","subItemsHeading":{"key":"severity"},"label":"Severity"}],"items":[{"severity":"High","description":"No frame control policy found"}]}},"script-treemap-data":{"id":"script-treemap-data","title":"Script Treemap Data","description":"Used for treemap app","score":1,"scoreDisplayMode":"informative","details":{"type":"treemap-data","nodes":[]}},"accesskeys":{"id":"accesskeys","title":"`[accesskey]` values are unique","description":"Access keys let users quickly focus a part of the page. For proper navigation, each access key must be unique. [Learn more about access keys](https://dequeuniversity.com/rules/axe/4.10/accesskeys).","score":null,"scoreDisplayMode":"notApplicable"},"aria-allowed-attr":{"id":"aria-allowed-attr","title":"`[aria-*]` attributes match their roles","description":"Each ARIA `role` supports a specific subset of `aria-*` attributes. Mismatching these invalidates the `aria-*` attributes. [Learn how to match ARIA attributes to their roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-attr).","score":null,"scoreDisplayMode":"notApplicable"},"aria-allowed-role":{"id":"aria-allowed-role","title":"Uses ARIA roles only on compatible elements","description":"Many HTML elements can only be assigned certain ARIA roles. Using ARIA roles where they are not allowed can interfere with the accessibility of the web page. [Learn more about ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-allowed-role).","score":null,"scoreDisplayMode":"notApplicable"},"aria-command-name":{"id":"aria-command-name","title":"`button`, `link`, and `menuitem` elements have accessible names","description":"When an element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to make command elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-command-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-conditional-attr":{"id":"aria-conditional-attr","title":"ARIA attributes are used as specified for the element's role","description":"Some ARIA attributes are only allowed on an element under certain conditions. [Learn more about conditional ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-conditional-attr).","score":null,"scoreDisplayMode":"notApplicable"},"aria-deprecated-role":{"id":"aria-deprecated-role","title":"Deprecated ARIA roles were not used","description":"Deprecated ARIA roles may not be processed correctly by assistive technology. [Learn more about deprecated ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-deprecated-role).","score":null,"scoreDisplayMode":"notApplicable"},"aria-dialog-name":{"id":"aria-dialog-name","title":"Elements with `role=\"dialog\"` or `role=\"alertdialog\"` have accessible names.","description":"ARIA dialog elements without accessible names may prevent screen readers users from discerning the purpose of these elements. [Learn how to make ARIA dialog elements more accessible](https://dequeuniversity.com/rules/axe/4.10/aria-dialog-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-hidden-body":{"id":"aria-hidden-body","title":"`[aria-hidden=\"true\"]` is not present on the document `\u003cbody>`","description":"Assistive technologies, like screen readers, work inconsistently when `aria-hidden=\"true\"` is set on the document `\u003cbody>`. [Learn how `aria-hidden` affects the document body](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-body).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"aria-hidden-focus":{"id":"aria-hidden-focus","title":"`[aria-hidden=\"true\"]` elements do not contain focusable descendents","description":"Focusable descendents within an `[aria-hidden=\"true\"]` element prevent those interactive elements from being available to users of assistive technologies like screen readers. [Learn how `aria-hidden` affects focusable elements](https://dequeuniversity.com/rules/axe/4.10/aria-hidden-focus).","score":null,"scoreDisplayMode":"notApplicable"},"aria-input-field-name":{"id":"aria-input-field-name","title":"ARIA input fields have accessible names","description":"When an input field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about input field labels](https://dequeuniversity.com/rules/axe/4.10/aria-input-field-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-meter-name":{"id":"aria-meter-name","title":"ARIA `meter` elements have accessible names","description":"When a meter element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `meter` elements](https://dequeuniversity.com/rules/axe/4.10/aria-meter-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-progressbar-name":{"id":"aria-progressbar-name","title":"ARIA `progressbar` elements have accessible names","description":"When a `progressbar` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to label `progressbar` elements](https://dequeuniversity.com/rules/axe/4.10/aria-progressbar-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-prohibited-attr":{"id":"aria-prohibited-attr","title":"Elements use only permitted ARIA attributes","description":"Using ARIA attributes in roles where they are prohibited can mean that important information is not communicated to users of assistive technologies. [Learn more about prohibited ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-prohibited-attr).","score":null,"scoreDisplayMode":"notApplicable"},"aria-required-attr":{"id":"aria-required-attr","title":"`[role]`s have all required `[aria-*]` attributes","description":"Some ARIA roles have required attributes that describe the state of the element to screen readers. [Learn more about roles and required attributes](https://dequeuniversity.com/rules/axe/4.10/aria-required-attr).","score":null,"scoreDisplayMode":"notApplicable"},"aria-required-children":{"id":"aria-required-children","title":"Elements with an ARIA `[role]` that require children to contain a specific `[role]` have all required children.","description":"Some ARIA parent roles must contain specific child roles to perform their intended accessibility functions. [Learn more about roles and required children elements](https://dequeuniversity.com/rules/axe/4.10/aria-required-children).","score":null,"scoreDisplayMode":"notApplicable"},"aria-required-parent":{"id":"aria-required-parent","title":"`[role]`s are contained by their required parent element","description":"Some ARIA child roles must be contained by specific parent roles to properly perform their intended accessibility functions. [Learn more about ARIA roles and required parent element](https://dequeuniversity.com/rules/axe/4.10/aria-required-parent).","score":null,"scoreDisplayMode":"notApplicable"},"aria-roles":{"id":"aria-roles","title":"`[role]` values are valid","description":"ARIA roles must have valid values in order to perform their intended accessibility functions. [Learn more about valid ARIA roles](https://dequeuniversity.com/rules/axe/4.10/aria-roles).","score":null,"scoreDisplayMode":"notApplicable"},"aria-text":{"id":"aria-text","title":"Elements with the `role=text` attribute do not have focusable descendents.","description":"Adding `role=text` around a text node split by markup enables VoiceOver to treat it as one phrase, but the element's focusable descendents will not be announced. [Learn more about the `role=text` attribute](https://dequeuniversity.com/rules/axe/4.10/aria-text).","score":null,"scoreDisplayMode":"notApplicable"},"aria-toggle-field-name":{"id":"aria-toggle-field-name","title":"ARIA toggle fields have accessible names","description":"When a toggle field doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about toggle fields](https://dequeuniversity.com/rules/axe/4.10/aria-toggle-field-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-tooltip-name":{"id":"aria-tooltip-name","title":"ARIA `tooltip` elements have accessible names","description":"When a tooltip element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn how to name `tooltip` elements](https://dequeuniversity.com/rules/axe/4.10/aria-tooltip-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-treeitem-name":{"id":"aria-treeitem-name","title":"ARIA `treeitem` elements have accessible names","description":"When a `treeitem` element doesn't have an accessible name, screen readers announce it with a generic name, making it unusable for users who rely on screen readers. [Learn more about labeling `treeitem` elements](https://dequeuniversity.com/rules/axe/4.10/aria-treeitem-name).","score":null,"scoreDisplayMode":"notApplicable"},"aria-valid-attr-value":{"id":"aria-valid-attr-value","title":"`[aria-*]` attributes have valid values","description":"Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid values. [Learn more about valid values for ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr-value).","score":null,"scoreDisplayMode":"notApplicable"},"aria-valid-attr":{"id":"aria-valid-attr","title":"`[aria-*]` attributes are valid and not misspelled","description":"Assistive technologies, like screen readers, can't interpret ARIA attributes with invalid names. [Learn more about valid ARIA attributes](https://dequeuniversity.com/rules/axe/4.10/aria-valid-attr).","score":null,"scoreDisplayMode":"notApplicable"},"button-name":{"id":"button-name","title":"Buttons have an accessible name","description":"When a button doesn't have an accessible name, screen readers announce it as \"button\", making it unusable for users who rely on screen readers. [Learn how to make buttons more accessible](https://dequeuniversity.com/rules/axe/4.10/button-name).","score":null,"scoreDisplayMode":"notApplicable"},"bypass":{"id":"bypass","title":"The page contains a heading, skip link, or landmark region","description":"Adding ways to bypass repetitive content lets keyboard users navigate the page more efficiently. [Learn more about bypass blocks](https://dequeuniversity.com/rules/axe/4.10/bypass).","score":null,"scoreDisplayMode":"notApplicable"},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors have a sufficient contrast ratio","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"definition-list":{"id":"definition-list","title":"`\u003cdl>`'s contain only properly-ordered `\u003cdt>` and `\u003cdd>` groups, `\u003cscript>`, `\u003ctemplate>` or `\u003cdiv>` elements.","description":"When definition lists are not properly marked up, screen readers may produce confusing or inaccurate output. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/definition-list).","score":null,"scoreDisplayMode":"notApplicable"},"dlitem":{"id":"dlitem","title":"Definition list items are wrapped in `\u003cdl>` elements","description":"Definition list items (`\u003cdt>` and `\u003cdd>`) must be wrapped in a parent `\u003cdl>` element to ensure that screen readers can properly announce them. [Learn how to structure definition lists correctly](https://dequeuniversity.com/rules/axe/4.10/dlitem).","score":null,"scoreDisplayMode":"notApplicable"},"document-title":{"id":"document-title","title":"Document doesn't have a `\u003ctitle>` element","description":"The title gives screen reader users an overview of the page, and search engine users rely on it heavily to determine if a page is relevant to their search. [Learn more about document titles](https://dequeuniversity.com/rules/axe/4.10/document-title).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-HTML","path":"0,HTML","selector":"html","boundingRect":{"top":0,"bottom":823,"left":0,"right":412,"width":412,"height":823},"snippet":"\u003chtml>","nodeLabel":"html","explanation":"Fix any of the following:\n  Document does not have a non-empty \u003ctitle> element"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.text-alternatives","wcag2a","wcag242","TTv5","TT12.a","EN-301-549","EN-*******","ACT"]}}},"duplicate-id-aria":{"id":"duplicate-id-aria","title":"ARIA IDs are unique","description":"The value of an ARIA ID must be unique to prevent other instances from being overlooked by assistive technologies. [Learn how to fix duplicate ARIA IDs](https://dequeuniversity.com/rules/axe/4.10/duplicate-id-aria).","score":null,"scoreDisplayMode":"notApplicable"},"empty-heading":{"id":"empty-heading","title":"All heading elements contain content.","description":"A heading with no content or inaccessible text prevent screen reader users from accessing information on the page's structure. [Learn more about headings](https://dequeuniversity.com/rules/axe/4.10/empty-heading).","score":null,"scoreDisplayMode":"notApplicable"},"form-field-multiple-labels":{"id":"form-field-multiple-labels","title":"No form fields have multiple labels","description":"Form fields with multiple labels can be confusingly announced by assistive technologies like screen readers which use either the first, the last, or all of the labels. [Learn how to use form labels](https://dequeuniversity.com/rules/axe/4.10/form-field-multiple-labels).","score":null,"scoreDisplayMode":"notApplicable"},"frame-title":{"id":"frame-title","title":"`\u003cframe>` or `\u003ciframe>` elements have a title","description":"Screen reader users rely on frame titles to describe the contents of frames. [Learn more about frame titles](https://dequeuniversity.com/rules/axe/4.10/frame-title).","score":null,"scoreDisplayMode":"notApplicable"},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":null,"scoreDisplayMode":"notApplicable"},"html-has-lang":{"id":"html-has-lang","title":"`\u003chtml>` element does not have a `[lang]` attribute","description":"If a page doesn't specify a `lang` attribute, a screen reader assumes that the page is in the default language that the user chose when setting up the screen reader. If the page isn't actually in the default language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-has-lang).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-HTML","path":"0,HTML","selector":"html","boundingRect":{"top":0,"bottom":823,"left":0,"right":412,"width":412,"height":823},"snippet":"\u003chtml>","nodeLabel":"html","explanation":"Fix any of the following:\n  The \u003chtml> element does not have a lang attribute"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.language","wcag2a","wcag311","TTv5","TT11.a","EN-301-549","EN-*******","ACT"]}}},"html-lang-valid":{"id":"html-lang-valid","title":"`\u003chtml>` element has a valid value for its `[lang]` attribute","description":"Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) helps screen readers announce text properly. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-lang-valid).","score":null,"scoreDisplayMode":"notApplicable"},"html-xml-lang-mismatch":{"id":"html-xml-lang-mismatch","title":"`\u003chtml>` element has an `[xml:lang]` attribute with the same base language as the `[lang]` attribute.","description":"If the webpage does not specify a consistent language, then the screen reader might not announce the page's text correctly. [Learn more about the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/html-xml-lang-mismatch).","score":null,"scoreDisplayMode":"notApplicable"},"identical-links-same-purpose":{"id":"identical-links-same-purpose","title":"Identical links have the same purpose.","description":"Links with the same destination should have the same description, to help users understand the link's purpose and decide whether to follow it. [Learn more about identical links](https://dequeuniversity.com/rules/axe/4.10/identical-links-same-purpose).","score":null,"scoreDisplayMode":"notApplicable"},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"image-redundant-alt":{"id":"image-redundant-alt","title":"Image elements do not have `[alt]` attributes that are redundant text.","description":"Informative elements should aim for short, descriptive alternative text. Alternative text that is exactly the same as the text adjacent to the link or image is potentially confusing for screen reader users, because the text will be read twice. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-redundant-alt).","score":null,"scoreDisplayMode":"notApplicable"},"input-button-name":{"id":"input-button-name","title":"Input buttons have discernible text.","description":"Adding discernable and accessible text to input buttons may help screen reader users understand the purpose of the input button. [Learn more about input buttons](https://dequeuniversity.com/rules/axe/4.10/input-button-name).","score":null,"scoreDisplayMode":"notApplicable"},"input-image-alt":{"id":"input-image-alt","title":"`\u003cinput type=\"image\">` elements have `[alt]` text","description":"When an image is being used as an `\u003cinput>` button, providing alternative text can help screen reader users understand the purpose of the button. [Learn about input image alt text](https://dequeuniversity.com/rules/axe/4.10/input-image-alt).","score":null,"scoreDisplayMode":"notApplicable"},"label-content-name-mismatch":{"id":"label-content-name-mismatch","title":"Elements with visible text labels have matching accessible names.","description":"Visible text labels that do not match the accessible name can result in a confusing experience for screen reader users. [Learn more about accessible names](https://dequeuniversity.com/rules/axe/4.10/label-content-name-mismatch).","score":null,"scoreDisplayMode":"notApplicable"},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":null,"scoreDisplayMode":"notApplicable"},"landmark-one-main":{"id":"landmark-one-main","title":"Document has a main landmark.","description":"One main landmark helps screen reader users navigate a web page. [Learn more about landmarks](https://dequeuniversity.com/rules/axe/4.10/landmark-one-main).","score":1,"scoreDisplayMode":"informative","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-HTML","path":"0,HTML","selector":"html","boundingRect":{"top":0,"bottom":823,"left":0,"right":412,"width":412,"height":823},"snippet":"\u003chtml>","nodeLabel":"html","explanation":"Fix all of the following:\n  Document does not have a main landmark"}}],"debugData":{"type":"debugdata","impact":"moderate","tags":["cat.semantics","best-practice"]}}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"link-in-text-block":{"id":"link-in-text-block","title":"Links are distinguishable without relying on color.","description":"Low-contrast text is difficult or impossible for many users to read. Link text that is discernible improves the experience for users with low vision. [Learn how to make links distinguishable](https://dequeuniversity.com/rules/axe/4.10/link-in-text-block).","score":null,"scoreDisplayMode":"notApplicable"},"list":{"id":"list","title":"Lists contain only `\u003cli>` elements and script supporting elements (`\u003cscript>` and `\u003ctemplate>`).","description":"Screen readers have a specific way of announcing lists. Ensuring proper list structure aids screen reader output. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/list).","score":null,"scoreDisplayMode":"notApplicable"},"listitem":{"id":"listitem","title":"List items (`\u003cli>`) are contained within `\u003cul>`, `\u003col>` or `\u003cmenu>` parent elements","description":"Screen readers require list items (`\u003cli>`) to be contained within a parent `\u003cul>`, `\u003col>` or `\u003cmenu>` to be announced properly. [Learn more about proper list structure](https://dequeuniversity.com/rules/axe/4.10/listitem).","score":null,"scoreDisplayMode":"notApplicable"},"meta-refresh":{"id":"meta-refresh","title":"The document does not use `\u003cmeta http-equiv=\"refresh\">`","description":"Users do not expect a page to refresh automatically, and doing so will move focus back to the top of the page. This may create a frustrating or confusing experience. [Learn more about the refresh meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-refresh).","score":null,"scoreDisplayMode":"notApplicable"},"meta-viewport":{"id":"meta-viewport","title":"`[user-scalable=\"no\"]` is not used in the `\u003cmeta name=\"viewport\">` element and the `[maximum-scale]` attribute is not less than 5.","description":"Disabling zooming is problematic for users with low vision who rely on screen magnification to properly see the contents of a web page. [Learn more about the viewport meta tag](https://dequeuniversity.com/rules/axe/4.10/meta-viewport).","score":null,"scoreDisplayMode":"notApplicable"},"object-alt":{"id":"object-alt","title":"`\u003cobject>` elements have alternate text","description":"Screen readers cannot translate non-text content. Adding alternate text to `\u003cobject>` elements helps screen readers convey meaning to users. [Learn more about alt text for `object` elements](https://dequeuniversity.com/rules/axe/4.10/object-alt).","score":null,"scoreDisplayMode":"notApplicable"},"select-name":{"id":"select-name","title":"Select elements have associated label elements.","description":"Form elements without effective labels can create frustrating experiences for screen reader users. [Learn more about the `select` element](https://dequeuniversity.com/rules/axe/4.10/select-name).","score":null,"scoreDisplayMode":"notApplicable"},"skip-link":{"id":"skip-link","title":"Skip links are focusable.","description":"Including a skip link can help users skip to the main content to save time. [Learn more about skip links](https://dequeuniversity.com/rules/axe/4.10/skip-link).","score":null,"scoreDisplayMode":"notApplicable"},"tabindex":{"id":"tabindex","title":"No element has a `[tabindex]` value greater than 0","description":"A value greater than 0 implies an explicit navigation ordering. Although technically valid, this often creates frustrating experiences for users who rely on assistive technologies. [Learn more about the `tabindex` attribute](https://dequeuniversity.com/rules/axe/4.10/tabindex).","score":null,"scoreDisplayMode":"notApplicable"},"table-duplicate-name":{"id":"table-duplicate-name","title":"Tables have different content in the summary attribute and `\u003ccaption>`.","description":"The summary attribute should describe the table structure, while `\u003ccaption>` should have the onscreen title. Accurate table mark-up helps users of screen readers. [Learn more about summary and caption](https://dequeuniversity.com/rules/axe/4.10/table-duplicate-name).","score":null,"scoreDisplayMode":"notApplicable"},"table-fake-caption":{"id":"table-fake-caption","title":"Tables use `\u003ccaption>` instead of cells with the `[colspan]` attribute to indicate a caption.","description":"Screen readers have features to make navigating tables easier. Ensuring that tables use the actual caption element instead of cells with the `[colspan]` attribute may improve the experience for screen reader users. [Learn more about captions](https://dequeuniversity.com/rules/axe/4.10/table-fake-caption).","score":null,"scoreDisplayMode":"notApplicable"},"target-size":{"id":"target-size","title":"Touch targets have sufficient size and spacing.","description":"Touch targets with sufficient size and spacing help users who may have difficulty targeting small controls to activate the targets. [Learn more about touch targets](https://dequeuniversity.com/rules/axe/4.10/target-size).","score":null,"scoreDisplayMode":"notApplicable"},"td-has-header":{"id":"td-has-header","title":"`\u003ctd>` elements in a large `\u003ctable>` have one or more table headers.","description":"Screen readers have features to make navigating tables easier. Ensuring that `\u003ctd>` elements in a large table (3 or more cells in width and height) have an associated table header may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/td-has-header).","score":null,"scoreDisplayMode":"notApplicable"},"td-headers-attr":{"id":"td-headers-attr","title":"Cells in a `\u003ctable>` element that use the `[headers]` attribute refer to table cells within the same table.","description":"Screen readers have features to make navigating tables easier. Ensuring `\u003ctd>` cells using the `[headers]` attribute only refer to other cells in the same table may improve the experience for screen reader users. [Learn more about the `headers` attribute](https://dequeuniversity.com/rules/axe/4.10/td-headers-attr).","score":null,"scoreDisplayMode":"notApplicable"},"th-has-data-cells":{"id":"th-has-data-cells","title":"`\u003cth>` elements and elements with `[role=\"columnheader\"/\"rowheader\"]` have data cells they describe.","description":"Screen readers have features to make navigating tables easier. Ensuring table headers always refer to some set of cells may improve the experience for screen reader users. [Learn more about table headers](https://dequeuniversity.com/rules/axe/4.10/th-has-data-cells).","score":null,"scoreDisplayMode":"notApplicable"},"valid-lang":{"id":"valid-lang","title":"`[lang]` attributes have a valid value","description":"Specifying a valid [BCP 47 language](https://www.w3.org/International/questions/qa-choosing-language-tags#question) on elements helps ensure that text is pronounced correctly by a screen reader. [Learn how to use the `lang` attribute](https://dequeuniversity.com/rules/axe/4.10/valid-lang).","score":null,"scoreDisplayMode":"notApplicable"},"video-caption":{"id":"video-caption","title":"`\u003cvideo>` elements contain a `\u003ctrack>` element with `[kind=\"captions\"]`","description":"When a video provides a caption it is easier for deaf and hearing impaired users to access its information. [Learn more about video captions](https://dequeuniversity.com/rules/axe/4.10/video-caption).","score":null,"scoreDisplayMode":"notApplicable"},"custom-controls-labels":{"id":"custom-controls-labels","title":"Custom controls have associated labels","description":"Custom interactive controls have associated labels, provided by aria-label or aria-labelledby. [Learn more about custom controls and labels](https://developer.chrome.com/docs/lighthouse/accessibility/custom-controls-labels/).","score":null,"scoreDisplayMode":"manual"},"custom-controls-roles":{"id":"custom-controls-roles","title":"Custom controls have ARIA roles","description":"Custom interactive controls have appropriate ARIA roles. [Learn how to add roles to custom controls](https://developer.chrome.com/docs/lighthouse/accessibility/custom-control-roles/).","score":null,"scoreDisplayMode":"manual"},"focus-traps":{"id":"focus-traps","title":"User focus is not accidentally trapped in a region","description":"A user can tab into and out of any control or region without accidentally trapping their focus. [Learn how to avoid focus traps](https://developer.chrome.com/docs/lighthouse/accessibility/focus-traps/).","score":null,"scoreDisplayMode":"manual"},"focusable-controls":{"id":"focusable-controls","title":"Interactive controls are keyboard focusable","description":"Custom interactive controls are keyboard focusable and display a focus indicator. [Learn how to make custom controls focusable](https://developer.chrome.com/docs/lighthouse/accessibility/focusable-controls/).","score":null,"scoreDisplayMode":"manual"},"interactive-element-affordance":{"id":"interactive-element-affordance","title":"Interactive elements indicate their purpose and state","description":"Interactive elements, such as links and buttons, should indicate their state and be distinguishable from non-interactive elements. [Learn how to decorate interactive elements with affordance hints](https://developer.chrome.com/docs/lighthouse/accessibility/interactive-element-affordance/).","score":null,"scoreDisplayMode":"manual"},"logical-tab-order":{"id":"logical-tab-order","title":"The page has a logical tab order","description":"Tabbing through the page follows the visual layout. Users cannot focus elements that are offscreen. [Learn more about logical tab ordering](https://developer.chrome.com/docs/lighthouse/accessibility/logical-tab-order/).","score":null,"scoreDisplayMode":"manual"},"managed-focus":{"id":"managed-focus","title":"The user's focus is directed to new content added to the page","description":"If new content, such as a dialog, is added to the page, the user's focus is directed to it. [Learn how to direct focus to new content](https://developer.chrome.com/docs/lighthouse/accessibility/managed-focus/).","score":null,"scoreDisplayMode":"manual"},"offscreen-content-hidden":{"id":"offscreen-content-hidden","title":"Offscreen content is hidden from assistive technology","description":"Offscreen content is hidden with display: none or aria-hidden=true. [Learn how to properly hide offscreen content](https://developer.chrome.com/docs/lighthouse/accessibility/offscreen-content-hidden/).","score":null,"scoreDisplayMode":"manual"},"use-landmarks":{"id":"use-landmarks","title":"HTML5 landmark elements are used to improve navigation","description":"Landmark elements (`\u003cmain>`, `\u003cnav>`, etc.) are used to improve the keyboard navigation of the page for assistive technology. [Learn more about landmark elements](https://developer.chrome.com/docs/lighthouse/accessibility/use-landmarks/).","score":null,"scoreDisplayMode":"manual"},"visual-order-follows-dom":{"id":"visual-order-follows-dom","title":"Visual order on the page follows DOM order","description":"DOM order matches the visual order, improving navigation for assistive technology. [Learn more about DOM and visual ordering](https://developer.chrome.com/docs/lighthouse/accessibility/visual-order-follows-dom/).","score":null,"scoreDisplayMode":"manual"},"uses-long-cache-ttl":{"id":"uses-long-cache-ttl","title":"Uses efficient cache policy on static assets","description":"A long cache lifetime can speed up repeat visits to your page. [Learn more about efficient cache policies](https://developer.chrome.com/docs/lighthouse/performance/uses-long-cache-ttl/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"byte","displayValue":"0 resources found","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"total-byte-weight":{"id":"total-byte-weight","title":"Avoids enormous network payloads","description":"Large network payloads cost users real money and are highly correlated with long load times. [Learn how to reduce payload sizes](https://developer.chrome.com/docs/lighthouse/performance/total-byte-weight/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":1297,"numericUnit":"byte","displayValue":"Total size was 1 KiB","details":{"type":"table","headings":[{"key":"url","valueType":"url","label":"URL"},{"key":"totalBytes","valueType":"bytes","label":"Transfer Size"}],"items":[{"url":"https://filecoin.io/categories/","totalBytes":1297}],"sortedBy":["totalBytes"]},"guidanceLevel":1},"offscreen-images":{"id":"offscreen-images","title":"Defer offscreen images","description":"Consider lazy-loading offscreen and hidden images after all critical resources have finished loading to lower time to interactive. [Learn how to defer offscreen images](https://developer.chrome.com/docs/lighthouse/performance/offscreen-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"render-blocking-resources":{"id":"render-blocking-resources","title":"Eliminate render-blocking resources","description":"Resources are blocking the first paint of your page. Consider delivering critical JS/CSS inline and deferring all non-critical JS/styles. [Learn how to eliminate render-blocking resources](https://developer.chrome.com/docs/lighthouse/performance/render-blocking-resources/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"unminified-css":{"id":"unminified-css","title":"Minify CSS","description":"Minifying CSS files can reduce network payload sizes. [Learn how to minify CSS](https://developer.chrome.com/docs/lighthouse/performance/unminified-css/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"unminified-javascript":{"id":"unminified-javascript","title":"Minify JavaScript","description":"Minifying JavaScript files can reduce payload sizes and script parse time. [Learn how to minify JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unminified-javascript/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"unused-css-rules":{"id":"unused-css-rules","title":"Reduce unused CSS","description":"Reduce unused rules from stylesheets and defer CSS not used for above-the-fold content to decrease bytes consumed by network activity. [Learn how to reduce unused CSS](https://developer.chrome.com/docs/lighthouse/performance/unused-css-rules/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":1},"unused-javascript":{"id":"unused-javascript","title":"Reduce unused JavaScript","description":"Reduce unused JavaScript and defer loading scripts until they are required to decrease bytes consumed by network activity. [Learn how to reduce unused JavaScript](https://developer.chrome.com/docs/lighthouse/performance/unused-javascript/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":1},"modern-image-formats":{"id":"modern-image-formats","title":"Serve images in next-gen formats","description":"Image formats like WebP and AVIF often provide better compression than PNG or JPEG, which means faster downloads and less data consumption. [Learn more about modern image formats](https://developer.chrome.com/docs/lighthouse/performance/uses-webp-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"uses-optimized-images":{"id":"uses-optimized-images","title":"Efficiently encode images","description":"Optimized images load faster and consume less cellular data. [Learn how to efficiently encode images](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"uses-text-compression":{"id":"uses-text-compression","title":"Enable text compression","description":"Text-based resources should be served with compression (gzip, deflate or brotli) to minimize total network bytes. [Learn more about text compression](https://developer.chrome.com/docs/lighthouse/performance/uses-text-compression/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"uses-responsive-images":{"id":"uses-responsive-images","title":"Properly size images","description":"Serve images that are appropriately-sized to save cellular data and improve load time. [Learn how to size images](https://developer.chrome.com/docs/lighthouse/performance/uses-responsive-images/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"efficient-animated-content":{"id":"efficient-animated-content","title":"Use video formats for animated content","description":"Large GIFs are inefficient for delivering animated content. Consider using MPEG4/WebM videos for animations and PNG/WebP for static images instead of GIF to save network bytes. [Learn more about efficient video formats](https://developer.chrome.com/docs/lighthouse/performance/efficient-animated-content/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":3},"duplicated-javascript":{"id":"duplicated-javascript","title":"Remove duplicate modules in JavaScript bundles","description":"Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity. ","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"legacy-javascript":{"id":"legacy-javascript","title":"Avoid serving legacy JavaScript to modern browsers","description":"Polyfills and transforms enable legacy browsers to use new JavaScript features. However, many aren't necessary for modern browsers. Consider modifying your JavaScript build process to not transpile [Baseline](https://web.dev/baseline) features, unless you know you must support legacy browsers. [Learn why most sites can deploy ES6+ code without transpiling](https://philipwalton.com/articles/the-state-of-es5-on-the-web/)","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","warnings":[],"metricSavings":{"FCP":0,"LCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0,"overallSavingsBytes":0,"sortedBy":["wastedBytes"],"debugData":{"type":"debugdata","metricSavings":{"FCP":0,"LCP":0}}},"guidanceLevel":2},"doctype":{"id":"doctype","title":"Page lacks the HTML doctype, thus triggering quirks-mode","description":"Specifying a doctype prevents the browser from switching to quirks-mode. [Learn more about the doctype declaration](https://developer.chrome.com/docs/lighthouse/best-practices/doctype/).","score":0,"scoreDisplayMode":"binary","explanation":"Document must contain a doctype"},"charset":{"id":"charset","title":"Charset declaration is missing or occurs too late in the HTML","description":"A character encoding declaration is required. It can be done with a `\u003cmeta>` tag in the first 1024 bytes of the HTML or in the Content-Type HTTP response header. [Learn more about declaring the character encoding](https://developer.chrome.com/docs/lighthouse/best-practices/charset/).","score":0,"scoreDisplayMode":"binary"},"dom-size":{"id":"dom-size","title":"Avoids an excessive DOM size","description":"A large DOM will increase memory usage, cause longer [style calculations](https://developers.google.com/web/fundamentals/performance/rendering/reduce-the-scope-and-complexity-of-style-calculations), and produce costly [layout reflows](https://developers.google.com/speed/articles/reflow). [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"element","displayValue":"0 elements","metricSavings":{"TBT":0},"details":{"type":"table","headings":[{"key":"statistic","valueType":"text","label":"Statistic"},{"key":"node","valueType":"node","label":"Element"},{"key":"value","valueType":"numeric","label":"Value"}],"items":[{"statistic":"Total DOM Elements","value":{"type":"numeric","granularity":1,"value":0}},{"node":{"type":"node","lhId":"1-1-BODY","path":"0,HTML,1,BODY","selector":"body","boundingRect":{"top":8,"bottom":815,"left":8,"right":404,"width":396,"height":807},"snippet":"\u003cbody>","nodeLabel":"body"},"statistic":"Maximum DOM Depth","value":{"type":"numeric","granularity":1,"value":1}},{"node":{"type":"node","lhId":"1-1-BODY","path":"0,HTML,1,BODY","selector":"body","boundingRect":{"top":8,"bottom":815,"left":8,"right":404,"width":396,"height":807},"snippet":"\u003cbody>","nodeLabel":"body"},"statistic":"Maximum Child Elements","value":{"type":"numeric","granularity":1,"value":0}}]},"guidanceLevel":1},"geolocation-on-start":{"id":"geolocation-on-start","title":"Avoids requesting the geolocation permission on page load","description":"Users are mistrustful of or confused by sites that request their location without context. Consider tying the request to a user action instead. [Learn more about the geolocation permission](https://developer.chrome.com/docs/lighthouse/best-practices/geolocation-on-start/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"no-document-write":{"id":"no-document-write","title":"Avoids `document.write()`","description":"For users on slow connections, external scripts dynamically injected via `document.write()` can delay page load by tens of seconds. [Learn how to avoid document.write()](https://developer.chrome.com/docs/lighthouse/best-practices/no-document-write/).","score":1,"scoreDisplayMode":"metricSavings","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"js-libraries":{"id":"js-libraries","title":"Detected JavaScript libraries","description":"All front-end JavaScript libraries detected on the page. [Learn more about this JavaScript library detection diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/js-libraries/).","score":null,"scoreDisplayMode":"notApplicable"},"notification-on-start":{"id":"notification-on-start","title":"Avoids requesting the notification permission on page load","description":"Users are mistrustful of or confused by sites that request to send notifications without context. Consider tying the request to user gestures instead. [Learn more about responsibly getting permission for notifications](https://developer.chrome.com/docs/lighthouse/best-practices/notification-on-start/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"paste-preventing-inputs":{"id":"paste-preventing-inputs","title":"Allows users to paste into input fields","description":"Preventing input pasting is a bad practice for the UX, and weakens security by blocking password managers.[Learn more about user-friendly input fields](https://developer.chrome.com/docs/lighthouse/best-practices/paste-preventing-inputs/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"uses-http2":{"id":"uses-http2","title":"Use HTTP/2","description":"HTTP/2 offers many benefits over HTTP/1.1, including binary headers and multiplexing. [Learn more about HTTP/2](https://developer.chrome.com/docs/lighthouse/best-practices/uses-http2/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":3},"uses-passive-event-listeners":{"id":"uses-passive-event-listeners","title":"Uses passive listeners to improve scrolling performance","description":"Consider marking your touch and wheel event listeners as `passive` to improve your page's scroll performance. [Learn more about adopting passive event listeners](https://developer.chrome.com/docs/lighthouse/best-practices/uses-passive-event-listeners/).","score":1,"scoreDisplayMode":"metricSavings","details":{"type":"table","headings":[],"items":[]},"guidanceLevel":3},"meta-description":{"id":"meta-description","title":"Document does not have a meta description","description":"Meta descriptions may be included in search results to concisely summarize page content. [Learn more about the meta description](https://developer.chrome.com/docs/lighthouse/seo/meta-description/).","score":0,"scoreDisplayMode":"binary"},"http-status-code":{"id":"http-status-code","title":"Page has successful HTTP status code","description":"Pages with unsuccessful HTTP status codes may not be indexed properly. [Learn more about HTTP status codes](https://developer.chrome.com/docs/lighthouse/seo/http-status-code/).","score":1,"scoreDisplayMode":"binary"},"font-size":{"id":"font-size","title":"Document doesn't use legible font sizes","description":"Font sizes less than 12px are too small to be legible and require mobile visitors to “pinch to zoom” in order to read. Strive to have >60% of page text ≥12px. [Learn more about legible font sizes](https://developer.chrome.com/docs/lighthouse/seo/font-size/).","score":0,"scoreDisplayMode":"binary","explanation":"Text is illegible because there's no viewport meta tag optimized for mobile screens."},"link-text":{"id":"link-text","title":"Links have descriptive text","description":"Descriptive link text helps search engines understand your content. [Learn how to make links more accessible](https://developer.chrome.com/docs/lighthouse/seo/link-text/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"crawlable-anchors":{"id":"crawlable-anchors","title":"Links are crawlable","description":"Search engines may use `href` attributes on links to crawl websites. Ensure that the `href` attribute of anchor elements links to an appropriate destination, so more pages of the site can be discovered. [Learn how to make links crawlable](https://support.google.com/webmasters/answer/9112205)","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}},"robots-txt":{"id":"robots-txt","title":"robots.txt is valid","description":"If your robots.txt file is malformed, crawlers may not be able to understand how you want your website to be crawled or indexed. [Learn more about robots.txt](https://developer.chrome.com/docs/lighthouse/seo/invalid-robots-txt/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"hreflang":{"id":"hreflang","title":"Document has a valid `hreflang`","description":"hreflang links tell search engines what version of a page they should list in search results for a given language or region. [Learn more about `hreflang`](https://developer.chrome.com/docs/lighthouse/seo/hreflang/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"canonical":{"id":"canonical","title":"Document has a valid `rel=canonical`","description":"Canonical links suggest which URL to show in search results. [Learn more about canonical links](https://developer.chrome.com/docs/lighthouse/seo/canonical/).","score":null,"scoreDisplayMode":"notApplicable"},"structured-data":{"id":"structured-data","title":"Structured data is valid","description":"Run the [Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool/) and the [Structured Data Linter](http://linter.structured-data.org/) to validate structured data. [Learn more about Structured Data](https://developer.chrome.com/docs/lighthouse/seo/structured-data/).","score":null,"scoreDisplayMode":"manual"},"bf-cache":{"id":"bf-cache","title":"Page didn't prevent back/forward cache restoration","description":"Many navigations are performed by going back to a previous page, or forwards again. The back/forward cache (bfcache) can speed up these return navigations. [Learn more about the bfcache](https://developer.chrome.com/docs/lighthouse/performance/bf-cache/)","score":1,"scoreDisplayMode":"binary","guidanceLevel":4},"cls-culprits-insight":{"id":"cls-culprits-insight","title":"Layout shift culprits","description":"Layout shifts occur when elements move absent any user interaction. [Investigate the causes of layout shifts](https://web.dev/articles/optimize-cls), such as elements being added, removed, or their fonts changing as the page loads.","score":1,"scoreDisplayMode":"numeric","metricSavings":{"CLS":0},"details":{"type":"list","items":[]},"guidanceLevel":3,"replacesAudits":["layout-shifts","non-composited-animations","unsized-images"]},"document-latency-insight":{"id":"document-latency-insight","title":"Document request latency","description":"Your first network request is the most important.  Reduce its latency by avoiding redirects, ensuring a fast server response, and enabling text compression.","score":1,"scoreDisplayMode":"metricSavings","metricSavings":{"FCP":0,"LCP":0},"details":{"type":"checklist","items":{"noRedirects":{"label":"Avoids redirects","value":true},"serverResponseIsFast":{"label":"Server responds quickly","value":true},"usesCompression":{"label":"Applies text compression","value":true}}},"guidanceLevel":3,"replacesAudits":["redirects","server-response-time","uses-text-compression"]},"dom-size-insight":{"id":"dom-size-insight","title":"Optimize DOM size","description":"A large DOM can increase the duration of style calculations and layout reflows, impacting page responsiveness. A large DOM will also increase memory usage. [Learn how to avoid an excessive DOM size](https://developer.chrome.com/docs/lighthouse/performance/dom-size/).","score":1,"scoreDisplayMode":"numeric","metricSavings":{"INP":0},"details":{"type":"table","headings":[{"key":"statistic","valueType":"text","label":"Statistic"},{"key":"node","valueType":"node","label":"Element"},{"key":"value","valueType":"numeric","label":"Value"}],"items":[{"statistic":"Total elements","value":{"type":"numeric","granularity":1,"value":2}},{"statistic":"Most children","node":{"type":"node","lhId":"page-1-HTML","path":"0,HTML","selector":"html","boundingRect":{"top":0,"bottom":823,"left":0,"right":412,"width":412,"height":823},"snippet":"\u003chtml>","nodeLabel":"html"},"value":{"type":"numeric","granularity":1,"value":1}},{"statistic":"DOM depth","node":{"type":"node","lhId":"page-0-BODY","path":"0,HTML,1,BODY","selector":"body","boundingRect":{"top":8,"bottom":815,"left":8,"right":404,"width":396,"height":807},"snippet":"\u003cbody>","nodeLabel":"body"},"value":{"type":"numeric","granularity":1,"value":1}}]},"guidanceLevel":3,"replacesAudits":["dom-size"]},"duplicated-javascript-insight":{"id":"duplicated-javascript-insight","title":"Duplicated JavaScript","description":"Remove large, duplicate JavaScript modules from bundles to reduce unnecessary bytes consumed by network activity.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3},"font-display-insight":{"id":"font-display-insight","title":"Font display","description":"Consider setting [font-display](https://developer.chrome.com/blog/font-display) to swap or optional to ensure text is consistently visible. swap can be further optimized to mitigate layout shifts with [font metric overrides](https://developer.chrome.com/blog/font-fallbacks).","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["font-display"]},"forced-reflow-insight":{"id":"forced-reflow-insight","title":"Forced reflow","description":"Many APIs, typically reading layout geometry, force the rendering engine to pause script execution in order to calculate the style and layout. Learn more about [forced reflow](https://developers.google.com/web/fundamentals/performance/rendering/avoid-large-complex-layouts-and-layout-thrashing#avoid-forced-synchronous-layouts) and its mitigations.","score":1,"scoreDisplayMode":"numeric","details":{"type":"list","items":[{"type":"table","headings":[],"items":[]}]},"guidanceLevel":3},"image-delivery-insight":{"id":"image-delivery-insight","title":"Improve image delivery","description":"Reducing the download time of images can improve the perceived load time of the page and LCP. [Learn more about optimizing image size](https://developer.chrome.com/docs/lighthouse/performance/uses-optimized-images/)","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["modern-image-formats","uses-optimized-images","efficient-animated-content","uses-responsive-images"]},"interaction-to-next-paint-insight":{"id":"interaction-to-next-paint-insight","title":"INP by phase","description":"Start investigating with the longest phase. [Delays can be minimized](https://web.dev/articles/optimize-inp#optimize_interactions). To reduce processing duration, [optimize the main-thread costs](https://web.dev/articles/optimize-long-tasks), often JS.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["work-during-interaction"]},"lcp-discovery-insight":{"id":"lcp-discovery-insight","title":"LCP request discovery","description":"Optimize LCP by making the LCP image [discoverable](https://web.dev/articles/optimize-lcp#1_eliminate_resource_load_delay) from the HTML immediately, and [avoiding lazy-loading](https://web.dev/articles/lcp-lazy-loading)","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["prioritize-lcp-image","lcp-lazy-loaded"]},"lcp-phases-insight":{"id":"lcp-phases-insight","title":"LCP by phase","description":"Each [phase has specific improvement strategies](https://web.dev/articles/optimize-lcp#lcp-breakdown). Ideally, most of the LCP time should be spent on loading the resources, not within delays.","score":1,"scoreDisplayMode":"numeric","metricSavings":{"LCP":0},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-BODY","path":"0,HTML,1,BODY","selector":"body","boundingRect":{"top":8,"bottom":815,"left":8,"right":404,"width":396,"height":807},"snippet":"\u003cbody>","nodeLabel":"body"}}]},{"type":"table","headings":[{"key":"label","valueType":"text","label":"Phase"},{"key":"duration","valueType":"ms","label":"Duration"}],"items":[{"phase":"timeToFirstByte","label":"Time to first byte","duration":136.683},{"phase":"elementRenderDelay","label":"Element render delay","duration":48.21200000000002}]}]},"guidanceLevel":3,"replacesAudits":["largest-contentful-paint-element"]},"network-dependency-tree-insight":{"id":"network-dependency-tree-insight","title":"Network dependency tree","description":"[Avoid chaining critical requests](https://developer.chrome.com/docs/lighthouse/performance/critical-request-chains) by reducing the length of chains, reducing the download size of resources, or deferring the download of unnecessary resources to improve page load.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["critical-request-chains"]},"render-blocking-insight":{"id":"render-blocking-insight","title":"Render blocking requests","description":"Requests are blocking the page's initial render, which may delay LCP. [Deferring or inlining](https://web.dev/learn/performance/understanding-the-critical-path#render-blocking_resources/) can move these network requests out of the critical path.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["render-blocking-resources"]},"slow-css-selector-insight":{"id":"slow-css-selector-insight","title":"CSS Selector costs","description":"If Recalculate Style costs remain high, selector optimization can reduce them. [Optimize the selectors](https://developer.chrome.com/docs/devtools/performance/selector-stats) with both high elapsed time and high slow-path %. Simpler selectors, fewer selectors, a smaller DOM, and a shallower DOM will all reduce matching costs.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3},"third-parties-insight":{"id":"third-parties-insight","title":"3rd parties","description":"3rd party code can significantly impact load performance. [Reduce and defer loading of 3rd party code](https://developers.google.com/web/fundamentals/performance/optimizing-content-efficiency/loading-third-party-javascript/) to prioritize your page's content.","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["third-party-summary"]},"use-cache-insight":{"id":"use-cache-insight","title":"Use efficient cache lifetimes","description":"A long cache lifetime can speed up repeat visits to your page. [Learn more](https://web.dev/uses-long-cache-ttl/).","score":null,"scoreDisplayMode":"notApplicable","guidanceLevel":3,"replacesAudits":["uses-long-cache-ttl"]},"viewport-insight":{"id":"viewport-insight","title":"Optimize viewport for mobile","description":"Tap interactions may be [delayed by up to 300 ms](https://developer.chrome.com/blog/300ms-tap-delay-gone-away/) if the viewport is not optimized for mobile.","score":0,"scoreDisplayMode":"metricSavings","metricSavings":{"INP":300},"details":{"type":"table","headings":[{"key":"node","valueType":"node","label":""}],"items":[{}]},"guidanceLevel":3,"replacesAudits":["viewport"]}},"configSettings":{"output":["html","json"],"maxWaitForFcp":30000,"maxWaitForLoad":45000,"pauseAfterFcpMs":1000,"pauseAfterLoadMs":1000,"networkQuietThresholdMs":1000,"cpuQuietThresholdMs":1000,"formFactor":"mobile","throttling":{"rttMs":150,"throughputKbps":1638.4,"requestLatencyMs":600,"downloadThroughputKbps":1638.4,"uploadThroughputKbps":750,"cpuSlowdownMultiplier":1},"throttlingMethod":"simulate","screenEmulation":{"mobile":true,"width":412,"height":823,"deviceScaleFactor":1.75,"disabled":false},"emulatedUserAgent":"Mozilla/5.0 (Linux; Android 11; moto g power (2022)) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36","auditMode":false,"gatherMode":false,"clearStorageTypes":["file_systems","shader_cache","service_workers","cache_storage"],"disableStorageReset":false,"debugNavigation":false,"channel":"node","usePassiveGathering":false,"disableFullPageScreenshot":false,"skipAboutBlank":false,"blankPage":"about:blank","ignoreStatusCode":false,"locale":"en-US","blockedUrlPatterns":null,"additionalTraceCategories":null,"extraHeaders":null,"precomputedLanternData":null,"onlyAudits":null,"onlyCategories":["performance","accessibility","best-practices","seo"],"skipAudits":null},"categories":{"performance":{"title":"Performance","supportedModes":["navigation","timespan","snapshot"],"auditRefs":[{"id":"first-contentful-paint","weight":10,"group":"metrics","acronym":"FCP"},{"id":"largest-contentful-paint","weight":25,"group":"metrics","acronym":"LCP"},{"id":"total-blocking-time","weight":30,"group":"metrics","acronym":"TBT"},{"id":"cumulative-layout-shift","weight":25,"group":"metrics","acronym":"CLS"},{"id":"speed-index","weight":10,"group":"metrics","acronym":"SI"},{"id":"cls-culprits-insight","weight":0,"group":"hidden"},{"id":"document-latency-insight","weight":0,"group":"hidden"},{"id":"dom-size-insight","weight":0,"group":"hidden"},{"id":"duplicated-javascript-insight","weight":0,"group":"hidden"},{"id":"font-display-insight","weight":0,"group":"hidden"},{"id":"forced-reflow-insight","weight":0,"group":"hidden"},{"id":"image-delivery-insight","weight":0,"group":"hidden"},{"id":"interaction-to-next-paint-insight","weight":0,"group":"hidden"},{"id":"lcp-discovery-insight","weight":0,"group":"hidden"},{"id":"lcp-phases-insight","weight":0,"group":"hidden"},{"id":"network-dependency-tree-insight","weight":0,"group":"hidden"},{"id":"render-blocking-insight","weight":0,"group":"hidden"},{"id":"slow-css-selector-insight","weight":0,"group":"hidden"},{"id":"third-parties-insight","weight":0,"group":"hidden"},{"id":"use-cache-insight","weight":0,"group":"hidden"},{"id":"viewport-insight","weight":0,"group":"hidden"},{"id":"interactive","weight":0,"group":"hidden","acronym":"TTI"},{"id":"max-potential-fid","weight":0,"group":"hidden"},{"id":"first-meaningful-paint","weight":0,"acronym":"FMP","group":"hidden"},{"id":"render-blocking-resources","weight":0,"group":"diagnostics"},{"id":"uses-responsive-images","weight":0,"group":"diagnostics"},{"id":"offscreen-images","weight":0,"group":"diagnostics"},{"id":"unminified-css","weight":0,"group":"diagnostics"},{"id":"unminified-javascript","weight":0,"group":"diagnostics"},{"id":"unused-css-rules","weight":0,"group":"diagnostics"},{"id":"unused-javascript","weight":0,"group":"diagnostics"},{"id":"uses-optimized-images","weight":0,"group":"diagnostics"},{"id":"modern-image-formats","weight":0,"group":"diagnostics"},{"id":"uses-text-compression","weight":0,"group":"diagnostics"},{"id":"uses-rel-preconnect","weight":0,"group":"diagnostics"},{"id":"server-response-time","weight":0,"group":"diagnostics"},{"id":"redirects","weight":0,"group":"diagnostics"},{"id":"uses-http2","weight":0,"group":"diagnostics"},{"id":"efficient-animated-content","weight":0,"group":"diagnostics"},{"id":"duplicated-javascript","weight":0,"group":"diagnostics"},{"id":"legacy-javascript","weight":0,"group":"diagnostics"},{"id":"prioritize-lcp-image","weight":0,"group":"diagnostics"},{"id":"total-byte-weight","weight":0,"group":"diagnostics"},{"id":"uses-long-cache-ttl","weight":0,"group":"diagnostics"},{"id":"dom-size","weight":0,"group":"diagnostics"},{"id":"critical-request-chains","weight":0,"group":"diagnostics"},{"id":"user-timings","weight":0,"group":"diagnostics"},{"id":"bootup-time","weight":0,"group":"diagnostics"},{"id":"mainthread-work-breakdown","weight":0,"group":"diagnostics"},{"id":"font-display","weight":0,"group":"diagnostics"},{"id":"third-party-summary","weight":0,"group":"diagnostics"},{"id":"third-party-facades","weight":0,"group":"diagnostics"},{"id":"largest-contentful-paint-element","weight":0,"group":"diagnostics"},{"id":"lcp-lazy-loaded","weight":0,"group":"diagnostics"},{"id":"layout-shifts","weight":0,"group":"diagnostics"},{"id":"uses-passive-event-listeners","weight":0,"group":"diagnostics"},{"id":"no-document-write","weight":0,"group":"diagnostics"},{"id":"long-tasks","weight":0,"group":"diagnostics"},{"id":"non-composited-animations","weight":0,"group":"diagnostics"},{"id":"unsized-images","weight":0,"group":"diagnostics"},{"id":"viewport","weight":0,"group":"diagnostics"},{"id":"bf-cache","weight":0,"group":"diagnostics"},{"id":"network-requests","weight":0,"group":"hidden"},{"id":"network-rtt","weight":0,"group":"hidden"},{"id":"network-server-latency","weight":0,"group":"hidden"},{"id":"main-thread-tasks","weight":0,"group":"hidden"},{"id":"diagnostics","weight":0,"group":"hidden"},{"id":"metrics","weight":0,"group":"hidden"},{"id":"screenshot-thumbnails","weight":0,"group":"hidden"},{"id":"final-screenshot","weight":0,"group":"hidden"},{"id":"script-treemap-data","weight":0,"group":"hidden"},{"id":"resource-summary","weight":0,"group":"hidden"}],"id":"performance","score":1},"accessibility":{"title":"Accessibility","description":"These checks highlight opportunities to [improve the accessibility of your web app](https://developer.chrome.com/docs/lighthouse/accessibility/). Automatic detection can only detect a subset of issues and does not guarantee the accessibility of your web app, so [manual testing](https://web.dev/articles/how-to-review) is also encouraged.","manualDescription":"These items address areas which an automated testing tool cannot cover. Learn more in our guide on [conducting an accessibility review](https://web.dev/articles/how-to-review).","supportedModes":["navigation","snapshot"],"auditRefs":[{"id":"accesskeys","weight":0,"group":"a11y-navigation"},{"id":"aria-allowed-attr","weight":0,"group":"a11y-aria"},{"id":"aria-allowed-role","weight":0,"group":"a11y-aria"},{"id":"aria-command-name","weight":0,"group":"a11y-aria"},{"id":"aria-conditional-attr","weight":0,"group":"a11y-aria"},{"id":"aria-deprecated-role","weight":0,"group":"a11y-aria"},{"id":"aria-dialog-name","weight":0,"group":"a11y-aria"},{"id":"aria-hidden-body","weight":10,"group":"a11y-aria"},{"id":"aria-hidden-focus","weight":0,"group":"a11y-aria"},{"id":"aria-input-field-name","weight":0,"group":"a11y-aria"},{"id":"aria-meter-name","weight":0,"group":"a11y-aria"},{"id":"aria-progressbar-name","weight":0,"group":"a11y-aria"},{"id":"aria-prohibited-attr","weight":0,"group":"a11y-aria"},{"id":"aria-required-attr","weight":0,"group":"a11y-aria"},{"id":"aria-required-children","weight":0,"group":"a11y-aria"},{"id":"aria-required-parent","weight":0,"group":"a11y-aria"},{"id":"aria-roles","weight":0,"group":"a11y-aria"},{"id":"aria-text","weight":0,"group":"a11y-aria"},{"id":"aria-toggle-field-name","weight":0,"group":"a11y-aria"},{"id":"aria-tooltip-name","weight":0,"group":"a11y-aria"},{"id":"aria-treeitem-name","weight":0,"group":"a11y-aria"},{"id":"aria-valid-attr-value","weight":0,"group":"a11y-aria"},{"id":"aria-valid-attr","weight":0,"group":"a11y-aria"},{"id":"button-name","weight":0,"group":"a11y-names-labels"},{"id":"bypass","weight":0,"group":"a11y-navigation"},{"id":"color-contrast","weight":7,"group":"a11y-color-contrast"},{"id":"definition-list","weight":0,"group":"a11y-tables-lists"},{"id":"dlitem","weight":0,"group":"a11y-tables-lists"},{"id":"document-title","weight":7,"group":"a11y-names-labels"},{"id":"duplicate-id-aria","weight":0,"group":"a11y-aria"},{"id":"form-field-multiple-labels","weight":0,"group":"a11y-names-labels"},{"id":"frame-title","weight":0,"group":"a11y-names-labels"},{"id":"heading-order","weight":0,"group":"a11y-navigation"},{"id":"html-has-lang","weight":7,"group":"a11y-language"},{"id":"html-lang-valid","weight":0,"group":"a11y-language"},{"id":"html-xml-lang-mismatch","weight":0,"group":"a11y-language"},{"id":"image-alt","weight":0,"group":"a11y-names-labels"},{"id":"image-redundant-alt","weight":0,"group":"a11y-names-labels"},{"id":"input-button-name","weight":0,"group":"a11y-names-labels"},{"id":"input-image-alt","weight":0,"group":"a11y-names-labels"},{"id":"label","weight":0,"group":"a11y-names-labels"},{"id":"link-in-text-block","weight":0,"group":"a11y-color-contrast"},{"id":"link-name","weight":0,"group":"a11y-names-labels"},{"id":"list","weight":0,"group":"a11y-tables-lists"},{"id":"listitem","weight":0,"group":"a11y-tables-lists"},{"id":"meta-refresh","weight":0,"group":"a11y-best-practices"},{"id":"meta-viewport","weight":0,"group":"a11y-best-practices"},{"id":"object-alt","weight":0,"group":"a11y-names-labels"},{"id":"select-name","weight":0,"group":"a11y-names-labels"},{"id":"skip-link","weight":0,"group":"a11y-names-labels"},{"id":"tabindex","weight":0,"group":"a11y-navigation"},{"id":"table-duplicate-name","weight":0,"group":"a11y-tables-lists"},{"id":"target-size","weight":0,"group":"a11y-best-practices"},{"id":"td-headers-attr","weight":0,"group":"a11y-tables-lists"},{"id":"th-has-data-cells","weight":0,"group":"a11y-tables-lists"},{"id":"valid-lang","weight":0,"group":"a11y-language"},{"id":"video-caption","weight":0,"group":"a11y-audio-video"},{"id":"focusable-controls","weight":0},{"id":"interactive-element-affordance","weight":0},{"id":"logical-tab-order","weight":0},{"id":"visual-order-follows-dom","weight":0},{"id":"focus-traps","weight":0},{"id":"managed-focus","weight":0},{"id":"use-landmarks","weight":0},{"id":"offscreen-content-hidden","weight":0},{"id":"custom-controls-labels","weight":0},{"id":"custom-controls-roles","weight":0},{"id":"empty-heading","weight":0,"group":"hidden"},{"id":"identical-links-same-purpose","weight":0,"group":"hidden"},{"id":"landmark-one-main","weight":0,"group":"hidden"},{"id":"label-content-name-mismatch","weight":0,"group":"hidden"},{"id":"table-fake-caption","weight":0,"group":"hidden"},{"id":"td-has-header","weight":0,"group":"hidden"}],"id":"accessibility","score":0.55},"best-practices":{"title":"Best Practices","supportedModes":["navigation","timespan","snapshot"],"auditRefs":[{"id":"is-on-https","weight":5,"group":"best-practices-trust-safety"},{"id":"redirects-http","weight":0,"group":"best-practices-trust-safety"},{"id":"geolocation-on-start","weight":1,"group":"best-practices-trust-safety"},{"id":"notification-on-start","weight":1,"group":"best-practices-trust-safety"},{"id":"csp-xss","weight":0,"group":"best-practices-trust-safety"},{"id":"has-hsts","weight":0,"group":"best-practices-trust-safety"},{"id":"origin-isolation","weight":0,"group":"best-practices-trust-safety"},{"id":"clickjacking-mitigation","weight":0,"group":"best-practices-trust-safety"},{"id":"paste-preventing-inputs","weight":3,"group":"best-practices-ux"},{"id":"image-aspect-ratio","weight":1,"group":"best-practices-ux"},{"id":"image-size-responsive","weight":1,"group":"best-practices-ux"},{"id":"viewport","weight":1,"group":"best-practices-ux"},{"id":"font-size","weight":1,"group":"best-practices-ux"},{"id":"doctype","weight":1,"group":"best-practices-browser-compat"},{"id":"charset","weight":1,"group":"best-practices-browser-compat"},{"id":"js-libraries","weight":0,"group":"best-practices-general"},{"id":"deprecations","weight":5,"group":"best-practices-general"},{"id":"third-party-cookies","weight":5,"group":"best-practices-general"},{"id":"errors-in-console","weight":1,"group":"best-practices-general"},{"id":"valid-source-maps","weight":0,"group":"best-practices-general"},{"id":"inspector-issues","weight":1,"group":"best-practices-general"}],"id":"best-practices","score":0.86},"seo":{"title":"SEO","description":"These checks ensure that your page is following basic search engine optimization advice. There are many additional factors Lighthouse does not score here that may affect your search ranking, including performance on [Core Web Vitals](https://web.dev/explore/vitals). [Learn more about Google Search Essentials](https://support.google.com/webmasters/answer/35769).","manualDescription":"Run these additional validators on your site to check additional SEO best practices.","supportedModes":["navigation","snapshot"],"auditRefs":[{"id":"is-crawlable","weight":4.043478260869565,"group":"seo-crawl"},{"id":"document-title","weight":1,"group":"seo-content"},{"id":"meta-description","weight":1,"group":"seo-content"},{"id":"http-status-code","weight":1,"group":"seo-crawl"},{"id":"link-text","weight":1,"group":"seo-content"},{"id":"crawlable-anchors","weight":1,"group":"seo-crawl"},{"id":"robots-txt","weight":1,"group":"seo-crawl"},{"id":"image-alt","weight":0,"group":"seo-content"},{"id":"hreflang","weight":1,"group":"seo-content"},{"id":"canonical","weight":0,"group":"seo-content"},{"id":"structured-data","weight":0}],"id":"seo","score":0.82}},"categoryGroups":{"metrics":{"title":"Metrics"},"insights":{"title":"Insights"},"diagnostics":{"title":"Diagnostics","description":"More information about the performance of your application. These numbers don't [directly affect](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) the Performance score."},"a11y-best-practices":{"title":"Best practices","description":"These items highlight common accessibility best practices."},"a11y-color-contrast":{"title":"Contrast","description":"These are opportunities to improve the legibility of your content."},"a11y-names-labels":{"title":"Names and labels","description":"These are opportunities to improve the semantics of the controls in your application. This may enhance the experience for users of assistive technology, like a screen reader."},"a11y-navigation":{"title":"Navigation","description":"These are opportunities to improve keyboard navigation in your application."},"a11y-aria":{"title":"ARIA","description":"These are opportunities to improve the usage of ARIA in your application which may enhance the experience for users of assistive technology, like a screen reader."},"a11y-language":{"title":"Internationalization and localization","description":"These are opportunities to improve the interpretation of your content by users in different locales."},"a11y-audio-video":{"title":"Audio and video","description":"These are opportunities to provide alternative content for audio and video. This may improve the experience for users with hearing or vision impairments."},"a11y-tables-lists":{"title":"Tables and lists","description":"These are opportunities to improve the experience of reading tabular or list data using assistive technology, like a screen reader."},"seo-mobile":{"title":"Mobile Friendly","description":"Make sure your pages are mobile friendly so users don’t have to pinch or zoom in order to read the content pages. [Learn how to make pages mobile-friendly](https://developers.google.com/search/mobile-sites/)."},"seo-content":{"title":"Content Best Practices","description":"Format your HTML in a way that enables crawlers to better understand your app’s content."},"seo-crawl":{"title":"Crawling and Indexing","description":"To appear in search results, crawlers need access to your app."},"best-practices-trust-safety":{"title":"Trust and Safety"},"best-practices-ux":{"title":"User Experience"},"best-practices-browser-compat":{"title":"Browser Compatibility"},"best-practices-general":{"title":"General"},"hidden":{"title":""}},"stackPacks":[],"entities":[{"name":"filecoin.io","origins":["https://filecoin.io"],"isFirstParty":true,"isUnrecognized":true}],"fullPageScreenshot":{"screenshot":{"data":"data:image/webp;base64,UklGRvoCAABXRUJQVlA4IO4CAADQTQCdASqcATcDPxGIwVosKCckIDqoAYAiCWlu4XdhG17BwCicxqmjAAT2/FIgG/LAPfbJyHvtk5CAqm0Fku14uTkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHvtk5D32ych77ZOQ99snIe+2TkPfbJyHrAAAP7tTkigqA1Yra+WU/UiQy41R3xD39rojFUHljK8C5G9SK6Bcw0i02aaJVpjVhWICqXCNrf4FeR/f/rYG2yU91Z8YwRZHMYSH9uG9NeLmT7/zQxwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=","width":980.0000610351562,"height":1957.6214599609375},"nodes":{"page-0-BODY":{"id":"","top":8,"bottom":1950,"left":8,"right":972,"width":964,"height":1942},"page-1-HTML":{"id":"","top":0,"bottom":1958,"left":0,"right":980,"width":980,"height":1958},"1-0-HTML":{"id":"","top":0,"bottom":1958,"left":0,"right":980,"width":980,"height":1958},"1-1-BODY":{"id":"","top":8,"bottom":1950,"left":8,"right":972,"width":964,"height":1942}}},"timing":{"entries":[{"startTime":853.19,"name":"lh:config","duration":514.19,"entryType":"measure"},{"startTime":854.3,"name":"lh:config:resolveArtifactsToDefns","duration":56.23,"entryType":"measure"},{"startTime":1367.5,"name":"lh:runner:gather","duration":6344.55,"entryType":"measure"},{"startTime":1608.25,"name":"lh:driver:connect","duration":18.94,"entryType":"measure"},{"startTime":1627.39,"name":"lh:driver:navigate","duration":99.02,"entryType":"measure"},{"startTime":1726.59,"name":"lh:gather:getBenchmarkIndex","duration":1008.17,"entryType":"measure"},{"startTime":2735.89,"name":"lh:gather:getVersion","duration":0.56,"entryType":"measure"},{"startTime":2741.21,"name":"lh:prepare:navigationMode","duration":54.69,"entryType":"measure"},{"startTime":2765.16,"name":"lh:storage:clearDataForOrigin","duration":14.92,"entryType":"measure"},{"startTime":2780.17,"name":"lh:storage:clearBrowserCaches","duration":13.94,"entryType":"measure"},{"startTime":2794.85,"name":"lh:gather:prepareThrottlingAndNetwork","duration":0.94,"entryType":"measure"},{"startTime":2818.9,"name":"lh:driver:navigate","duration":2615.27,"entryType":"measure"},{"startTime":5491.24,"name":"lh:computed:NetworkRecords","duration":0.55,"entryType":"measure"},{"startTime":5492.06,"name":"lh:gather:getArtifact:DevtoolsLog","duration":0.1,"entryType":"measure"},{"startTime":5492.17,"name":"lh:gather:getArtifact:Trace","duration":0.01,"entryType":"measure"},{"startTime":5492.19,"name":"lh:gather:getArtifact:Accessibility","duration":114.88,"entryType":"measure"},{"startTime":5607.1,"name":"lh:gather:getArtifact:AnchorElements","duration":6.28,"entryType":"measure"},{"startTime":5613.4,"name":"lh:gather:getArtifact:ConsoleMessages","duration":0.07,"entryType":"measure"},{"startTime":5615.75,"name":"lh:gather:getArtifact:CSSUsage","duration":12.9,"entryType":"measure"},{"startTime":5628.68,"name":"lh:gather:getArtifact:Doctype","duration":6.23,"entryType":"measure"},{"startTime":5634.93,"name":"lh:gather:getArtifact:DOMStats","duration":9.59,"entryType":"measure"},{"startTime":5644.55,"name":"lh:gather:getArtifact:FontSize","duration":9.99,"entryType":"measure"},{"startTime":5654.6,"name":"lh:gather:getArtifact:Inputs","duration":6.78,"entryType":"measure"},{"startTime":5661.41,"name":"lh:gather:getArtifact:ImageElements","duration":13.32,"entryType":"measure"},{"startTime":5674.82,"name":"lh:gather:getArtifact:InspectorIssues","duration":0.16,"entryType":"measure"},{"startTime":5674.99,"name":"lh:gather:getArtifact:JsUsage","duration":0.03,"entryType":"measure"},{"startTime":5675.03,"name":"lh:gather:getArtifact:LinkElements","duration":3.07,"entryType":"measure"},{"startTime":5677.84,"name":"lh:computed:MainResource","duration":0.21,"entryType":"measure"},{"startTime":5678.14,"name":"lh:gather:getArtifact:MainDocumentContent","duration":4.27,"entryType":"measure"},{"startTime":5682.44,"name":"lh:gather:getArtifact:MetaElements","duration":2.95,"entryType":"measure"},{"startTime":5685.42,"name":"lh:gather:getArtifact:NetworkUserAgent","duration":0.06,"entryType":"measure"},{"startTime":5685.5,"name":"lh:gather:getArtifact:OptimizedImages","duration":0.16,"entryType":"measure"},{"startTime":5685.67,"name":"lh:gather:getArtifact:ResponseCompression","duration":4.44,"entryType":"measure"},{"startTime":5690.13,"name":"lh:gather:getArtifact:RobotsTxt","duration":151.45,"entryType":"measure"},{"startTime":5841.61,"name":"lh:gather:getArtifact:Scripts","duration":0.05,"entryType":"measure"},{"startTime":5841.69,"name":"lh:gather:getArtifact:SourceMaps","duration":0.02,"entryType":"measure"},{"startTime":5841.72,"name":"lh:gather:getArtifact:Stacks","duration":7.9,"entryType":"measure"},{"startTime":5841.76,"name":"lh:gather:collectStacks","duration":7.84,"entryType":"measure"},{"startTime":5849.63,"name":"lh:gather:getArtifact:Stylesheets","duration":4.05,"entryType":"measure"},{"startTime":5853.71,"name":"lh:gather:getArtifact:TraceElements","duration":239.03,"entryType":"measure"},{"startTime":5853.86,"name":"lh:computed:TraceEngineResult","duration":223.71,"entryType":"measure"},{"startTime":5853.91,"name":"lh:computed:ProcessedTrace","duration":4.81,"entryType":"measure"},{"startTime":5859.39,"name":"lh:computed:TraceEngineResult:total","duration":216.31,"entryType":"measure"},{"startTime":5859.4,"name":"lh:computed:TraceEngineResult:parse","duration":202.54,"entryType":"measure"},{"startTime":5859.96,"name":"lh:computed:TraceEngineResult:parse:handleEvent","duration":63.91,"entryType":"measure"},{"startTime":5923.9,"name":"lh:computed:TraceEngineResult:parse:Meta:finalize","duration":1.85,"entryType":"measure"},{"startTime":5925.95,"name":"lh:computed:TraceEngineResult:parse:AnimationFrames:finalize","duration":19.04,"entryType":"measure"},{"startTime":5945.02,"name":"lh:computed:TraceEngineResult:parse:Animations:finalize","duration":8.26,"entryType":"measure"},{"startTime":5953.35,"name":"lh:computed:TraceEngineResult:parse:Samples:finalize","duration":0.75,"entryType":"measure"},{"startTime":5954.14,"name":"lh:computed:TraceEngineResult:parse:AuctionWorklets:finalize","duration":1.41,"entryType":"measure"},{"startTime":5955.59,"name":"lh:computed:TraceEngineResult:parse:NetworkRequests:finalize","duration":3.79,"entryType":"measure"},{"startTime":5959.41,"name":"lh:computed:TraceEngineResult:parse:Renderer:finalize","duration":2.55,"entryType":"measure"},{"startTime":5961.99,"name":"lh:computed:TraceEngineResult:parse:Flows:finalize","duration":4.66,"entryType":"measure"},{"startTime":5966.72,"name":"lh:computed:TraceEngineResult:parse:AsyncJSCalls:finalize","duration":9.62,"entryType":"measure"},{"startTime":5976.42,"name":"lh:computed:TraceEngineResult:parse:DOMStats:finalize","duration":2.9,"entryType":"measure"},{"startTime":5979.37,"name":"lh:computed:TraceEngineResult:parse:UserTimings:finalize","duration":1.15,"entryType":"measure"},{"startTime":5980.54,"name":"lh:computed:TraceEngineResult:parse:ExtensionTraceData:finalize","duration":2.2,"entryType":"measure"},{"startTime":5982.77,"name":"lh:computed:TraceEngineResult:parse:LayerTree:finalize","duration":3.35,"entryType":"measure"},{"startTime":5986.15,"name":"lh:computed:TraceEngineResult:parse:Frames:finalize","duration":10.01,"entryType":"measure"},{"startTime":5996.18,"name":"lh:computed:TraceEngineResult:parse:GPU:finalize","duration":2.91,"entryType":"measure"},{"startTime":5999.24,"name":"lh:computed:TraceEngineResult:parse:ImagePainting:finalize","duration":4.42,"entryType":"measure"},{"startTime":6003.86,"name":"lh:computed:TraceEngineResult:parse:Initiators:finalize","duration":2.18,"entryType":"measure"},{"startTime":6006.07,"name":"lh:computed:TraceEngineResult:parse:Invalidations:finalize","duration":3.92,"entryType":"measure"},{"startTime":6010.02,"name":"lh:computed:TraceEngineResult:parse:PageLoadMetrics:finalize","duration":5.55,"entryType":"measure"},{"startTime":6015.75,"name":"lh:computed:TraceEngineResult:parse:LargestImagePaint:finalize","duration":4.76,"entryType":"measure"},{"startTime":6020.54,"name":"lh:computed:TraceEngineResult:parse:LargestTextPaint:finalize","duration":18.85,"entryType":"measure"},{"startTime":6039.43,"name":"lh:computed:TraceEngineResult:parse:Screenshots:finalize","duration":2.54,"entryType":"measure"},{"startTime":6042,"name":"lh:computed:TraceEngineResult:parse:LayoutShifts:finalize","duration":3.37,"entryType":"measure"},{"startTime":6045.51,"name":"lh:computed:TraceEngineResult:parse:Memory:finalize","duration":3.17,"entryType":"measure"},{"startTime":6048.7,"name":"lh:computed:TraceEngineResult:parse:PageFrames:finalize","duration":2.63,"entryType":"measure"},{"startTime":6051.36,"name":"lh:computed:TraceEngineResult:parse:Scripts:finalize","duration":2.17,"entryType":"measure"},{"startTime":6053.55,"name":"lh:computed:TraceEngineResult:parse:SelectorStats:finalize","duration":0.15,"entryType":"measure"},{"startTime":6053.71,"name":"lh:computed:TraceEngineResult:parse:ServerTimings:finalize","duration":1.77,"entryType":"measure"},{"startTime":6055.51,"name":"lh:computed:TraceEngineResult:parse:UserInteractions:finalize","duration":0.4,"entryType":"measure"},{"startTime":6055.92,"name":"lh:computed:TraceEngineResult:parse:Warnings:finalize","duration":2.79,"entryType":"measure"},{"startTime":6058.74,"name":"lh:computed:TraceEngineResult:parse:Workers:finalize","duration":2.3,"entryType":"measure"},{"startTime":6061.09,"name":"lh:computed:TraceEngineResult:parse:clone","duration":0.69,"entryType":"measure"},{"startTime":6061.94,"name":"lh:computed:TraceEngineResult:insights","duration":13.75,"entryType":"measure"},{"startTime":6062.14,"name":"lh:computed:TraceEngineResult:insights:CLSCulprits","duration":0.26,"entryType":"measure"},{"startTime":6062.41,"name":"lh:computed:TraceEngineResult:insights:DOMSize","duration":0.16,"entryType":"measure"},{"startTime":6062.57,"name":"lh:computed:TraceEngineResult:insights:DocumentLatency","duration":0.08,"entryType":"measure"},{"startTime":6062.66,"name":"lh:computed:TraceEngineResult:insights:DuplicatedJavaScript","duration":0.71,"entryType":"measure"},{"startTime":6063.37,"name":"lh:computed:TraceEngineResult:insights:FontDisplay","duration":0.08,"entryType":"measure"},{"startTime":6063.46,"name":"lh:computed:TraceEngineResult:insights:ForcedReflow","duration":0.17,"entryType":"measure"},{"startTime":6063.64,"name":"lh:computed:TraceEngineResult:insights:ImageDelivery","duration":1.78,"entryType":"measure"},{"startTime":6065.49,"name":"lh:computed:TraceEngineResult:insights:InteractionToNextPaint","duration":0.09,"entryType":"measure"},{"startTime":6065.59,"name":"lh:computed:TraceEngineResult:insights:LCPDiscovery","duration":0.11,"entryType":"measure"},{"startTime":6065.7,"name":"lh:computed:TraceEngineResult:insights:LCPPhases","duration":0.44,"entryType":"measure"},{"startTime":6066.16,"name":"lh:computed:TraceEngineResult:insights:NetworkDependencyTree","duration":0.14,"entryType":"measure"},{"startTime":6066.3,"name":"lh:computed:TraceEngineResult:insights:RenderBlocking","duration":0.09,"entryType":"measure"},{"startTime":6066.4,"name":"lh:computed:TraceEngineResult:insights:SlowCSSSelector","duration":0.13,"entryType":"measure"},{"startTime":6066.54,"name":"lh:computed:TraceEngineResult:insights:ThirdParties","duration":0.35,"entryType":"measure"},{"startTime":6066.89,"name":"lh:computed:TraceEngineResult:insights:UseCache","duration":0.1,"entryType":"measure"},{"startTime":6067,"name":"lh:computed:TraceEngineResult:insights:Viewport","duration":0.07,"entryType":"measure"},{"startTime":6067.43,"name":"lh:computed:TraceEngineResult:insights:createLanternContext","duration":6.8,"entryType":"measure"},{"startTime":6074.28,"name":"lh:computed:TraceEngineResult:insights:CLSCulprits","duration":0.06,"entryType":"measure"},{"startTime":6074.34,"name":"lh:computed:TraceEngineResult:insights:DOMSize","duration":0.04,"entryType":"measure"},{"startTime":6074.39,"name":"lh:computed:TraceEngineResult:insights:DocumentLatency","duration":0.12,"entryType":"measure"},{"startTime":6074.63,"name":"lh:computed:TraceEngineResult:insights:DuplicatedJavaScript","duration":0.1,"entryType":"measure"},{"startTime":6074.74,"name":"lh:computed:TraceEngineResult:insights:FontDisplay","duration":0.01,"entryType":"measure"},{"startTime":6074.75,"name":"lh:computed:TraceEngineResult:insights:ForcedReflow","duration":0.01,"entryType":"measure"},{"startTime":6074.77,"name":"lh:computed:TraceEngineResult:insights:ImageDelivery","duration":0.02,"entryType":"measure"},{"startTime":6074.79,"name":"lh:computed:TraceEngineResult:insights:InteractionToNextPaint","duration":0.01,"entryType":"measure"},{"startTime":6074.81,"name":"lh:computed:TraceEngineResult:insights:LCPDiscovery","duration":0.02,"entryType":"measure"},{"startTime":6074.84,"name":"lh:computed:TraceEngineResult:insights:LCPPhases","duration":0.09,"entryType":"measure"},{"startTime":6074.93,"name":"lh:computed:TraceEngineResult:insights:NetworkDependencyTree","duration":0.16,"entryType":"measure"},{"startTime":6075.09,"name":"lh:computed:TraceEngineResult:insights:RenderBlocking","duration":0.07,"entryType":"measure"},{"startTime":6075.17,"name":"lh:computed:TraceEngineResult:insights:SlowCSSSelector","duration":0.01,"entryType":"measure"},{"startTime":6075.18,"name":"lh:computed:TraceEngineResult:insights:ThirdParties","duration":0.24,"entryType":"measure"},{"startTime":6075.43,"name":"lh:computed:TraceEngineResult:insights:UseCache","duration":0.01,"entryType":"measure"},{"startTime":6075.45,"name":"lh:computed:TraceEngineResult:insights:Viewport","duration":0.02,"entryType":"measure"},{"startTime":6077.98,"name":"lh:computed:ProcessedNavigation","duration":0.33,"entryType":"measure"},{"startTime":6078.35,"name":"lh:computed:CumulativeLayoutShift","duration":9.25,"entryType":"measure"},{"startTime":6087.96,"name":"lh:computed:Responsiveness","duration":0.1,"entryType":"measure"},{"startTime":6092.76,"name":"lh:gather:getArtifact:ViewportDimensions","duration":1.12,"entryType":"measure"},{"startTime":6093.91,"name":"lh:gather:getArtifact:devtoolsLogs","duration":0.05,"entryType":"measure"},{"startTime":6093.97,"name":"lh:gather:getArtifact:traces","duration":0.01,"entryType":"measure"},{"startTime":6093.98,"name":"lh:gather:getArtifact:FullPageScreenshot","duration":1072.8,"entryType":"measure"},{"startTime":7166.95,"name":"lh:gather:getArtifact:BFCacheFailures","duration":533.52,"entryType":"measure"},{"startTime":7712.37,"name":"lh:runner:audit","duration":900.23,"entryType":"measure"},{"startTime":7712.45,"name":"lh:runner:auditing","duration":895.47,"entryType":"measure"},{"startTime":7713.12,"name":"lh:audit:is-on-https","duration":4.21,"entryType":"measure"},{"startTime":7717.68,"name":"lh:audit:redirects-http","duration":2.68,"entryType":"measure"},{"startTime":7720.55,"name":"lh:audit:viewport","duration":0.71,"entryType":"measure"},{"startTime":7720.78,"name":"lh:computed:ViewportMeta","duration":0.07,"entryType":"measure"},{"startTime":7721.51,"name":"lh:audit:first-contentful-paint","duration":7.59,"entryType":"measure"},{"startTime":7721.82,"name":"lh:computed:FirstContentfulPaint","duration":5.3,"entryType":"measure"},{"startTime":7723.86,"name":"lh:computed:LanternFirstContentfulPaint","duration":3.25,"entryType":"measure"},{"startTime":7724.14,"name":"lh:computed:PageDependencyGraph","duration":1.04,"entryType":"measure"},{"startTime":7725.21,"name":"lh:computed:LoadSimulator","duration":0.49,"entryType":"measure"},{"startTime":7725.24,"name":"lh:computed:NetworkAnalysis","duration":0.29,"entryType":"measure"},{"startTime":7729.3,"name":"lh:audit:largest-contentful-paint","duration":5,"entryType":"measure"},{"startTime":7729.74,"name":"lh:computed:LargestContentfulPaint","duration":3.33,"entryType":"measure"},{"startTime":7729.79,"name":"lh:computed:LanternLargestContentfulPaint","duration":3.22,"entryType":"measure"},{"startTime":7734.5,"name":"lh:audit:first-meaningful-paint","duration":0.55,"entryType":"measure"},{"startTime":7735.19,"name":"lh:audit:speed-index","duration":172.2,"entryType":"measure"},{"startTime":7735.38,"name":"lh:computed:SpeedIndex","duration":171.42,"entryType":"measure"},{"startTime":7735.44,"name":"lh:computed:LanternSpeedIndex","duration":171.34,"entryType":"measure"},{"startTime":7735.48,"name":"lh:computed:Speedline","duration":169.31,"entryType":"measure"},{"startTime":7907.41,"name":"lh:audit:screenshot-thumbnails","duration":0.62,"entryType":"measure"},{"startTime":7908.19,"name":"lh:audit:final-screenshot","duration":0.76,"entryType":"measure"},{"startTime":7908.42,"name":"lh:computed:Screenshots","duration":0.5,"entryType":"measure"},{"startTime":7909.55,"name":"lh:audit:total-blocking-time","duration":2.08,"entryType":"measure"},{"startTime":7910.08,"name":"lh:computed:TotalBlockingTime","duration":0.97,"entryType":"measure"},{"startTime":7910.16,"name":"lh:computed:LanternTotalBlockingTime","duration":0.88,"entryType":"measure"},{"startTime":7910.39,"name":"lh:computed:LanternInteractive","duration":0.41,"entryType":"measure"},{"startTime":7911.77,"name":"lh:audit:max-potential-fid","duration":1.11,"entryType":"measure"},{"startTime":7911.96,"name":"lh:computed:MaxPotentialFID","duration":0.39,"entryType":"measure"},{"startTime":7911.99,"name":"lh:computed:LanternMaxPotentialFID","duration":0.35,"entryType":"measure"},{"startTime":7913.01,"name":"lh:audit:cumulative-layout-shift","duration":0.54,"entryType":"measure"},{"startTime":7913.69,"name":"lh:audit:errors-in-console","duration":3.7,"entryType":"measure"},{"startTime":7913.98,"name":"lh:computed:JSBundles","duration":0.37,"entryType":"measure"},{"startTime":7917.75,"name":"lh:audit:server-response-time","duration":1.98,"entryType":"measure"},{"startTime":7919.89,"name":"lh:audit:interactive","duration":1.68,"entryType":"measure"},{"startTime":7920.07,"name":"lh:computed:Interactive","duration":0.06,"entryType":"measure"},{"startTime":7922.48,"name":"lh:audit:user-timings","duration":2.73,"entryType":"measure"},{"startTime":7923.66,"name":"lh:computed:UserTimings","duration":0.49,"entryType":"measure"},{"startTime":7925.36,"name":"lh:audit:critical-request-chains","duration":3.59,"entryType":"measure"},{"startTime":7925.57,"name":"lh:computed:CriticalRequestChains","duration":0.37,"entryType":"measure"},{"startTime":7929.52,"name":"lh:audit:redirects","duration":3.81,"entryType":"measure"},{"startTime":7934.43,"name":"lh:audit:image-aspect-ratio","duration":2.98,"entryType":"measure"},{"startTime":7938.22,"name":"lh:audit:image-size-responsive","duration":3.85,"entryType":"measure"},{"startTime":7942.51,"name":"lh:audit:deprecations","duration":0.7,"entryType":"measure"},{"startTime":7943.36,"name":"lh:audit:third-party-cookies","duration":0.48,"entryType":"measure"},{"startTime":7943.97,"name":"lh:audit:mainthread-work-breakdown","duration":3.22,"entryType":"measure"},{"startTime":7944.32,"name":"lh:computed:MainThreadTasks","duration":1.68,"entryType":"measure"},{"startTime":7947.8,"name":"lh:audit:bootup-time","duration":3.33,"entryType":"measure"},{"startTime":7949.17,"name":"lh:computed:TBTImpactTasks","duration":1.19,"entryType":"measure"},{"startTime":7951.26,"name":"lh:audit:uses-rel-preconnect","duration":0.9,"entryType":"measure"},{"startTime":7952.31,"name":"lh:audit:font-display","duration":1.32,"entryType":"measure"},{"startTime":7953.65,"name":"lh:audit:diagnostics","duration":0.24,"entryType":"measure"},{"startTime":7953.89,"name":"lh:audit:network-requests","duration":0.87,"entryType":"measure"},{"startTime":7954,"name":"lh:computed:EntityClassification","duration":0.61,"entryType":"measure"},{"startTime":7954.93,"name":"lh:audit:network-rtt","duration":0.87,"entryType":"measure"},{"startTime":7955.91,"name":"lh:audit:network-server-latency","duration":0.53,"entryType":"measure"},{"startTime":7956.45,"name":"lh:audit:main-thread-tasks","duration":0.18,"entryType":"measure"},{"startTime":7956.63,"name":"lh:audit:metrics","duration":2.55,"entryType":"measure"},{"startTime":7957.08,"name":"lh:computed:TimingSummary","duration":1.92,"entryType":"measure"},{"startTime":7957.6,"name":"lh:computed:FirstContentfulPaintAllFrames","duration":0.59,"entryType":"measure"},{"startTime":7958.23,"name":"lh:computed:LargestContentfulPaintAllFrames","duration":0.05,"entryType":"measure"},{"startTime":7958.32,"name":"lh:computed:LCPBreakdown","duration":0.36,"entryType":"measure"},{"startTime":7958.38,"name":"lh:computed:TimeToFirstByte","duration":0.1,"entryType":"measure"},{"startTime":7958.48,"name":"lh:computed:LCPImageRecord","duration":0.19,"entryType":"measure"},{"startTime":7959.21,"name":"lh:audit:resource-summary","duration":0.59,"entryType":"measure"},{"startTime":7959.29,"name":"lh:computed:ResourceSummary","duration":0.17,"entryType":"measure"},{"startTime":7959.94,"name":"lh:audit:third-party-summary","duration":1,"entryType":"measure"},{"startTime":7961.07,"name":"lh:audit:third-party-facades","duration":0.66,"entryType":"measure"},{"startTime":7961.83,"name":"lh:audit:largest-contentful-paint-element","duration":0.75,"entryType":"measure"},{"startTime":7962.7,"name":"lh:audit:lcp-lazy-loaded","duration":0.43,"entryType":"measure"},{"startTime":7963.25,"name":"lh:audit:layout-shifts","duration":1.52,"entryType":"measure"},{"startTime":7965.4,"name":"lh:audit:long-tasks","duration":0.98,"entryType":"measure"},{"startTime":7966.5,"name":"lh:audit:non-composited-animations","duration":0.44,"entryType":"measure"},{"startTime":7967.12,"name":"lh:audit:unsized-images","duration":0.77,"entryType":"measure"},{"startTime":7968.06,"name":"lh:audit:valid-source-maps","duration":0.6,"entryType":"measure"},{"startTime":7968.75,"name":"lh:audit:prioritize-lcp-image","duration":0.36,"entryType":"measure"},{"startTime":7969.21,"name":"lh:audit:csp-xss","duration":1.69,"entryType":"measure"},{"startTime":7971.03,"name":"lh:audit:has-hsts","duration":0.58,"entryType":"measure"},{"startTime":7971.81,"name":"lh:audit:origin-isolation","duration":0.45,"entryType":"measure"},{"startTime":7972.37,"name":"lh:audit:clickjacking-mitigation","duration":0.43,"entryType":"measure"},{"startTime":7972.81,"name":"lh:audit:script-treemap-data","duration":0.25,"entryType":"measure"},{"startTime":7972.96,"name":"lh:computed:ModuleDuplication","duration":0.09,"entryType":"measure"},{"startTime":7973.18,"name":"lh:audit:accesskeys","duration":0.46,"entryType":"measure"},{"startTime":7973.77,"name":"lh:audit:aria-allowed-attr","duration":2.43,"entryType":"measure"},{"startTime":7976.34,"name":"lh:audit:aria-allowed-role","duration":0.48,"entryType":"measure"},{"startTime":7976.93,"name":"lh:audit:aria-command-name","duration":0.5,"entryType":"measure"},{"startTime":7977.55,"name":"lh:audit:aria-conditional-attr","duration":0.51,"entryType":"measure"},{"startTime":7978.19,"name":"lh:audit:aria-deprecated-role","duration":0.52,"entryType":"measure"},{"startTime":7978.83,"name":"lh:audit:aria-dialog-name","duration":0.63,"entryType":"measure"},{"startTime":7979.58,"name":"lh:audit:aria-hidden-body","duration":3.56,"entryType":"measure"},{"startTime":7983.27,"name":"lh:audit:aria-hidden-focus","duration":0.59,"entryType":"measure"},{"startTime":7983.98,"name":"lh:audit:aria-input-field-name","duration":0.6,"entryType":"measure"},{"startTime":7984.68,"name":"lh:audit:aria-meter-name","duration":0.63,"entryType":"measure"},{"startTime":7985.42,"name":"lh:audit:aria-progressbar-name","duration":0.66,"entryType":"measure"},{"startTime":7986.19,"name":"lh:audit:aria-prohibited-attr","duration":0.91,"entryType":"measure"},{"startTime":7987.24,"name":"lh:audit:aria-required-attr","duration":0.74,"entryType":"measure"},{"startTime":7988.12,"name":"lh:audit:aria-required-children","duration":3.85,"entryType":"measure"},{"startTime":7992.1,"name":"lh:audit:aria-required-parent","duration":0.81,"entryType":"measure"},{"startTime":7993.02,"name":"lh:audit:aria-roles","duration":0.79,"entryType":"measure"},{"startTime":7994.12,"name":"lh:audit:aria-text","duration":0.93,"entryType":"measure"},{"startTime":7995.16,"name":"lh:audit:aria-toggle-field-name","duration":0.88,"entryType":"measure"},{"startTime":7996.25,"name":"lh:audit:aria-tooltip-name","duration":2.54,"entryType":"measure"},{"startTime":7999.13,"name":"lh:audit:aria-treeitem-name","duration":1.88,"entryType":"measure"},{"startTime":8001.15,"name":"lh:audit:aria-valid-attr-value","duration":2.03,"entryType":"measure"},{"startTime":8003.34,"name":"lh:audit:aria-valid-attr","duration":3.17,"entryType":"measure"},{"startTime":8006.72,"name":"lh:audit:button-name","duration":1.31,"entryType":"measure"},{"startTime":8008.17,"name":"lh:audit:bypass","duration":1.69,"entryType":"measure"},{"startTime":8009.99,"name":"lh:audit:color-contrast","duration":9.27,"entryType":"measure"},{"startTime":8019.41,"name":"lh:audit:definition-list","duration":1.2,"entryType":"measure"},{"startTime":8020.74,"name":"lh:audit:dlitem","duration":1.66,"entryType":"measure"},{"startTime":8022.54,"name":"lh:audit:document-title","duration":4.06,"entryType":"measure"},{"startTime":8026.81,"name":"lh:audit:duplicate-id-aria","duration":1.11,"entryType":"measure"},{"startTime":8028.03,"name":"lh:audit:empty-heading","duration":1.3,"entryType":"measure"},{"startTime":8029.45,"name":"lh:audit:form-field-multiple-labels","duration":2.74,"entryType":"measure"},{"startTime":8032.33,"name":"lh:audit:frame-title","duration":1.46,"entryType":"measure"},{"startTime":8033.98,"name":"lh:audit:heading-order","duration":2.72,"entryType":"measure"},{"startTime":8042.08,"name":"lh:audit:html-has-lang","duration":2.82,"entryType":"measure"},{"startTime":8045.04,"name":"lh:audit:html-lang-valid","duration":4.3,"entryType":"measure"},{"startTime":8049.49,"name":"lh:audit:html-xml-lang-mismatch","duration":1.41,"entryType":"measure"},{"startTime":8051.04,"name":"lh:audit:identical-links-same-purpose","duration":1.75,"entryType":"measure"},{"startTime":8052.99,"name":"lh:audit:image-alt","duration":1.99,"entryType":"measure"},{"startTime":8056.11,"name":"lh:audit:image-redundant-alt","duration":10.58,"entryType":"measure"},{"startTime":8066.83,"name":"lh:audit:input-button-name","duration":2.97,"entryType":"measure"},{"startTime":8069.95,"name":"lh:audit:input-image-alt","duration":9.57,"entryType":"measure"},{"startTime":8079.78,"name":"lh:audit:label-content-name-mismatch","duration":5.37,"entryType":"measure"},{"startTime":8085.28,"name":"lh:audit:label","duration":2.16,"entryType":"measure"},{"startTime":8087.69,"name":"lh:audit:landmark-one-main","duration":3.38,"entryType":"measure"},{"startTime":8091.2,"name":"lh:audit:link-name","duration":1.68,"entryType":"measure"},{"startTime":8093.02,"name":"lh:audit:link-in-text-block","duration":1.65,"entryType":"measure"},{"startTime":8094.82,"name":"lh:audit:list","duration":11.04,"entryType":"measure"},{"startTime":8106.06,"name":"lh:audit:listitem","duration":1.85,"entryType":"measure"},{"startTime":8108.05,"name":"lh:audit:meta-refresh","duration":2.09,"entryType":"measure"},{"startTime":8110.28,"name":"lh:audit:meta-viewport","duration":1.82,"entryType":"measure"},{"startTime":8112.24,"name":"lh:audit:object-alt","duration":2.59,"entryType":"measure"},{"startTime":8115.55,"name":"lh:audit:select-name","duration":3.47,"entryType":"measure"},{"startTime":8119.16,"name":"lh:audit:skip-link","duration":2.34,"entryType":"measure"},{"startTime":8121.85,"name":"lh:audit:tabindex","duration":16.76,"entryType":"measure"},{"startTime":8139.25,"name":"lh:audit:table-duplicate-name","duration":3.27,"entryType":"measure"},{"startTime":8142.92,"name":"lh:audit:table-fake-caption","duration":2.21,"entryType":"measure"},{"startTime":8145.27,"name":"lh:audit:target-size","duration":1.84,"entryType":"measure"},{"startTime":8147.23,"name":"lh:audit:td-has-header","duration":4.8,"entryType":"measure"},{"startTime":8152.17,"name":"lh:audit:td-headers-attr","duration":9.73,"entryType":"measure"},{"startTime":8162.15,"name":"lh:audit:th-has-data-cells","duration":17.64,"entryType":"measure"},{"startTime":8183.23,"name":"lh:audit:valid-lang","duration":15.11,"entryType":"measure"},{"startTime":8199.47,"name":"lh:audit:video-caption","duration":4.98,"entryType":"measure"},{"startTime":8204.47,"name":"lh:audit:custom-controls-labels","duration":0.46,"entryType":"measure"},{"startTime":8205,"name":"lh:audit:custom-controls-roles","duration":0.02,"entryType":"measure"},{"startTime":8205.02,"name":"lh:audit:focus-traps","duration":0.01,"entryType":"measure"},{"startTime":8205.03,"name":"lh:audit:focusable-controls","duration":0.01,"entryType":"measure"},{"startTime":8205.05,"name":"lh:audit:interactive-element-affordance","duration":0.01,"entryType":"measure"},{"startTime":8205.06,"name":"lh:audit:logical-tab-order","duration":0.01,"entryType":"measure"},{"startTime":8205.07,"name":"lh:audit:managed-focus","duration":0.01,"entryType":"measure"},{"startTime":8205.08,"name":"lh:audit:offscreen-content-hidden","duration":1.27,"entryType":"measure"},{"startTime":8221.92,"name":"lh:audit:use-landmarks","duration":0.06,"entryType":"measure"},{"startTime":8221.99,"name":"lh:audit:visual-order-follows-dom","duration":0.01,"entryType":"measure"},{"startTime":8222.18,"name":"lh:audit:uses-long-cache-ttl","duration":17.96,"entryType":"measure"},{"startTime":8240.6,"name":"lh:audit:total-byte-weight","duration":3.99,"entryType":"measure"},{"startTime":8245.46,"name":"lh:audit:offscreen-images","duration":9.2,"entryType":"measure"},{"startTime":8257.36,"name":"lh:audit:render-blocking-resources","duration":7.39,"entryType":"measure"},{"startTime":8261.36,"name":"lh:computed:UnusedCSS","duration":0.23,"entryType":"measure"},{"startTime":8261.63,"name":"lh:computed:NavigationInsights","duration":0.06,"entryType":"measure"},{"startTime":8261.98,"name":"lh:computed:FirstContentfulPaint","duration":0.18,"entryType":"measure"},{"startTime":8264.99,"name":"lh:audit:unminified-css","duration":1.85,"entryType":"measure"},{"startTime":8267.43,"name":"lh:audit:unminified-javascript","duration":1.57,"entryType":"measure"},{"startTime":8269.1,"name":"lh:audit:unused-css-rules","duration":4.49,"entryType":"measure"},{"startTime":8273.82,"name":"lh:audit:unused-javascript","duration":3.45,"entryType":"measure"},{"startTime":8277.38,"name":"lh:audit:modern-image-formats","duration":6.43,"entryType":"measure"},{"startTime":8283.93,"name":"lh:audit:uses-optimized-images","duration":2.25,"entryType":"measure"},{"startTime":8286.28,"name":"lh:audit:uses-text-compression","duration":1.1,"entryType":"measure"},{"startTime":8287.47,"name":"lh:audit:uses-responsive-images","duration":1.75,"entryType":"measure"},{"startTime":8287.68,"name":"lh:computed:ImageRecords","duration":0.19,"entryType":"measure"},{"startTime":8289.34,"name":"lh:audit:efficient-animated-content","duration":0.96,"entryType":"measure"},{"startTime":8290.39,"name":"lh:audit:duplicated-javascript","duration":0.77,"entryType":"measure"},{"startTime":8291.34,"name":"lh:audit:legacy-javascript","duration":0.87,"entryType":"measure"},{"startTime":8292.35,"name":"lh:audit:doctype","duration":0.47,"entryType":"measure"},{"startTime":8292.94,"name":"lh:audit:charset","duration":0.72,"entryType":"measure"},{"startTime":8293.79,"name":"lh:audit:dom-size","duration":1.18,"entryType":"measure"},{"startTime":8295.13,"name":"lh:audit:geolocation-on-start","duration":1.08,"entryType":"measure"},{"startTime":8296.35,"name":"lh:audit:inspector-issues","duration":0.48,"entryType":"measure"},{"startTime":8296.96,"name":"lh:audit:no-document-write","duration":2.46,"entryType":"measure"},{"startTime":8299.53,"name":"lh:audit:js-libraries","duration":0.41,"entryType":"measure"},{"startTime":8300.06,"name":"lh:audit:notification-on-start","duration":0.83,"entryType":"measure"},{"startTime":8301.04,"name":"lh:audit:paste-preventing-inputs","duration":0.47,"entryType":"measure"},{"startTime":8301.61,"name":"lh:audit:uses-http2","duration":1.03,"entryType":"measure"},{"startTime":8302.77,"name":"lh:audit:uses-passive-event-listeners","duration":0.61,"entryType":"measure"},{"startTime":8303.51,"name":"lh:audit:meta-description","duration":0.43,"entryType":"measure"},{"startTime":8305.59,"name":"lh:audit:http-status-code","duration":1.45,"entryType":"measure"},{"startTime":8307.35,"name":"lh:audit:font-size","duration":3.4,"entryType":"measure"},{"startTime":8311.23,"name":"lh:audit:link-text","duration":1.21,"entryType":"measure"},{"startTime":8312.6,"name":"lh:audit:crawlable-anchors","duration":0.6,"entryType":"measure"},{"startTime":8313.34,"name":"lh:audit:is-crawlable","duration":2.38,"entryType":"measure"},{"startTime":8315.85,"name":"lh:audit:robots-txt","duration":0.55,"entryType":"measure"},{"startTime":8316.53,"name":"lh:audit:hreflang","duration":0.45,"entryType":"measure"},{"startTime":8317.1,"name":"lh:audit:canonical","duration":0.49,"entryType":"measure"},{"startTime":8318.08,"name":"lh:audit:structured-data","duration":0.57,"entryType":"measure"},{"startTime":8318.82,"name":"lh:audit:bf-cache","duration":0.71,"entryType":"measure"},{"startTime":8319.69,"name":"lh:audit:cls-culprits-insight","duration":6.43,"entryType":"measure"},{"startTime":8326.8,"name":"lh:audit:document-latency-insight","duration":1.7,"entryType":"measure"},{"startTime":8328.89,"name":"lh:audit:dom-size-insight","duration":1.06,"entryType":"measure"},{"startTime":8335.96,"name":"lh:audit:duplicated-javascript-insight","duration":3.19,"entryType":"measure"},{"startTime":8340.32,"name":"lh:audit:font-display-insight","duration":0.79,"entryType":"measure"},{"startTime":8341.23,"name":"lh:audit:forced-reflow-insight","duration":0.47,"entryType":"measure"},{"startTime":8344.57,"name":"lh:audit:image-delivery-insight","duration":1.15,"entryType":"measure"},{"startTime":8346.33,"name":"lh:audit:interaction-to-next-paint-insight","duration":30.24,"entryType":"measure"},{"startTime":8378,"name":"lh:audit:lcp-discovery-insight","duration":7.5,"entryType":"measure"},{"startTime":8395.64,"name":"lh:audit:lcp-phases-insight","duration":3.73,"entryType":"measure"},{"startTime":8400.54,"name":"lh:audit:network-dependency-tree-insight","duration":64.26,"entryType":"measure"},{"startTime":8464.97,"name":"lh:audit:render-blocking-insight","duration":3.39,"entryType":"measure"},{"startTime":8493.67,"name":"lh:audit:slow-css-selector-insight","duration":102.75,"entryType":"measure"},{"startTime":8599.72,"name":"lh:audit:third-parties-insight","duration":1.51,"entryType":"measure"},{"startTime":8601.37,"name":"lh:audit:use-cache-insight","duration":1.26,"entryType":"measure"},{"startTime":8603.13,"name":"lh:audit:viewport-insight","duration":4.75,"entryType":"measure"},{"startTime":8607.92,"name":"lh:runner:generate","duration":4.63,"entryType":"measure"}],"total":7244.780000000001},"i18n":{"rendererFormattedStrings":{"calculatorLink":"See calculator.","collapseView":"Collapse view","crcInitialNavigation":"Initial Navigation","crcLongestDurationLabel":"Maximum critical path latency:","dropdownCopyJSON":"Copy JSON","dropdownDarkTheme":"Toggle Dark Theme","dropdownInsightsToggle":"Toggle experimental insights","dropdownPrintExpanded":"Print Expanded","dropdownPrintSummary":"Print Summary","dropdownSaveGist":"Save as Gist","dropdownSaveHTML":"Save as HTML","dropdownSaveJSON":"Save as JSON","dropdownViewUnthrottledTrace":"View Unthrottled Trace","dropdownViewer":"Open in Viewer","errorLabel":"Error!","errorMissingAuditInfo":"Report error: no audit information","expandView":"Expand view","firstPartyChipLabel":"1st party","footerIssue":"File an issue","hide":"Hide","labDataTitle":"Lab Data","lsPerformanceCategoryDescription":"[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.","manualAuditsGroupTitle":"Additional items to manually check","notApplicableAuditsGroupTitle":"Not applicable","openInANewTabTooltip":"Open in a new tab","opportunityResourceColumnLabel":"Opportunity","opportunitySavingsColumnLabel":"Estimated Savings","passedAuditsGroupTitle":"Passed audits","runtimeAnalysisWindow":"Initial page load","runtimeAnalysisWindowSnapshot":"Point-in-time snapshot","runtimeAnalysisWindowTimespan":"User interactions timespan","runtimeCustom":"Custom throttling","runtimeDesktopEmulation":"Emulated Desktop","runtimeMobileEmulation":"Emulated Moto G Power","runtimeNoEmulation":"No emulation","runtimeSettingsAxeVersion":"Axe version","runtimeSettingsBenchmark":"Unthrottled CPU/Memory Power","runtimeSettingsCPUThrottling":"CPU throttling","runtimeSettingsDevice":"Device","runtimeSettingsNetworkThrottling":"Network throttling","runtimeSettingsScreenEmulation":"Screen emulation","runtimeSettingsUANetwork":"User agent (network)","runtimeSingleLoad":"Single page session","runtimeSingleLoadTooltip":"This data is taken from a single page session, as opposed to field data summarizing many sessions.","runtimeSlow4g":"Slow 4G throttling","runtimeUnknown":"Unknown","show":"Show","showRelevantAudits":"Show audits relevant to:","snippetCollapseButtonLabel":"Collapse snippet","snippetExpandButtonLabel":"Expand snippet","thirdPartyResourcesLabel":"Show 3rd-party resources","throttlingProvided":"Provided by environment","toplevelWarningsMessage":"There were issues affecting this run of Lighthouse:","unattributable":"Unattributable","varianceDisclaimer":"Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.","viewTraceLabel":"View Trace","viewTreemapLabel":"View Treemap","warningAuditsGroupTitle":"Passed audits but with warnings","warningHeader":"Warnings: "},"icuMessagePaths":{"core/audits/is-on-https.js | title":["audits[is-on-https].title"],"core/audits/is-on-https.js | description":["audits[is-on-https].description"],"core/audits/redirects-http.js | title":["audits[redirects-http].title"],"core/audits/redirects-http.js | description":["audits[redirects-http].description"],"core/audits/viewport.js | failureTitle":["audits.viewport.title"],"core/audits/viewport.js | description":["audits.viewport.description"],"core/audits/viewport.js | explanationNoTag":["audits.viewport.explanation"],"core/lib/i18n/i18n.js | firstContentfulPaintMetric":["audits[first-contentful-paint].title"],"core/audits/metrics/first-contentful-paint.js | description":["audits[first-contentful-paint].description"],"core/lib/i18n/i18n.js | seconds":[{"values":{"timeInMs":888.8973},"path":"audits[first-contentful-paint].displayValue"},{"values":{"timeInMs":888.8973},"path":"audits[largest-contentful-paint].displayValue"},{"values":{"timeInMs":888.8973},"path":"audits[speed-index].displayValue"},{"values":{"timeInMs":888.8973000000001},"path":"audits.interactive.displayValue"},{"values":{"timeInMs":15.797999999999995},"path":"audits[mainthread-work-breakdown].displayValue"}],"core/lib/i18n/i18n.js | largestContentfulPaintMetric":["audits[largest-contentful-paint].title"],"core/audits/metrics/largest-contentful-paint.js | description":["audits[largest-contentful-paint].description"],"core/lib/i18n/i18n.js | firstMeaningfulPaintMetric":["audits[first-meaningful-paint].title"],"core/audits/metrics/first-meaningful-paint.js | description":["audits[first-meaningful-paint].description"],"core/lib/i18n/i18n.js | speedIndexMetric":["audits[speed-index].title"],"core/audits/metrics/speed-index.js | description":["audits[speed-index].description"],"core/lib/i18n/i18n.js | totalBlockingTimeMetric":["audits[total-blocking-time].title"],"core/audits/metrics/total-blocking-time.js | description":["audits[total-blocking-time].description"],"core/lib/i18n/i18n.js | ms":[{"values":{"timeInMs":0},"path":"audits[total-blocking-time].displayValue"},{"values":{"timeInMs":16},"path":"audits[max-potential-fid].displayValue"},{"values":{"timeInMs":0.0277},"path":"audits[network-rtt].displayValue"},{"values":{"timeInMs":134.89729999999997},"path":"audits[network-server-latency].displayValue"},{"values":{"timeInMs":888.8973},"path":"audits[largest-contentful-paint-element].displayValue"}],"core/lib/i18n/i18n.js | maxPotentialFIDMetric":["audits[max-potential-fid].title"],"core/audits/metrics/max-potential-fid.js | description":["audits[max-potential-fid].description"],"core/lib/i18n/i18n.js | cumulativeLayoutShiftMetric":["audits[cumulative-layout-shift].title"],"core/audits/metrics/cumulative-layout-shift.js | description":["audits[cumulative-layout-shift].description"],"core/audits/errors-in-console.js | title":["audits[errors-in-console].title"],"core/audits/errors-in-console.js | description":["audits[errors-in-console].description"],"core/audits/server-response-time.js | title":["audits[server-response-time].title"],"core/audits/server-response-time.js | description":["audits[server-response-time].description"],"core/audits/server-response-time.js | displayValue":[{"values":{"timeInMs":134.802},"path":"audits[server-response-time].displayValue"}],"core/lib/i18n/i18n.js | columnURL":["audits[server-response-time].details.headings[0].label","audits[network-rtt].details.headings[0].label","audits[network-server-latency].details.headings[0].label","audits[total-byte-weight].details.headings[0].label"],"core/lib/i18n/i18n.js | columnTimeSpent":["audits[server-response-time].details.headings[1].label","audits[mainthread-work-breakdown].details.headings[1].label","audits[network-rtt].details.headings[1].label","audits[network-server-latency].details.headings[1].label"],"core/lib/i18n/i18n.js | interactiveMetric":["audits.interactive.title"],"core/audits/metrics/interactive.js | description":["audits.interactive.description"],"core/audits/user-timings.js | title":["audits[user-timings].title"],"core/audits/user-timings.js | description":["audits[user-timings].description"],"core/audits/critical-request-chains.js | title":["audits[critical-request-chains].title"],"core/audits/critical-request-chains.js | description":["audits[critical-request-chains].description"],"core/audits/redirects.js | title":["audits.redirects.title"],"core/audits/redirects.js | description":["audits.redirects.description"],"core/audits/image-aspect-ratio.js | title":["audits[image-aspect-ratio].title"],"core/audits/image-aspect-ratio.js | description":["audits[image-aspect-ratio].description"],"core/audits/image-size-responsive.js | title":["audits[image-size-responsive].title"],"core/audits/image-size-responsive.js | description":["audits[image-size-responsive].description"],"core/audits/deprecations.js | title":["audits.deprecations.title"],"core/audits/deprecations.js | description":["audits.deprecations.description"],"core/audits/third-party-cookies.js | title":["audits[third-party-cookies].title"],"core/audits/third-party-cookies.js | description":["audits[third-party-cookies].description"],"core/audits/mainthread-work-breakdown.js | title":["audits[mainthread-work-breakdown].title"],"core/audits/mainthread-work-breakdown.js | description":["audits[mainthread-work-breakdown].description"],"core/audits/mainthread-work-breakdown.js | columnCategory":["audits[mainthread-work-breakdown].details.headings[0].label"],"core/audits/bootup-time.js | title":["audits[bootup-time].title"],"core/audits/bootup-time.js | description":["audits[bootup-time].description"],"core/audits/uses-rel-preconnect.js | title":["audits[uses-rel-preconnect].title"],"core/audits/uses-rel-preconnect.js | description":["audits[uses-rel-preconnect].description"],"core/audits/font-display.js | title":["audits[font-display].title"],"core/audits/font-display.js | description":["audits[font-display].description"],"core/audits/network-rtt.js | title":["audits[network-rtt].title"],"core/audits/network-rtt.js | description":["audits[network-rtt].description"],"core/audits/network-server-latency.js | title":["audits[network-server-latency].title"],"core/audits/network-server-latency.js | description":["audits[network-server-latency].description"],"core/lib/i18n/i18n.js | columnResourceType":["audits[resource-summary].details.headings[0].label"],"core/lib/i18n/i18n.js | columnRequests":["audits[resource-summary].details.headings[1].label"],"core/lib/i18n/i18n.js | columnTransferSize":["audits[resource-summary].details.headings[2].label","audits[total-byte-weight].details.headings[1].label"],"core/lib/i18n/i18n.js | documentResourceType":["audits[resource-summary].details.items[0].label"],"core/lib/i18n/i18n.js | total":["audits[resource-summary].details.items[1].label"],"core/lib/i18n/i18n.js | stylesheetResourceType":["audits[resource-summary].details.items[2].label"],"core/lib/i18n/i18n.js | imageResourceType":["audits[resource-summary].details.items[3].label"],"core/lib/i18n/i18n.js | mediaResourceType":["audits[resource-summary].details.items[4].label"],"core/lib/i18n/i18n.js | fontResourceType":["audits[resource-summary].details.items[5].label"],"core/lib/i18n/i18n.js | scriptResourceType":["audits[resource-summary].details.items[6].label"],"core/lib/i18n/i18n.js | otherResourceType":["audits[resource-summary].details.items[7].label"],"core/lib/i18n/i18n.js | thirdPartyResourceType":["audits[resource-summary].details.items[8].label"],"core/audits/third-party-summary.js | title":["audits[third-party-summary].title"],"core/audits/third-party-summary.js | description":["audits[third-party-summary].description"],"core/audits/third-party-facades.js | title":["audits[third-party-facades].title"],"core/audits/third-party-facades.js | description":["audits[third-party-facades].description"],"core/audits/largest-contentful-paint-element.js | title":["audits[largest-contentful-paint-element].title"],"core/audits/largest-contentful-paint-element.js | description":["audits[largest-contentful-paint-element].description"],"core/lib/i18n/i18n.js | columnElement":["audits[largest-contentful-paint-element].details.items[0].headings[0].label","audits[dom-size].details.headings[1].label","audits[dom-size-insight].details.headings[1].label","audits[lcp-phases-insight].details.items[0].headings[0].label"],"core/audits/largest-contentful-paint-element.js | columnPhase":["audits[largest-contentful-paint-element].details.items[1].headings[0].label"],"core/audits/largest-contentful-paint-element.js | columnPercentOfLCP":["audits[largest-contentful-paint-element].details.items[1].headings[1].label"],"core/audits/largest-contentful-paint-element.js | columnTiming":["audits[largest-contentful-paint-element].details.items[1].headings[2].label"],"core/audits/largest-contentful-paint-element.js | itemTTFB":["audits[largest-contentful-paint-element].details.items[1].items[0].phase"],"core/audits/largest-contentful-paint-element.js | itemLoadDelay":["audits[largest-contentful-paint-element].details.items[1].items[1].phase"],"core/audits/largest-contentful-paint-element.js | itemLoadTime":["audits[largest-contentful-paint-element].details.items[1].items[2].phase"],"core/audits/largest-contentful-paint-element.js | itemRenderDelay":["audits[largest-contentful-paint-element].details.items[1].items[3].phase"],"core/audits/lcp-lazy-loaded.js | title":["audits[lcp-lazy-loaded].title"],"core/audits/lcp-lazy-loaded.js | description":["audits[lcp-lazy-loaded].description"],"core/audits/layout-shifts.js | title":["audits[layout-shifts].title"],"core/audits/layout-shifts.js | description":["audits[layout-shifts].description"],"core/audits/long-tasks.js | title":["audits[long-tasks].title"],"core/audits/long-tasks.js | description":["audits[long-tasks].description"],"core/audits/non-composited-animations.js | title":["audits[non-composited-animations].title"],"core/audits/non-composited-animations.js | description":["audits[non-composited-animations].description"],"core/audits/unsized-images.js | title":["audits[unsized-images].title"],"core/audits/unsized-images.js | description":["audits[unsized-images].description"],"core/audits/valid-source-maps.js | title":["audits[valid-source-maps].title"],"core/audits/valid-source-maps.js | description":["audits[valid-source-maps].description"],"core/audits/prioritize-lcp-image.js | title":["audits[prioritize-lcp-image].title"],"core/audits/prioritize-lcp-image.js | description":["audits[prioritize-lcp-image].description"],"core/audits/csp-xss.js | title":["audits[csp-xss].title"],"core/audits/csp-xss.js | description":["audits[csp-xss].description"],"core/lib/i18n/i18n.js | columnDescription":["audits[csp-xss].details.headings[0].label","audits[has-hsts].details.headings[0].label","audits[origin-isolation].details.headings[0].label","audits[clickjacking-mitigation].details.headings[0].label"],"core/audits/csp-xss.js | columnDirective":["audits[csp-xss].details.headings[1].label"],"core/audits/csp-xss.js | columnSeverity":["audits[csp-xss].details.headings[2].label"],"core/lib/csp-evaluator.js | missingScriptSrc":["audits[csp-xss].details.items[0].description"],"core/lib/i18n/i18n.js | itemSeverityHigh":["audits[csp-xss].details.items[0].severity","audits[csp-xss].details.items[1].severity","audits[origin-isolation].details.items[0].severity","audits[clickjacking-mitigation].details.items[0].severity"],"core/lib/csp-evaluator.js | missingObjectSrc":["audits[csp-xss].details.items[1].description"],"core/audits/has-hsts.js | title":["audits[has-hsts].title"],"core/audits/has-hsts.js | description":["audits[has-hsts].description"],"core/audits/has-hsts.js | columnDirective":["audits[has-hsts].details.headings[1].label"],"core/audits/has-hsts.js | columnSeverity":["audits[has-hsts].details.headings[2].label"],"core/audits/has-hsts.js | noPreload":["audits[has-hsts].details.items[0].description"],"core/lib/i18n/i18n.js | itemSeverityMedium":["audits[has-hsts].details.items[0].severity"],"core/audits/origin-isolation.js | title":["audits[origin-isolation].title"],"core/audits/origin-isolation.js | description":["audits[origin-isolation].description"],"core/audits/origin-isolation.js | columnDirective":["audits[origin-isolation].details.headings[1].label"],"core/audits/origin-isolation.js | columnSeverity":["audits[origin-isolation].details.headings[2].label"],"core/audits/origin-isolation.js | noCoop":["audits[origin-isolation].details.items[0].description"],"core/audits/clickjacking-mitigation.js | title":["audits[clickjacking-mitigation].title"],"core/audits/clickjacking-mitigation.js | description":["audits[clickjacking-mitigation].description"],"core/audits/clickjacking-mitigation.js | columnSeverity":["audits[clickjacking-mitigation].details.headings[1].label"],"core/audits/clickjacking-mitigation.js | noClickjackingMitigation":["audits[clickjacking-mitigation].details.items[0].description"],"core/audits/accessibility/accesskeys.js | title":["audits.accesskeys.title"],"core/audits/accessibility/accesskeys.js | description":["audits.accesskeys.description"],"core/audits/accessibility/aria-allowed-attr.js | title":["audits[aria-allowed-attr].title"],"core/audits/accessibility/aria-allowed-attr.js | description":["audits[aria-allowed-attr].description"],"core/audits/accessibility/aria-allowed-role.js | title":["audits[aria-allowed-role].title"],"core/audits/accessibility/aria-allowed-role.js | description":["audits[aria-allowed-role].description"],"core/audits/accessibility/aria-command-name.js | title":["audits[aria-command-name].title"],"core/audits/accessibility/aria-command-name.js | description":["audits[aria-command-name].description"],"core/audits/accessibility/aria-conditional-attr.js | title":["audits[aria-conditional-attr].title"],"core/audits/accessibility/aria-conditional-attr.js | description":["audits[aria-conditional-attr].description"],"core/audits/accessibility/aria-deprecated-role.js | title":["audits[aria-deprecated-role].title"],"core/audits/accessibility/aria-deprecated-role.js | description":["audits[aria-deprecated-role].description"],"core/audits/accessibility/aria-dialog-name.js | title":["audits[aria-dialog-name].title"],"core/audits/accessibility/aria-dialog-name.js | description":["audits[aria-dialog-name].description"],"core/audits/accessibility/aria-hidden-body.js | title":["audits[aria-hidden-body].title"],"core/audits/accessibility/aria-hidden-body.js | description":["audits[aria-hidden-body].description"],"core/audits/accessibility/aria-hidden-focus.js | title":["audits[aria-hidden-focus].title"],"core/audits/accessibility/aria-hidden-focus.js | description":["audits[aria-hidden-focus].description"],"core/audits/accessibility/aria-input-field-name.js | title":["audits[aria-input-field-name].title"],"core/audits/accessibility/aria-input-field-name.js | description":["audits[aria-input-field-name].description"],"core/audits/accessibility/aria-meter-name.js | title":["audits[aria-meter-name].title"],"core/audits/accessibility/aria-meter-name.js | description":["audits[aria-meter-name].description"],"core/audits/accessibility/aria-progressbar-name.js | title":["audits[aria-progressbar-name].title"],"core/audits/accessibility/aria-progressbar-name.js | description":["audits[aria-progressbar-name].description"],"core/audits/accessibility/aria-prohibited-attr.js | title":["audits[aria-prohibited-attr].title"],"core/audits/accessibility/aria-prohibited-attr.js | description":["audits[aria-prohibited-attr].description"],"core/audits/accessibility/aria-required-attr.js | title":["audits[aria-required-attr].title"],"core/audits/accessibility/aria-required-attr.js | description":["audits[aria-required-attr].description"],"core/audits/accessibility/aria-required-children.js | title":["audits[aria-required-children].title"],"core/audits/accessibility/aria-required-children.js | description":["audits[aria-required-children].description"],"core/audits/accessibility/aria-required-parent.js | title":["audits[aria-required-parent].title"],"core/audits/accessibility/aria-required-parent.js | description":["audits[aria-required-parent].description"],"core/audits/accessibility/aria-roles.js | title":["audits[aria-roles].title"],"core/audits/accessibility/aria-roles.js | description":["audits[aria-roles].description"],"core/audits/accessibility/aria-text.js | title":["audits[aria-text].title"],"core/audits/accessibility/aria-text.js | description":["audits[aria-text].description"],"core/audits/accessibility/aria-toggle-field-name.js | title":["audits[aria-toggle-field-name].title"],"core/audits/accessibility/aria-toggle-field-name.js | description":["audits[aria-toggle-field-name].description"],"core/audits/accessibility/aria-tooltip-name.js | title":["audits[aria-tooltip-name].title"],"core/audits/accessibility/aria-tooltip-name.js | description":["audits[aria-tooltip-name].description"],"core/audits/accessibility/aria-treeitem-name.js | title":["audits[aria-treeitem-name].title"],"core/audits/accessibility/aria-treeitem-name.js | description":["audits[aria-treeitem-name].description"],"core/audits/accessibility/aria-valid-attr-value.js | title":["audits[aria-valid-attr-value].title"],"core/audits/accessibility/aria-valid-attr-value.js | description":["audits[aria-valid-attr-value].description"],"core/audits/accessibility/aria-valid-attr.js | title":["audits[aria-valid-attr].title"],"core/audits/accessibility/aria-valid-attr.js | description":["audits[aria-valid-attr].description"],"core/audits/accessibility/button-name.js | title":["audits[button-name].title"],"core/audits/accessibility/button-name.js | description":["audits[button-name].description"],"core/audits/accessibility/bypass.js | title":["audits.bypass.title"],"core/audits/accessibility/bypass.js | description":["audits.bypass.description"],"core/audits/accessibility/color-contrast.js | title":["audits[color-contrast].title"],"core/audits/accessibility/color-contrast.js | description":["audits[color-contrast].description"],"core/audits/accessibility/definition-list.js | title":["audits[definition-list].title"],"core/audits/accessibility/definition-list.js | description":["audits[definition-list].description"],"core/audits/accessibility/dlitem.js | title":["audits.dlitem.title"],"core/audits/accessibility/dlitem.js | description":["audits.dlitem.description"],"core/audits/accessibility/document-title.js | failureTitle":["audits[document-title].title"],"core/audits/accessibility/document-title.js | description":["audits[document-title].description"],"core/lib/i18n/i18n.js | columnFailingElem":["audits[document-title].details.headings[0].label","audits[html-has-lang].details.headings[0].label","audits[landmark-one-main].details.headings[0].label"],"core/audits/accessibility/duplicate-id-aria.js | title":["audits[duplicate-id-aria].title"],"core/audits/accessibility/duplicate-id-aria.js | description":["audits[duplicate-id-aria].description"],"core/audits/accessibility/empty-heading.js | title":["audits[empty-heading].title"],"core/audits/accessibility/empty-heading.js | description":["audits[empty-heading].description"],"core/audits/accessibility/form-field-multiple-labels.js | title":["audits[form-field-multiple-labels].title"],"core/audits/accessibility/form-field-multiple-labels.js | description":["audits[form-field-multiple-labels].description"],"core/audits/accessibility/frame-title.js | title":["audits[frame-title].title"],"core/audits/accessibility/frame-title.js | description":["audits[frame-title].description"],"core/audits/accessibility/heading-order.js | title":["audits[heading-order].title"],"core/audits/accessibility/heading-order.js | description":["audits[heading-order].description"],"core/audits/accessibility/html-has-lang.js | failureTitle":["audits[html-has-lang].title"],"core/audits/accessibility/html-has-lang.js | description":["audits[html-has-lang].description"],"core/audits/accessibility/html-lang-valid.js | title":["audits[html-lang-valid].title"],"core/audits/accessibility/html-lang-valid.js | description":["audits[html-lang-valid].description"],"core/audits/accessibility/html-xml-lang-mismatch.js | title":["audits[html-xml-lang-mismatch].title"],"core/audits/accessibility/html-xml-lang-mismatch.js | description":["audits[html-xml-lang-mismatch].description"],"core/audits/accessibility/identical-links-same-purpose.js | title":["audits[identical-links-same-purpose].title"],"core/audits/accessibility/identical-links-same-purpose.js | description":["audits[identical-links-same-purpose].description"],"core/audits/accessibility/image-alt.js | title":["audits[image-alt].title"],"core/audits/accessibility/image-alt.js | description":["audits[image-alt].description"],"core/audits/accessibility/image-redundant-alt.js | title":["audits[image-redundant-alt].title"],"core/audits/accessibility/image-redundant-alt.js | description":["audits[image-redundant-alt].description"],"core/audits/accessibility/input-button-name.js | title":["audits[input-button-name].title"],"core/audits/accessibility/input-button-name.js | description":["audits[input-button-name].description"],"core/audits/accessibility/input-image-alt.js | title":["audits[input-image-alt].title"],"core/audits/accessibility/input-image-alt.js | description":["audits[input-image-alt].description"],"core/audits/accessibility/label-content-name-mismatch.js | title":["audits[label-content-name-mismatch].title"],"core/audits/accessibility/label-content-name-mismatch.js | description":["audits[label-content-name-mismatch].description"],"core/audits/accessibility/label.js | title":["audits.label.title"],"core/audits/accessibility/label.js | description":["audits.label.description"],"core/audits/accessibility/landmark-one-main.js | title":["audits[landmark-one-main].title"],"core/audits/accessibility/landmark-one-main.js | description":["audits[landmark-one-main].description"],"core/audits/accessibility/link-name.js | title":["audits[link-name].title"],"core/audits/accessibility/link-name.js | description":["audits[link-name].description"],"core/audits/accessibility/link-in-text-block.js | title":["audits[link-in-text-block].title"],"core/audits/accessibility/link-in-text-block.js | description":["audits[link-in-text-block].description"],"core/audits/accessibility/list.js | title":["audits.list.title"],"core/audits/accessibility/list.js | description":["audits.list.description"],"core/audits/accessibility/listitem.js | title":["audits.listitem.title"],"core/audits/accessibility/listitem.js | description":["audits.listitem.description"],"core/audits/accessibility/meta-refresh.js | title":["audits[meta-refresh].title"],"core/audits/accessibility/meta-refresh.js | description":["audits[meta-refresh].description"],"core/audits/accessibility/meta-viewport.js | title":["audits[meta-viewport].title"],"core/audits/accessibility/meta-viewport.js | description":["audits[meta-viewport].description"],"core/audits/accessibility/object-alt.js | title":["audits[object-alt].title"],"core/audits/accessibility/object-alt.js | description":["audits[object-alt].description"],"core/audits/accessibility/select-name.js | title":["audits[select-name].title"],"core/audits/accessibility/select-name.js | description":["audits[select-name].description"],"core/audits/accessibility/skip-link.js | title":["audits[skip-link].title"],"core/audits/accessibility/skip-link.js | description":["audits[skip-link].description"],"core/audits/accessibility/tabindex.js | title":["audits.tabindex.title"],"core/audits/accessibility/tabindex.js | description":["audits.tabindex.description"],"core/audits/accessibility/table-duplicate-name.js | title":["audits[table-duplicate-name].title"],"core/audits/accessibility/table-duplicate-name.js | description":["audits[table-duplicate-name].description"],"core/audits/accessibility/table-fake-caption.js | title":["audits[table-fake-caption].title"],"core/audits/accessibility/table-fake-caption.js | description":["audits[table-fake-caption].description"],"core/audits/accessibility/target-size.js | title":["audits[target-size].title"],"core/audits/accessibility/target-size.js | description":["audits[target-size].description"],"core/audits/accessibility/td-has-header.js | title":["audits[td-has-header].title"],"core/audits/accessibility/td-has-header.js | description":["audits[td-has-header].description"],"core/audits/accessibility/td-headers-attr.js | title":["audits[td-headers-attr].title"],"core/audits/accessibility/td-headers-attr.js | description":["audits[td-headers-attr].description"],"core/audits/accessibility/th-has-data-cells.js | title":["audits[th-has-data-cells].title"],"core/audits/accessibility/th-has-data-cells.js | description":["audits[th-has-data-cells].description"],"core/audits/accessibility/valid-lang.js | title":["audits[valid-lang].title"],"core/audits/accessibility/valid-lang.js | description":["audits[valid-lang].description"],"core/audits/accessibility/video-caption.js | title":["audits[video-caption].title"],"core/audits/accessibility/video-caption.js | description":["audits[video-caption].description"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | title":["audits[uses-long-cache-ttl].title"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | description":["audits[uses-long-cache-ttl].description"],"core/audits/byte-efficiency/uses-long-cache-ttl.js | displayValue":[{"values":{"itemCount":0},"path":"audits[uses-long-cache-ttl].displayValue"}],"core/audits/byte-efficiency/total-byte-weight.js | title":["audits[total-byte-weight].title"],"core/audits/byte-efficiency/total-byte-weight.js | description":["audits[total-byte-weight].description"],"core/audits/byte-efficiency/total-byte-weight.js | displayValue":[{"values":{"totalBytes":1297},"path":"audits[total-byte-weight].displayValue"}],"core/audits/byte-efficiency/offscreen-images.js | title":["audits[offscreen-images].title"],"core/audits/byte-efficiency/offscreen-images.js | description":["audits[offscreen-images].description"],"core/audits/byte-efficiency/render-blocking-resources.js | title":["audits[render-blocking-resources].title"],"core/audits/byte-efficiency/render-blocking-resources.js | description":["audits[render-blocking-resources].description"],"core/audits/byte-efficiency/unminified-css.js | title":["audits[unminified-css].title"],"core/audits/byte-efficiency/unminified-css.js | description":["audits[unminified-css].description"],"core/audits/byte-efficiency/unminified-javascript.js | title":["audits[unminified-javascript].title"],"core/audits/byte-efficiency/unminified-javascript.js | description":["audits[unminified-javascript].description"],"core/audits/byte-efficiency/unused-css-rules.js | title":["audits[unused-css-rules].title"],"core/audits/byte-efficiency/unused-css-rules.js | description":["audits[unused-css-rules].description"],"core/audits/byte-efficiency/unused-javascript.js | title":["audits[unused-javascript].title"],"core/audits/byte-efficiency/unused-javascript.js | description":["audits[unused-javascript].description"],"core/audits/byte-efficiency/modern-image-formats.js | title":["audits[modern-image-formats].title"],"core/audits/byte-efficiency/modern-image-formats.js | description":["audits[modern-image-formats].description"],"core/audits/byte-efficiency/uses-optimized-images.js | title":["audits[uses-optimized-images].title"],"core/audits/byte-efficiency/uses-optimized-images.js | description":["audits[uses-optimized-images].description"],"core/audits/byte-efficiency/uses-text-compression.js | title":["audits[uses-text-compression].title"],"core/audits/byte-efficiency/uses-text-compression.js | description":["audits[uses-text-compression].description"],"core/audits/byte-efficiency/uses-responsive-images.js | title":["audits[uses-responsive-images].title"],"core/audits/byte-efficiency/uses-responsive-images.js | description":["audits[uses-responsive-images].description"],"core/audits/byte-efficiency/efficient-animated-content.js | title":["audits[efficient-animated-content].title"],"core/audits/byte-efficiency/efficient-animated-content.js | description":["audits[efficient-animated-content].description"],"core/audits/byte-efficiency/duplicated-javascript.js | title":["audits[duplicated-javascript].title"],"core/audits/byte-efficiency/duplicated-javascript.js | description":["audits[duplicated-javascript].description"],"core/audits/byte-efficiency/legacy-javascript.js | title":["audits[legacy-javascript].title"],"core/audits/byte-efficiency/legacy-javascript.js | description":["audits[legacy-javascript].description"],"core/audits/dobetterweb/doctype.js | failureTitle":["audits.doctype.title"],"core/audits/dobetterweb/doctype.js | description":["audits.doctype.description"],"core/audits/dobetterweb/doctype.js | explanationNoDoctype":["audits.doctype.explanation"],"core/audits/dobetterweb/charset.js | failureTitle":["audits.charset.title"],"core/audits/dobetterweb/charset.js | description":["audits.charset.description"],"core/audits/dobetterweb/dom-size.js | title":["audits[dom-size].title"],"core/audits/dobetterweb/dom-size.js | description":["audits[dom-size].description"],"core/audits/dobetterweb/dom-size.js | displayValue":[{"values":{"itemCount":0},"path":"audits[dom-size].displayValue"}],"core/audits/dobetterweb/dom-size.js | columnStatistic":["audits[dom-size].details.headings[0].label"],"core/audits/dobetterweb/dom-size.js | columnValue":["audits[dom-size].details.headings[2].label"],"core/audits/dobetterweb/dom-size.js | statisticDOMElements":["audits[dom-size].details.items[0].statistic"],"core/audits/dobetterweb/dom-size.js | statisticDOMDepth":["audits[dom-size].details.items[1].statistic"],"core/audits/dobetterweb/dom-size.js | statisticDOMWidth":["audits[dom-size].details.items[2].statistic"],"core/audits/dobetterweb/geolocation-on-start.js | title":["audits[geolocation-on-start].title"],"core/audits/dobetterweb/geolocation-on-start.js | description":["audits[geolocation-on-start].description"],"core/audits/dobetterweb/inspector-issues.js | title":["audits[inspector-issues].title"],"core/audits/dobetterweb/inspector-issues.js | description":["audits[inspector-issues].description"],"core/audits/dobetterweb/no-document-write.js | title":["audits[no-document-write].title"],"core/audits/dobetterweb/no-document-write.js | description":["audits[no-document-write].description"],"core/audits/dobetterweb/js-libraries.js | title":["audits[js-libraries].title"],"core/audits/dobetterweb/js-libraries.js | description":["audits[js-libraries].description"],"core/audits/dobetterweb/notification-on-start.js | title":["audits[notification-on-start].title"],"core/audits/dobetterweb/notification-on-start.js | description":["audits[notification-on-start].description"],"core/audits/dobetterweb/paste-preventing-inputs.js | title":["audits[paste-preventing-inputs].title"],"core/audits/dobetterweb/paste-preventing-inputs.js | description":["audits[paste-preventing-inputs].description"],"core/audits/dobetterweb/uses-http2.js | title":["audits[uses-http2].title"],"core/audits/dobetterweb/uses-http2.js | description":["audits[uses-http2].description"],"core/audits/dobetterweb/uses-passive-event-listeners.js | title":["audits[uses-passive-event-listeners].title"],"core/audits/dobetterweb/uses-passive-event-listeners.js | description":["audits[uses-passive-event-listeners].description"],"core/audits/seo/meta-description.js | failureTitle":["audits[meta-description].title"],"core/audits/seo/meta-description.js | description":["audits[meta-description].description"],"core/audits/seo/http-status-code.js | title":["audits[http-status-code].title"],"core/audits/seo/http-status-code.js | description":["audits[http-status-code].description"],"core/audits/seo/font-size.js | failureTitle":["audits[font-size].title"],"core/audits/seo/font-size.js | description":["audits[font-size].description"],"core/audits/seo/font-size.js | explanationViewport":["audits[font-size].explanation"],"core/audits/seo/link-text.js | title":["audits[link-text].title"],"core/audits/seo/link-text.js | description":["audits[link-text].description"],"core/audits/seo/crawlable-anchors.js | title":["audits[crawlable-anchors].title"],"core/audits/seo/crawlable-anchors.js | description":["audits[crawlable-anchors].description"],"core/audits/seo/is-crawlable.js | title":["audits[is-crawlable].title"],"core/audits/seo/is-crawlable.js | description":["audits[is-crawlable].description"],"core/audits/seo/robots-txt.js | title":["audits[robots-txt].title"],"core/audits/seo/robots-txt.js | description":["audits[robots-txt].description"],"core/audits/seo/hreflang.js | title":["audits.hreflang.title"],"core/audits/seo/hreflang.js | description":["audits.hreflang.description"],"core/audits/seo/canonical.js | title":["audits.canonical.title"],"core/audits/seo/canonical.js | description":["audits.canonical.description"],"core/audits/seo/manual/structured-data.js | title":["audits[structured-data].title"],"core/audits/seo/manual/structured-data.js | description":["audits[structured-data].description"],"core/audits/bf-cache.js | title":["audits[bf-cache].title"],"core/audits/bf-cache.js | description":["audits[bf-cache].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | title":["audits[cls-culprits-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/CLSCulprits.js | description":["audits[cls-culprits-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | title":["audits[document-latency-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | description":["audits[document-latency-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingRedirects":["audits[document-latency-insight].details.items.noRedirects.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingServerResponseTime":["audits[document-latency-insight].details.items.serverResponseIsFast.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DocumentLatency.js | passingTextCompression":["audits[document-latency-insight].details.items.usesCompression.label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | title":["audits[dom-size-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | description":["audits[dom-size-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | statistic":["audits[dom-size-insight].details.headings[0].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | value":["audits[dom-size-insight].details.headings[2].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | totalElements":["audits[dom-size-insight].details.items[0].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxChildren":["audits[dom-size-insight].details.items[1].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DOMSize.js | maxDOMDepth":["audits[dom-size-insight].details.items[2].statistic"],"node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | title":["audits[duplicated-javascript-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/DuplicatedJavaScript.js | description":["audits[duplicated-javascript-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | title":["audits[font-display-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/FontDisplay.js | description":["audits[font-display-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | title":["audits[forced-reflow-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ForcedReflow.js | description":["audits[forced-reflow-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | title":["audits[image-delivery-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ImageDelivery.js | description":["audits[image-delivery-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | title":["audits[interaction-to-next-paint-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/InteractionToNextPaint.js | description":["audits[interaction-to-next-paint-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | title":["audits[lcp-discovery-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPDiscovery.js | description":["audits[lcp-discovery-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | title":["audits[lcp-phases-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | description":["audits[lcp-phases-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | phase":["audits[lcp-phases-insight].details.items[1].headings[0].label"],"core/lib/i18n/i18n.js | columnDuration":["audits[lcp-phases-insight].details.items[1].headings[1].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | timeToFirstByte":["audits[lcp-phases-insight].details.items[1].items[0].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/LCPPhases.js | elementRenderDelay":["audits[lcp-phases-insight].details.items[1].items[1].label"],"node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | title":["audits[network-dependency-tree-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/NetworkDependencyTree.js | description":["audits[network-dependency-tree-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | title":["audits[render-blocking-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/RenderBlocking.js | description":["audits[render-blocking-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | title":["audits[slow-css-selector-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/SlowCSSSelector.js | description":["audits[slow-css-selector-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | title":["audits[third-parties-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/ThirdParties.js | description":["audits[third-parties-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/UseCache.js | title":["audits[use-cache-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/UseCache.js | description":["audits[use-cache-insight].description"],"node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | title":["audits[viewport-insight].title"],"node_modules/@paulirish/trace_engine/models/trace/insights/Viewport.js | description":["audits[viewport-insight].description"],"core/config/default-config.js | performanceCategoryTitle":["categories.performance.title"],"core/config/default-config.js | a11yCategoryTitle":["categories.accessibility.title"],"core/config/default-config.js | a11yCategoryDescription":["categories.accessibility.description"],"core/config/default-config.js | a11yCategoryManualDescription":["categories.accessibility.manualDescription"],"core/config/default-config.js | bestPracticesCategoryTitle":["categories[best-practices].title"],"core/config/default-config.js | seoCategoryTitle":["categories.seo.title"],"core/config/default-config.js | seoCategoryDescription":["categories.seo.description"],"core/config/default-config.js | seoCategoryManualDescription":["categories.seo.manualDescription"],"core/config/default-config.js | metricGroupTitle":["categoryGroups.metrics.title"],"core/config/default-config.js | insightGroupTitle":["categoryGroups.insights.title"],"core/config/default-config.js | diagnosticsGroupTitle":["categoryGroups.diagnostics.title"],"core/config/default-config.js | diagnosticsGroupDescription":["categoryGroups.diagnostics.description"],"core/config/default-config.js | a11yBestPracticesGroupTitle":["categoryGroups[a11y-best-practices].title"],"core/config/default-config.js | a11yBestPracticesGroupDescription":["categoryGroups[a11y-best-practices].description"],"core/config/default-config.js | a11yColorContrastGroupTitle":["categoryGroups[a11y-color-contrast].title"],"core/config/default-config.js | a11yColorContrastGroupDescription":["categoryGroups[a11y-color-contrast].description"],"core/config/default-config.js | a11yNamesLabelsGroupTitle":["categoryGroups[a11y-names-labels].title"],"core/config/default-config.js | a11yNamesLabelsGroupDescription":["categoryGroups[a11y-names-labels].description"],"core/config/default-config.js | a11yNavigationGroupTitle":["categoryGroups[a11y-navigation].title"],"core/config/default-config.js | a11yNavigationGroupDescription":["categoryGroups[a11y-navigation].description"],"core/config/default-config.js | a11yAriaGroupTitle":["categoryGroups[a11y-aria].title"],"core/config/default-config.js | a11yAriaGroupDescription":["categoryGroups[a11y-aria].description"],"core/config/default-config.js | a11yLanguageGroupTitle":["categoryGroups[a11y-language].title"],"core/config/default-config.js | a11yLanguageGroupDescription":["categoryGroups[a11y-language].description"],"core/config/default-config.js | a11yAudioVideoGroupTitle":["categoryGroups[a11y-audio-video].title"],"core/config/default-config.js | a11yAudioVideoGroupDescription":["categoryGroups[a11y-audio-video].description"],"core/config/default-config.js | a11yTablesListsVideoGroupTitle":["categoryGroups[a11y-tables-lists].title"],"core/config/default-config.js | a11yTablesListsVideoGroupDescription":["categoryGroups[a11y-tables-lists].description"],"core/config/default-config.js | seoMobileGroupTitle":["categoryGroups[seo-mobile].title"],"core/config/default-config.js | seoMobileGroupDescription":["categoryGroups[seo-mobile].description"],"core/config/default-config.js | seoContentGroupTitle":["categoryGroups[seo-content].title"],"core/config/default-config.js | seoContentGroupDescription":["categoryGroups[seo-content].description"],"core/config/default-config.js | seoCrawlingGroupTitle":["categoryGroups[seo-crawl].title"],"core/config/default-config.js | seoCrawlingGroupDescription":["categoryGroups[seo-crawl].description"],"core/config/default-config.js | bestPracticesTrustSafetyGroupTitle":["categoryGroups[best-practices-trust-safety].title"],"core/config/default-config.js | bestPracticesUXGroupTitle":["categoryGroups[best-practices-ux].title"],"core/config/default-config.js | bestPracticesBrowserCompatGroupTitle":["categoryGroups[best-practices-browser-compat].title"],"core/config/default-config.js | bestPracticesGeneralGroupTitle":["categoryGroups[best-practices-general].title"]}}};</script>
  <script>"use strict";(()=>{var Ne=.8999999999999999,Ie=.5,He=.49999999999999994;function Ue(o){let e=Math.sign(o);o=Math.abs(o);let t=.254829592,n=-.284496736,r=1.421413741,i=-1.453152027,a=1.061405429,s=1/(1+.3275911*o),c=s*(t+s*(n+s*(r+s*(i+s*a))));return e*(1-c*Math.exp(-o*o))}function ve({median:o,p10:e},t){if(o<=0)throw new Error("median must be greater than zero");if(e<=0)throw new Error("p10 must be greater than zero");if(e>=o)throw new Error("p10 must be less than the median");if(t<=0)return 1;let n=.9061938024368232,r=Math.max(Number.MIN_VALUE,t/o),i=Math.log(r),a=Math.max(Number.MIN_VALUE,e/o),l=-Math.log(a),s=i*n/l,c=(1-Ue(s))/2,d;return t<=e?d=Math.max(.9,Math.min(1,c)):t<=o?d=Math.max(Ie,Math.min(Ne,c)):d=Math.max(0,Math.min(He,c)),d}var O="\u2026",$e="\xA0",_e=.9,Ve={PASS:{label:"pass",minScore:_e},AVERAGE:{label:"average",minScore:.5},FAIL:{label:"fail"},ERROR:{label:"error"}},Ge=["com","co","gov","edu","ac","org","go","gob","or","net","in","ne","nic","gouv","web","spb","blog","jus","kiev","mil","wi","qc","ca","bel","on"],E=class o{static get RATINGS(){return Ve}static get PASS_THRESHOLD(){return _e}static get MS_DISPLAY_VALUE(){return`%10d${$e}ms`}static getFinalDisplayedUrl(e){if(e.finalDisplayedUrl)return e.finalDisplayedUrl;if(e.finalUrl)return e.finalUrl;throw new Error("Could not determine final displayed URL")}static getMainDocumentUrl(e){return e.mainDocumentUrl||e.finalUrl}static getFullPageScreenshot(e){return e.fullPageScreenshot?e.fullPageScreenshot:e.audits["full-page-screenshot"]?.details}static getEntityFromUrl(e,t){return t&&t.find(r=>r.origins.find(i=>e.startsWith(i)))||o.getPseudoRootDomain(e)}static splitMarkdownCodeSpans(e){let t=[],n=e.split(/`(.*?)`/g);for(let r=0;r<n.length;r++){let i=n[r];if(!i)continue;let a=r%2!==0;t.push({isCode:a,text:i})}return t}static splitMarkdownLink(e){let t=[],n=e.split(/\[([^\]]+?)\]\((https?:\/\/.*?)\)/g);for(;n.length;){let[r,i,a]=n.splice(0,3);r&&t.push({isLink:!1,text:r}),i&&a&&t.push({isLink:!0,text:i,linkHref:a})}return t}static truncate(e,t,n="\u2026"){if(e.length<=t)return e;let i=new Intl.Segmenter(void 0,{granularity:"grapheme"}).segment(e)[Symbol.iterator](),a=0;for(let l=0;l<=t-n.length;l++){let s=i.next();if(s.done)return e;a=s.value.index}for(let l=0;l<n.length;l++)if(i.next().done)return e;return e.slice(0,a)+n}static getURLDisplayName(e,t){t=t||{numPathParts:void 0,preserveQuery:void 0,preserveHost:void 0};let n=t.numPathParts!==void 0?t.numPathParts:2,r=t.preserveQuery!==void 0?t.preserveQuery:!0,i=t.preserveHost||!1,a;if(e.protocol==="about:"||e.protocol==="data:")a=e.href;else{a=e.pathname;let s=a.split("/").filter(c=>c.length);n&&s.length>n&&(a=O+s.slice(-1*n).join("/")),i&&(a=`${e.host}/${a.replace(/^\//,"")}`),r&&(a=`${a}${e.search}`)}let l=64;if(e.protocol!=="data:"&&(a=a.slice(0,200),a=a.replace(/([a-f0-9]{7})[a-f0-9]{13}[a-f0-9]*/g,`$1${O}`),a=a.replace(/([a-zA-Z0-9-_]{9})(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9-_]{10,}/g,`$1${O}`),a=a.replace(/(\d{3})\d{6,}/g,`$1${O}`),a=a.replace(/\u2026+/g,O),a.length>l&&a.includes("?")&&(a=a.replace(/\?([^=]*)(=)?.*/,`?$1$2${O}`),a.length>l&&(a=a.replace(/\?.*/,`?${O}`)))),a.length>l){let s=a.lastIndexOf(".");s>=0?a=a.slice(0,l-1-(a.length-s))+`${O}${a.slice(s)}`:a=a.slice(0,l-1)+O}return a}static getChromeExtensionOrigin(e){let t=new URL(e);return t.protocol+"//"+t.host}static parseURL(e){let t=new URL(e);return{file:o.getURLDisplayName(t),hostname:t.hostname,origin:t.protocol==="chrome-extension:"?o.getChromeExtensionOrigin(e):t.origin}}static createOrReturnURL(e){return e instanceof URL?e:new URL(e)}static getPseudoTld(e){let t=e.split(".").slice(-2);return Ge.includes(t[0])?`.${t.join(".")}`:`.${t[t.length-1]}`}static getPseudoRootDomain(e){let t=o.createOrReturnURL(e).hostname,r=o.getPseudoTld(t).split(".");return t.split(".").slice(-r.length).join(".")}static filterRelevantLines(e,t,n){if(t.length===0)return e.slice(0,n*2+1);let r=3,i=new Set;return t=t.sort((a,l)=>(a.lineNumber||0)-(l.lineNumber||0)),t.forEach(({lineNumber:a})=>{let l=a-n,s=a+n;for(;l<1;)l++,s++;i.has(l-r-1)&&(l-=r);for(let c=l;c<=s;c++){let d=c;i.add(d)}}),e.filter(a=>i.has(a.lineNumber))}static computeLogNormalScore(e,t){let n=ve(e,t);return n>.9&&(n+=.05*(n-.9)),Math.floor(n*100)/100}};function Be(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-3p-filter {
      color: var(--color-gray-600);
      float: right;
      padding: 6px var(--stackpack-padding-horizontal);
    }
    .lh-3p-filter-label, .lh-3p-filter-input {
      vertical-align: middle;
      user-select: none;
    }
    .lh-3p-filter-input:disabled + .lh-3p-ui-string {
      text-decoration: line-through;
    }
  `),e.append(t);let n=o.createElement("div","lh-3p-filter"),r=o.createElement("label","lh-3p-filter-label"),i=o.createElement("input","lh-3p-filter-input");i.setAttribute("type","checkbox"),i.setAttribute("checked","");let a=o.createElement("span","lh-3p-ui-string");a.append("Show 3rd party resources");let l=o.createElement("span","lh-3p-filter-count");return r.append(" ",i," ",a," (",l,") "),n.append(" ",r," "),e.append(n),e}function qe(o){let e=o.createFragment(),t=o.createElement("div","lh-audit"),n=o.createElement("details","lh-expandable-details"),r=o.createElement("summary"),i=o.createElement("div","lh-audit__header lh-expandable-details__summary"),a=o.createElement("span","lh-audit__score-icon"),l=o.createElement("span","lh-audit__title-and-text"),s=o.createElement("span","lh-audit__title"),c=o.createElement("span","lh-audit__display-text");l.append(" ",s," ",c," ");let d=o.createElement("div","lh-chevron-container");i.append(" ",a," ",l," ",d," "),r.append(" ",i," ");let p=o.createElement("div","lh-audit__description"),h=o.createElement("div","lh-audit__stackpacks");return n.append(" ",r," ",p," ",h," "),t.append(" ",n," "),e.append(t),e}function je(o){let e=o.createFragment(),t=o.createElement("div","lh-category-header"),n=o.createElement("div","lh-score__gauge");n.setAttribute("role","heading"),n.setAttribute("aria-level","2");let r=o.createElement("div","lh-category-header__description");return t.append(" ",n," ",r," "),e.append(t),e}function We(o){let e=o.createFragment(),t=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-chevron");t.setAttribute("viewBox","0 0 100 100");let n=o.createElementNS("http://www.w3.org/2000/svg","g","lh-chevron__lines"),r=o.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-left");r.setAttribute("d","M10 50h40");let i=o.createElementNS("http://www.w3.org/2000/svg","path","lh-chevron__line lh-chevron__line-right");return i.setAttribute("d","M90 50H50"),n.append(" ",r," ",i," "),t.append(" ",n," "),e.append(t),e}function Ke(o){let e=o.createFragment(),t=o.createElement("div","lh-audit-group"),n=o.createElement("details","lh-clump"),r=o.createElement("summary"),i=o.createElement("div","lh-audit-group__summary"),a=o.createElement("div","lh-audit-group__header"),l=o.createElement("span","lh-audit-group__title"),s=o.createElement("span","lh-audit-group__itemcount");a.append(" ",l," ",s," "," "," ");let c=o.createElement("div","lh-clump-toggle"),d=o.createElement("span","lh-clump-toggletext--show"),p=o.createElement("span","lh-clump-toggletext--hide");return c.append(" ",d," ",p," "),i.append(" ",a," ",c," "),r.append(" ",i," "),n.append(" ",r," "),t.append(" "," ",n," "),e.append(t),e}function Je(o){let e=o.createFragment(),t=o.createElement("div","lh-crc-container"),n=o.createElement("style");n.append(`
      .lh-crc .lh-tree-marker {
        width: 12px;
        height: 26px;
        display: block;
        float: left;
        background-position: top left;
      }
      .lh-crc .lh-horiz-down {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><g fill="%23D8D8D8" fill-rule="evenodd"><path d="M16 12v2H-2v-2z"/><path d="M9 12v14H7V12z"/>\u003c/g>\u003c/svg>');
      }
      .lh-crc .lh-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M16 12v2H0v-2z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-up-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v14H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-vert-right {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v27H7zm2 12h7v2H9z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-vert {
        background: url('data:image/svg+xml;utf8,<svg width="16" height="26" viewBox="0 0 16 26" xmlns="http://www.w3.org/2000/svg"><path d="M7 0h2v26H7z" fill="%23D8D8D8" fill-rule="evenodd"/>\u003c/svg>');
      }
      .lh-crc .lh-crc-tree {
        font-size: 14px;
        width: 100%;
        overflow-x: auto;
      }
      .lh-crc .lh-crc-node {
        height: 26px;
        line-height: 26px;
        white-space: nowrap;
      }
      .lh-crc .lh-crc-node__tree-value {
        margin-left: 10px;
      }
      .lh-crc .lh-crc-node__tree-value div {
        display: inline;
      }
      .lh-crc .lh-crc-node__chain-duration {
        font-weight: 700;
      }
      .lh-crc .lh-crc-initial-nav {
        color: #595959;
        font-style: italic;
      }
      .lh-crc__summary-value {
        margin-bottom: 10px;
      }
    `);let r=o.createElement("div"),i=o.createElement("div","lh-crc__summary-value"),a=o.createElement("span","lh-crc__longest_duration_label"),l=o.createElement("b","lh-crc__longest_duration");i.append(" ",a," ",l," "),r.append(" ",i," ");let s=o.createElement("div","lh-crc"),c=o.createElement("div","lh-crc-initial-nav");return s.append(" ",c," "," "),t.append(" ",n," ",r," ",s," "),e.append(t),e}function Ze(o){let e=o.createFragment(),t=o.createElement("div","lh-crc-node"),n=o.createElement("span","lh-crc-node__tree-marker"),r=o.createElement("span","lh-crc-node__tree-value");return t.append(" ",n," ",r," "),e.append(t),e}function Qe(o){let e=o.createFragment(),t=o.createElement("div","lh-element-screenshot"),n=o.createElement("div","lh-element-screenshot__content"),r=o.createElement("div","lh-element-screenshot__image"),i=o.createElement("div","lh-element-screenshot__mask"),a=o.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("height","0"),a.setAttribute("width","0");let l=o.createElementNS("http://www.w3.org/2000/svg","defs"),s=o.createElementNS("http://www.w3.org/2000/svg","clipPath");s.setAttribute("clipPathUnits","objectBoundingBox"),l.append(" ",s," "," "),a.append(" ",l," "),i.append(" ",a," ");let c=o.createElement("div","lh-element-screenshot__element-marker");return r.append(" ",i," ",c," "),n.append(" ",r," "),t.append(" ",n," "),e.append(t),e}function Ye(o){let e=o.createFragment(),t=o.createElement("div","lh-exp-gauge-component"),n=o.createElement("div","lh-exp-gauge__wrapper");n.setAttribute("target","_blank");let r=o.createElement("div","lh-exp-gauge__svg-wrapper"),i=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-exp-gauge"),a=o.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__inner"),l=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__bg"),s=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__base lh-exp-gauge--faded"),c=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-exp-gauge__arc"),d=o.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__percentage");a.append(" ",l," ",s," ",c," ",d," ");let p=o.createElementNS("http://www.w3.org/2000/svg","g","lh-exp-gauge__outer"),h=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-cover");p.append(" ",h," ");let u=o.createElementNS("http://www.w3.org/2000/svg","text","lh-exp-gauge__label");return u.setAttribute("text-anchor","middle"),u.setAttribute("x","0"),u.setAttribute("y","60"),i.append(" ",a," ",p," ",u," "),r.append(" ",i," "),n.append(" ",r," "),t.append(" ",n," "),e.append(t),e}function Xe(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-footer {
      padding: var(--footer-padding-vertical) calc(var(--default-padding) * 2);
      max-width: var(--report-content-max-width);
      margin: 0 auto;
    }
    .lh-footer .lh-generated {
      text-align: center;
    }
  `),e.append(t);let n=o.createElement("footer","lh-footer"),r=o.createElement("ul","lh-meta__items");r.append(" ");let i=o.createElement("div","lh-generated"),a=o.createElement("b");a.append("Lighthouse");let l=o.createElement("span","lh-footer__version"),s=o.createElement("a","lh-footer__version_issue");return s.setAttribute("href","https://github.com/GoogleChrome/Lighthouse/issues"),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener"),s.append("File an issue"),i.append(" "," Generated by ",a," ",l," | ",s," "),n.append(" ",r," ",i," "),e.append(n),e}function et(o){let e=o.createFragment(),t=o.createElement("a","lh-fraction__wrapper"),n=o.createElement("div","lh-fraction__content-wrapper"),r=o.createElement("div","lh-fraction__content"),i=o.createElement("div","lh-fraction__background");r.append(" ",i," "),n.append(" ",r," ");let a=o.createElement("div","lh-fraction__label");return t.append(" ",n," ",a," "),e.append(t),e}function tt(o){let e=o.createFragment(),t=o.createElement("a","lh-gauge__wrapper"),n=o.createElement("div","lh-gauge__svg-wrapper"),r=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-gauge");r.setAttribute("viewBox","0 0 120 120");let i=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-base");i.setAttribute("r","56"),i.setAttribute("cx","60"),i.setAttribute("cy","60"),i.setAttribute("stroke-width","8");let a=o.createElementNS("http://www.w3.org/2000/svg","circle","lh-gauge-arc");a.setAttribute("r","56"),a.setAttribute("cx","60"),a.setAttribute("cy","60"),a.setAttribute("stroke-width","8"),r.append(" ",i," ",a," "),n.append(" ",r," ");let l=o.createElement("div","lh-gauge__percentage"),s=o.createElement("div","lh-gauge__label");return t.append(" "," ",n," ",l," "," ",s," "),e.append(t),e}function nt(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    /* CSS Fireworks. Originally by Eddie Lin
       https://codepen.io/paulirish/pen/yEVMbP
    */
    .lh-pyro {
      display: none;
      z-index: 1;
      pointer-events: none;
    }
    .lh-score100 .lh-pyro {
      display: block;
    }
    .lh-score100 .lh-lighthouse stop:first-child {
      stop-color: hsla(200, 12%, 95%, 0);
    }
    .lh-score100 .lh-lighthouse stop:last-child {
      stop-color: hsla(65, 81%, 76%, 1);
    }

    .lh-pyro > .lh-pyro-before, .lh-pyro > .lh-pyro-after {
      position: absolute;
      width: 5px;
      height: 5px;
      border-radius: 2.5px;
      box-shadow: 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff, 0 0 #fff;
      animation: 1s bang ease-out infinite backwards,  1s gravity ease-in infinite backwards,  5s position linear infinite backwards;
      animation-delay: 1s, 1s, 1s;
    }

    .lh-pyro > .lh-pyro-after {
      animation-delay: 2.25s, 2.25s, 2.25s;
      animation-duration: 1.25s, 1.25s, 6.25s;
    }

    @keyframes bang {
      to {
        opacity: 1;
        box-shadow: -70px -115.67px #47ebbc, -28px -99.67px #eb47a4, 58px -31.67px #7eeb47, 13px -141.67px #eb47c5, -19px 6.33px #7347eb, -2px -74.67px #ebd247, 24px -151.67px #eb47e0, 57px -138.67px #b4eb47, -51px -104.67px #479eeb, 62px 8.33px #ebcf47, -93px 0.33px #d547eb, -16px -118.67px #47bfeb, 53px -84.67px #47eb83, 66px -57.67px #eb47bf, -93px -65.67px #91eb47, 30px -13.67px #86eb47, -2px -59.67px #83eb47, -44px 1.33px #eb47eb, 61px -58.67px #47eb73, 5px -22.67px #47e8eb, -66px -28.67px #ebe247, 42px -123.67px #eb5547, -75px 26.33px #7beb47, 15px -52.67px #a147eb, 36px -51.67px #eb8347, -38px -12.67px #eb5547, -46px -59.67px #47eb81, 78px -114.67px #eb47ba, 15px -156.67px #eb47bf, -36px 1.33px #eb4783, -72px -86.67px #eba147, 31px -46.67px #ebe247, -68px 29.33px #47e2eb, -55px 19.33px #ebe047, -56px 27.33px #4776eb, -13px -91.67px #eb5547, -47px -138.67px #47ebc7, -18px -96.67px #eb47ac, 11px -88.67px #4783eb, -67px -28.67px #47baeb, 53px 10.33px #ba47eb, 11px 19.33px #5247eb, -5px -11.67px #eb4791, -68px -4.67px #47eba7, 95px -37.67px #eb478b, -67px -162.67px #eb5d47, -54px -120.67px #eb6847, 49px -12.67px #ebe047, 88px 8.33px #47ebda, 97px 33.33px #eb8147, 6px -71.67px #ebbc47;
      }
    }
    @keyframes gravity {
      from {
        opacity: 1;
      }
      to {
        transform: translateY(80px);
        opacity: 0;
      }
    }
    @keyframes position {
      0%, 19.9% {
        margin-top: 4%;
        margin-left: 47%;
      }
      20%, 39.9% {
        margin-top: 7%;
        margin-left: 30%;
      }
      40%, 59.9% {
        margin-top: 6%;
        margin-left: 70%;
      }
      60%, 79.9% {
        margin-top: 3%;
        margin-left: 20%;
      }
      80%, 99.9% {
        margin-top: 3%;
        margin-left: 80%;
      }
    }
  `),e.append(t);let n=o.createElement("div","lh-header-container"),r=o.createElement("div","lh-scores-wrapper-placeholder");return n.append(" ",r," "),e.append(n),e}function rt(o){let e=o.createFragment(),t=o.createElement("div","lh-metric"),n=o.createElement("div","lh-metric__innerwrap"),r=o.createElement("div","lh-metric__icon"),i=o.createElement("span","lh-metric__title"),a=o.createElement("div","lh-metric__value"),l=o.createElement("div","lh-metric__description");return n.append(" ",r," ",i," ",a," ",l," "),t.append(" ",n," "),e.append(t),e}function ot(o){let e=o.createFragment(),t=o.createElement("div","lh-scorescale"),n=o.createElement("span","lh-scorescale-range lh-scorescale-range--fail");n.append("0\u201349");let r=o.createElement("span","lh-scorescale-range lh-scorescale-range--average");r.append("50\u201389");let i=o.createElement("span","lh-scorescale-range lh-scorescale-range--pass");return i.append("90\u2013100"),t.append(" ",n," ",r," ",i," "),e.append(t),e}function it(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-scores-container {
      display: flex;
      flex-direction: column;
      padding: var(--default-padding) 0;
      position: relative;
      width: 100%;
    }

    .lh-sticky-header {
      --gauge-circle-size: var(--gauge-circle-size-sm);
      --plugin-badge-size: 16px;
      --plugin-icon-size: 75%;
      --gauge-wrapper-width: 60px;
      --gauge-percentage-font-size: 13px;
      position: fixed;
      left: 0;
      right: 0;
      top: var(--topbar-height);
      font-weight: 500;
      display: none;
      justify-content: center;
      background-color: var(--sticky-header-background-color);
      border-bottom: 1px solid var(--color-gray-200);
      padding-top: var(--score-container-padding);
      padding-bottom: 4px;
      z-index: 2;
      pointer-events: none;
    }

    .lh-devtools .lh-sticky-header {
      /* The report within DevTools is placed in a container with overflow, which changes the placement of this header unless we change \`position\` to \`sticky.\` */
      position: sticky;
    }

    .lh-sticky-header--visible {
      display: grid;
      grid-auto-flow: column;
      pointer-events: auto;
    }

    /* Disable the gauge arc animation for the sticky header, so toggling display: none
       does not play the animation. */
    .lh-sticky-header .lh-gauge-arc {
      animation: none;
    }

    .lh-sticky-header .lh-gauge__label,
    .lh-sticky-header .lh-fraction__label {
      display: none;
    }

    .lh-highlighter {
      width: var(--gauge-wrapper-width);
      height: 1px;
      background-color: var(--highlighter-background-color);
      /* Position at bottom of first gauge in sticky header. */
      position: absolute;
      grid-column: 1;
      bottom: -1px;
      left: 0px;
      right: 0px;
    }
  `),e.append(t);let n=o.createElement("div","lh-scores-wrapper"),r=o.createElement("div","lh-scores-container"),i=o.createElement("div","lh-pyro"),a=o.createElement("div","lh-pyro-before"),l=o.createElement("div","lh-pyro-after");return i.append(" ",a," ",l," "),r.append(" ",i," "),n.append(" ",r," "),e.append(n),e}function at(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet"),n=o.createElement("style");return n.append(`
          :root {
            --snippet-highlight-light: #fbf1f2;
            --snippet-highlight-dark: #ffd6d8;
          }

         .lh-snippet__header {
          position: relative;
          overflow: hidden;
          padding: 10px;
          border-bottom: none;
          color: var(--snippet-color);
          background-color: var(--snippet-background-color);
          border: 1px solid var(--report-border-color-secondary);
        }
        .lh-snippet__title {
          font-weight: bold;
          float: left;
        }
        .lh-snippet__node {
          float: left;
          margin-left: 4px;
        }
        .lh-snippet__toggle-expand {
          padding: 1px 7px;
          margin-top: -1px;
          margin-right: -7px;
          float: right;
          background: transparent;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #0c50c7;
        }

        .lh-snippet__snippet {
          overflow: auto;
          border: 1px solid var(--report-border-color-secondary);
        }
        /* Container needed so that all children grow to the width of the scroll container */
        .lh-snippet__snippet-inner {
          display: inline-block;
          min-width: 100%;
        }

        .lh-snippet:not(.lh-snippet--expanded) .lh-snippet__show-if-expanded {
          display: none;
        }
        .lh-snippet.lh-snippet--expanded .lh-snippet__show-if-collapsed {
          display: none;
        }

        .lh-snippet__line {
          background: white;
          white-space: pre;
          display: flex;
        }
        .lh-snippet__line:not(.lh-snippet__line--message):first-child {
          padding-top: 4px;
        }
        .lh-snippet__line:not(.lh-snippet__line--message):last-child {
          padding-bottom: 4px;
        }
        .lh-snippet__line--content-highlighted {
          background: var(--snippet-highlight-dark);
        }
        .lh-snippet__line--message {
          background: var(--snippet-highlight-light);
        }
        .lh-snippet__line--message .lh-snippet__line-number {
          padding-top: 10px;
          padding-bottom: 10px;
        }
        .lh-snippet__line--message code {
          padding: 10px;
          padding-left: 5px;
          color: var(--color-fail);
          font-family: var(--report-font-family);
        }
        .lh-snippet__line--message code {
          white-space: normal;
        }
        .lh-snippet__line-icon {
          padding-top: 10px;
          display: none;
        }
        .lh-snippet__line--message .lh-snippet__line-icon {
          display: block;
        }
        .lh-snippet__line-icon:before {
          content: "";
          display: inline-block;
          vertical-align: middle;
          margin-right: 4px;
          width: var(--score-icon-size);
          height: var(--score-icon-size);
          background-image: var(--fail-icon-url);
        }
        .lh-snippet__line-number {
          flex-shrink: 0;
          width: 40px;
          text-align: right;
          font-family: monospace;
          padding-right: 5px;
          margin-right: 5px;
          color: var(--color-gray-600);
          user-select: none;
        }
    `),t.append(" ",n," "),e.append(t),e}function lt(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__snippet"),n=o.createElement("div","lh-snippet__snippet-inner");return t.append(" ",n," "),e.append(t),e}function st(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__header"),n=o.createElement("div","lh-snippet__title"),r=o.createElement("div","lh-snippet__node"),i=o.createElement("button","lh-snippet__toggle-expand"),a=o.createElement("span","lh-snippet__btn-label-collapse lh-snippet__show-if-expanded"),l=o.createElement("span","lh-snippet__btn-label-expand lh-snippet__show-if-collapsed");return i.append(" ",a," ",l," "),t.append(" ",n," ",r," ",i," "),e.append(t),e}function ct(o){let e=o.createFragment(),t=o.createElement("div","lh-snippet__line"),n=o.createElement("div","lh-snippet__line-number"),r=o.createElement("div","lh-snippet__line-icon"),i=o.createElement("code");return t.append(" ",n," ",r," ",i," "),e.append(t),e}function dt(o){let e=o.createFragment(),t=o.createElement("style");return t.append(`/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/*
  Naming convention:

  If a variable is used for a specific component: --{component}-{property name}-{modifier}

  Both {component} and {property name} should be kebab-case. If the target is the entire page,
  use 'report' for the component. The property name should not be abbreviated. Use the
  property name the variable is intended for - if it's used for multiple, a common descriptor
  is fine (ex: 'size' for a variable applied to 'width' and 'height'). If a variable is shared
  across multiple components, either create more variables or just drop the "{component}-"
  part of the name. Append any modifiers at the end (ex: 'big', 'dark').

  For colors: --color-{hue}-{intensity}

  {intensity} is the Material Design tag - 700, A700, etc.
*/
.lh-vars {
  /* Palette using Material Design Colors
   * https://www.materialui.co/colors */
  --color-amber-50: #FFF8E1;
  --color-blue-200: #90CAF9;
  --color-blue-900: #0D47A1;
  --color-blue-A700: #2962FF;
  --color-blue-primary: #06f;
  --color-cyan-500: #00BCD4;
  --color-gray-100: #F5F5F5;
  --color-gray-300: #CFCFCF;
  --color-gray-200: #E0E0E0;
  --color-gray-400: #BDBDBD;
  --color-gray-50: #FAFAFA;
  --color-gray-500: #9E9E9E;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;
  --color-gray: #000000;
  --color-green-700: #080;
  --color-green: #0c6;
  --color-lime-400: #D3E156;
  --color-orange-50: #FFF3E0;
  --color-orange-700: #C33300;
  --color-orange: #fa3;
  --color-red-700: #c00;
  --color-red: #f33;
  --color-teal-600: #00897B;
  --color-white: #FFFFFF;

  /* Context-specific colors */
  --color-average-secondary: var(--color-orange-700);
  --color-average: var(--color-orange);
  --color-fail-secondary: var(--color-red-700);
  --color-fail: var(--color-red);
  --color-hover: var(--color-gray-50);
  --color-informative: var(--color-blue-900);
  --color-pass-secondary: var(--color-green-700);
  --color-pass: var(--color-green);
  --color-not-applicable: var(--color-gray-600);

  /* Component variables */
  --audit-description-padding-left: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right));
  --audit-explanation-line-height: 16px;
  --audit-group-margin-bottom: calc(var(--default-padding) * 6);
  --audit-group-padding-vertical: 8px;
  --audit-margin-horizontal: 5px;
  --audit-padding-vertical: 8px;
  --category-padding: calc(var(--default-padding) * 6) var(--edge-gap-padding) calc(var(--default-padding) * 4);
  --chevron-line-stroke: var(--color-gray-600);
  --chevron-size: 12px;
  --default-padding: 8px;
  --edge-gap-padding: calc(var(--default-padding) * 4);
  --env-item-background-color: var(--color-gray-100);
  --env-item-font-size: 28px;
  --env-item-line-height: 36px;
  --env-item-padding: 10px 0px;
  --env-name-min-width: 220px;
  --footer-padding-vertical: 16px;
  --gauge-circle-size-big: 96px;
  --gauge-circle-size: 48px;
  --gauge-circle-size-sm: 32px;
  --gauge-label-font-size-big: 18px;
  --gauge-label-font-size: var(--report-font-size-secondary);
  --gauge-label-line-height-big: 24px;
  --gauge-label-line-height: var(--report-line-height-secondary);
  --gauge-percentage-font-size-big: 38px;
  --gauge-percentage-font-size: var(--report-font-size-secondary);
  --gauge-wrapper-width: 120px;
  --header-line-height: 24px;
  --highlighter-background-color: var(--report-text-color);
  --icon-square-size: calc(var(--score-icon-size) * 0.88);
  --image-preview-size: 48px;
  --link-color: var(--color-blue-primary);
  --locale-selector-background-color: var(--color-white);
  --metric-toggle-lines-fill: #7F7F7F;
  --metric-value-font-size: calc(var(--report-font-size) * 1.8);
  --metrics-toggle-background-color: var(--color-gray-200);
  --plugin-badge-background-color: var(--color-white);
  --plugin-badge-size-big: calc(var(--gauge-circle-size-big) / 2.7);
  --plugin-badge-size: calc(var(--gauge-circle-size) / 2.7);
  --plugin-icon-size: 65%;
  --report-background-color: #fff;
  --report-border-color-secondary: #ebebeb;
  --report-font-family-monospace: 'Roboto Mono', 'Menlo', 'dejavu sans mono', 'Consolas', 'Lucida Console', monospace;
  --report-font-family: Roboto, Helvetica, Arial, sans-serif;
  --report-font-size: 14px;
  --report-font-size-secondary: 12px;
  --report-icon-size: var(--score-icon-background-size);
  --report-line-height: 24px;
  --report-line-height-secondary: 20px;
  --report-monospace-font-size: calc(var(--report-font-size) * 0.85);
  --report-text-color-secondary: var(--color-gray-800);
  --report-text-color: var(--color-gray-900);
  --report-content-max-width: calc(60 * var(--report-font-size)); /* defaults to 840px */
  --report-content-min-width: 360px;
  --report-content-max-width-minus-edge-gap: calc(var(--report-content-max-width) - var(--edge-gap-padding) * 2);
  --score-container-padding: 8px;
  --score-icon-background-size: 24px;
  --score-icon-margin-left: 6px;
  --score-icon-margin-right: 14px;
  --score-icon-margin: 0 var(--score-icon-margin-right) 0 var(--score-icon-margin-left);
  --score-icon-size: 12px;
  --score-icon-size-big: 16px;
  --screenshot-overlay-background: rgba(0, 0, 0, 0.3);
  --section-padding-vertical: calc(var(--default-padding) * 6);
  --snippet-background-color: var(--color-gray-50);
  --snippet-color: #0938C2;
  --stackpack-padding-horizontal: 10px;
  --sticky-header-background-color: var(--report-background-color);
  --sticky-header-buffer: var(--topbar-height);
  --sticky-header-height: calc(var(--gauge-circle-size-sm) + var(--score-container-padding) * 2 + 1em);
  --table-group-header-background-color: #EEF1F4;
  --table-group-header-text-color: var(--color-gray-700);
  --table-higlight-background-color: #F5F7FA;
  --tools-icon-color: var(--color-gray-600);
  --topbar-background-color: var(--color-white);
  --topbar-height: 32px;
  --topbar-logo-size: 24px;
  --topbar-padding: 0 8px;
  --toplevel-warning-background-color: hsla(30, 100%, 75%, 10%);
  --toplevel-warning-message-text-color: var(--color-average-secondary);
  --toplevel-warning-padding: 18px;
  --toplevel-warning-text-color: var(--report-text-color);

  /* SVGs */
  --plugin-icon-url-dark: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23FFFFFF"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>\u003c/svg>');
  --plugin-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24px" height="24px" viewBox="0 0 24 24" fill="%23757575"><path d="M0 0h24v24H0z" fill="none"/><path d="M20.5 11H19V7c0-1.1-.9-2-2-2h-4V3.5C13 2.12 11.88 1 10.5 1S8 2.12 8 3.5V5H4c-1.1 0-1.99.9-1.99 2v3.8H3.5c1.49 0 2.7 1.21 2.7 2.7s-1.21 2.7-2.7 2.7H2V20c0 1.1.9 2 2 2h3.8v-1.5c0-1.49 1.21-2.7 2.7-2.7 1.49 0 2.7 1.21 2.7 2.7V22H17c1.1 0 2-.9 2-2v-4h1.5c1.38 0 2.5-1.12 2.5-2.5S21.88 11 20.5 11z"/>\u003c/svg>');

  --pass-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>check\u003c/title><path fill="%23178239" d="M24 4C12.95 4 4 12.95 4 24c0 11.04 8.95 20 20 20 11.04 0 20-8.96 20-20 0-11.05-8.96-20-20-20zm-4 30L10 24l2.83-2.83L20 28.34l15.17-15.17L38 16 20 34z"/>\u003c/svg>');
  --average-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>info\u003c/title><path fill="%23E67700" d="M24 4C12.95 4 4 12.95 4 24s8.95 20 20 20 20-8.95 20-20S35.05 4 24 4zm2 30h-4V22h4v12zm0-16h-4v-4h4v4z"/>\u003c/svg>');
  --fail-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48"><title>warn\u003c/title><path fill="%23C7221F" d="M2 42h44L24 4 2 42zm24-6h-4v-4h4v4zm0-8h-4v-8h4v8z"/>\u003c/svg>');
  --error-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 3 15"><title>error\u003c/title><path d="M0 15H 3V 12H 0V" fill="%23FF4E42"/><path d="M0 9H 3V 0H 0V" fill="%23FF4E42"/>\u003c/svg>');

  --swap-locale-icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="#000000"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>\u003c/svg>');
}

@media not print {
  .lh-dark {
    /* Pallete */
    --color-gray-200: var(--color-gray-800);
    --color-gray-300: #616161;
    --color-gray-400: var(--color-gray-600);
    --color-gray-700: var(--color-gray-400);
    --color-gray-50: #757575;
    --color-gray-600: var(--color-gray-500);
    --color-green-700: var(--color-green);
    --color-orange-700: var(--color-orange);
    --color-red-700: var(--color-red);
    --color-teal-600: var(--color-cyan-500);

    /* Context-specific colors */
    --color-hover: rgba(0, 0, 0, 0.2);
    --color-informative: var(--color-blue-200);

    /* Component variables */
    --env-item-background-color: #393535;
    --link-color: var(--color-blue-200);
    --locale-selector-background-color: var(--color-gray-200);
    --plugin-badge-background-color: var(--color-gray-800);
    --report-background-color: var(--color-gray-900);
    --report-border-color-secondary: var(--color-gray-200);
    --report-text-color-secondary: var(--color-gray-400);
    --report-text-color: var(--color-gray-100);
    --snippet-color: var(--color-cyan-500);
    --topbar-background-color: var(--color-gray);
    --toplevel-warning-background-color: hsl(33deg 14% 18%);
    --toplevel-warning-message-text-color: var(--color-orange-700);
    --toplevel-warning-text-color: var(--color-gray-100);
    --table-group-header-background-color: rgba(186, 196, 206, 0.15);
    --table-group-header-text-color: var(--color-gray-100);
    --table-higlight-background-color: rgba(186, 196, 206, 0.09);

    /* SVGs */
    --plugin-icon-url: var(--plugin-icon-url-dark);
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media only screen and (max-width: 480px) {
  .lh-vars {
    --audit-group-margin-bottom: 20px;
    --edge-gap-padding: var(--default-padding);
    --env-name-min-width: 120px;
    --gauge-circle-size-big: 96px;
    --gauge-circle-size: 72px;
    --gauge-label-font-size-big: 22px;
    --gauge-label-font-size: 14px;
    --gauge-label-line-height-big: 26px;
    --gauge-label-line-height: 20px;
    --gauge-percentage-font-size-big: 34px;
    --gauge-percentage-font-size: 26px;
    --gauge-wrapper-width: 112px;
    --header-padding: 16px 0 16px 0;
    --image-preview-size: 24px;
    --plugin-icon-size: 75%;
    --report-font-size: 14px;
    --report-line-height: 20px;
    --score-icon-margin-left: 2px;
    --score-icon-size: 10px;
    --topbar-height: 28px;
    --topbar-logo-size: 20px;
  }
}

@container lh-container (max-width: 480px) {
  .lh-vars {
    --audit-group-margin-bottom: 20px;
    --edge-gap-padding: var(--default-padding);
    --env-name-min-width: 120px;
    --gauge-circle-size-big: 96px;
    --gauge-circle-size: 72px;
    --gauge-label-font-size-big: 22px;
    --gauge-label-font-size: 14px;
    --gauge-label-line-height-big: 26px;
    --gauge-label-line-height: 20px;
    --gauge-percentage-font-size-big: 34px;
    --gauge-percentage-font-size: 26px;
    --gauge-wrapper-width: 112px;
    --header-padding: 16px 0 16px 0;
    --image-preview-size: 24px;
    --plugin-icon-size: 75%;
    --report-font-size: 14px;
    --report-line-height: 20px;
    --score-icon-margin-left: 2px;
    --score-icon-size: 10px;
    --topbar-height: 28px;
    --topbar-logo-size: 20px;
  }
}

.lh-vars.lh-devtools {
  --audit-explanation-line-height: 14px;
  --audit-group-margin-bottom: 20px;
  --audit-group-padding-vertical: 12px;
  --audit-padding-vertical: 4px;
  --category-padding: 12px;
  --default-padding: 12px;
  --env-name-min-width: 120px;
  --footer-padding-vertical: 8px;
  --gauge-circle-size-big: 72px;
  --gauge-circle-size: 64px;
  --gauge-label-font-size-big: 22px;
  --gauge-label-font-size: 14px;
  --gauge-label-line-height-big: 26px;
  --gauge-label-line-height: 20px;
  --gauge-percentage-font-size-big: 34px;
  --gauge-percentage-font-size: 26px;
  --gauge-wrapper-width: 97px;
  --header-line-height: 20px;
  --header-padding: 16px 0 16px 0;
  --screenshot-overlay-background: transparent;
  --plugin-icon-size: 75%;
  --report-font-family-monospace: 'Menlo', 'dejavu sans mono', 'Consolas', 'Lucida Console', monospace;
  --report-font-family: '.SFNSDisplay-Regular', 'Helvetica Neue', 'Lucida Grande', sans-serif;
  --report-font-size: 12px;
  --report-line-height: 20px;
  --score-icon-margin-left: 2px;
  --score-icon-size: 10px;
  --section-padding-vertical: 8px;
}

.lh-container:has(.lh-sticky-header) {
  --sticky-header-buffer: calc(var(--topbar-height) + var(--sticky-header-height));
}

.lh-container:not(.lh-topbar + .lh-container) {
  --topbar-height: 0;
  --sticky-header-height: 0;
  --sticky-header-buffer: 0;
}

.lh-max-viewport {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
}

.lh-devtools.lh-root {
  height: 100%;
}
.lh-devtools.lh-root img {
  /* Override devtools default 'min-width: 0' so svg without size in a flexbox isn't collapsed. */
  min-width: auto;
}
.lh-devtools .lh-container {
  overflow-y: scroll;
  height: calc(100% - var(--topbar-height));
  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */
  --sticky-header-buffer: 0;
}
.lh-devtools .lh-container:has(.lh-sticky-header) {
  /** The .lh-container is the scroll parent in DevTools so we exclude the topbar from the sticky header buffer. */
  --sticky-header-buffer: var(--sticky-header-height);
}
@media print {
  .lh-devtools .lh-container {
    overflow: unset;
  }
}
.lh-devtools .lh-sticky-header {
  /* This is normally the height of the topbar, but we want it to stick to the top of our scroll container .lh-container\` */
  top: 0;
}
.lh-devtools .lh-element-screenshot__overlay {
  position: absolute;
}

@keyframes fadeIn {
  0% { opacity: 0;}
  100% { opacity: 0.6;}
}

.lh-root *, .lh-root *::before, .lh-root *::after {
  box-sizing: border-box;
}

.lh-root {
  font-family: var(--report-font-family);
  font-size: var(--report-font-size);
  margin: 0;
  line-height: var(--report-line-height);
  background: var(--report-background-color);
  color: var(--report-text-color);
}

.lh-root :focus-visible {
    outline: -webkit-focus-ring-color auto 3px;
}
.lh-root summary:focus {
    outline: none;
    box-shadow: 0 0 0 1px hsl(217, 89%, 61%);
}

.lh-root [hidden] {
  display: none !important;
}

.lh-root pre {
  margin: 0;
}

.lh-root pre,
.lh-root code {
  font-family: var(--report-font-family-monospace);
}

.lh-root details > summary {
  cursor: pointer;
}

.lh-hidden {
  display: none !important;
}

.lh-container {
  /*
  Text wrapping in the report is so much FUN!
  We have a \`word-break: break-word;\` globally here to prevent a few common scenarios, namely
  long non-breakable text (usually URLs) found in:
    1. The footer
    2. .lh-node (outerHTML)
    3. .lh-code

  With that sorted, the next challenge is appropriate column sizing and text wrapping inside our
  .lh-details tables. Even more fun.
    * We don't want table headers ("Potential Savings (ms)") to wrap or their column values, but
    we'd be happy for the URL column to wrap if the URLs are particularly long.
    * We want the narrow columns to remain narrow, providing the most column width for URL
    * We don't want the table to extend past 100% width.
    * Long URLs in the URL column can wrap. Util.getURLDisplayName maxes them out at 64 characters,
      but they do not get any overflow:ellipsis treatment.
  */
  word-break: break-word;

  container-name: lh-container;
  container-type: inline-size;
}

.lh-audit-group a,
.lh-category-header__description a,
.lh-audit__description a,
.lh-warnings a,
.lh-footer a,
.lh-table-column--link a {
  color: var(--link-color);
}

.lh-audit__description, .lh-audit__stackpack {
  --inner-audit-padding-right: var(--stackpack-padding-horizontal);
  padding-left: var(--audit-description-padding-left);
  padding-right: var(--inner-audit-padding-right);
  padding-top: 8px;
  padding-bottom: 8px;
}

.lh-details {
  margin-top: var(--default-padding);
  margin-bottom: var(--default-padding);
  margin-left: var(--audit-description-padding-left);
  /* whatever the .lh-details side margins are */
  width: 100%;
}

.lh-audit__stackpack {
  display: flex;
  align-items: center;
}

.lh-audit__stackpack__img {
  max-width: 30px;
  margin-right: var(--default-padding)
}

/* Report header */

.lh-report-icon {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
}
.lh-report-icon[disabled] {
  opacity: 0.3;
  pointer-events: none;
}

.lh-report-icon::before {
  content: "";
  margin: 4px;
  background-repeat: no-repeat;
  width: var(--report-icon-size);
  height: var(--report-icon-size);
  opacity: 0.7;
  display: inline-block;
  vertical-align: middle;
}
.lh-report-icon:hover::before {
  opacity: 1;
}
.lh-dark .lh-report-icon::before {
  filter: invert(1);
}
.lh-report-icon--print::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z"/><path fill="none" d="M0 0h24v24H0z"/>\u003c/svg>');
}
.lh-report-icon--copy::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>\u003c/svg>');
}
.lh-report-icon--open::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M0 0h24v24H0z" fill="none"/><path d="M19 4H5c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h4v-2H5V8h14v10h-4v2h4c1.1 0 2-.9 2-2V6c0-1.1-.89-2-2-2zm-7 6l-4 4h3v6h2v-6h3l-4-4z"/>\u003c/svg>');
}
.lh-report-icon--download::before {
  background-image: url('data:image/svg+xml;utf8,<svg height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/><path d="M0 0h24v24H0z" fill="none"/>\u003c/svg>');
}
.lh-report-icon--dark::before {
  background-image:url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 0 100 125"><path d="M50 23.587c-16.27 0-22.799 12.574-22.799 21.417 0 12.917 10.117 22.451 12.436 32.471h20.726c2.32-10.02 12.436-19.554 12.436-32.471 0-8.843-6.528-21.417-22.799-21.417zM39.637 87.161c0 3.001 1.18 4.181 4.181 4.181h.426l.41 1.231C45.278 94.449 46.042 95 48.019 95h3.963c1.978 0 2.74-.551 3.365-2.427l.409-1.231h.427c3.002 0 4.18-1.18 4.18-4.181V80.91H39.637v6.251zM50 18.265c1.26 0 2.072-.814 2.072-2.073v-9.12C52.072 5.813 51.26 5 50 5c-1.259 0-2.072.813-2.072 2.073v9.12c0 1.259.813 2.072 2.072 2.072zM68.313 23.727c.994.774 2.135.634 2.91-.357l5.614-7.187c.776-.992.636-2.135-.356-2.909-.992-.776-2.135-.636-2.91.357l-5.613 7.186c-.778.993-.636 2.135.355 2.91zM91.157 36.373c-.306-1.222-1.291-1.815-2.513-1.51l-8.85 2.207c-1.222.305-1.814 1.29-1.51 2.512.305 1.223 1.291 1.814 2.513 1.51l8.849-2.206c1.223-.305 1.816-1.291 1.511-2.513zM86.757 60.48l-8.331-3.709c-1.15-.512-2.225-.099-2.736 1.052-.512 1.151-.1 2.224 1.051 2.737l8.33 3.707c1.15.514 2.225.101 2.736-1.05.513-1.149.1-2.223-1.05-2.737zM28.779 23.37c.775.992 1.917 1.131 2.909.357.992-.776 1.132-1.917.357-2.91l-5.615-7.186c-.775-.992-1.917-1.132-2.909-.357s-1.131 1.917-.356 2.909l5.614 7.187zM21.715 39.583c.305-1.223-.288-2.208-1.51-2.513l-8.849-2.207c-1.222-.303-2.208.289-2.513 1.511-.303 1.222.288 2.207 1.511 2.512l8.848 2.206c1.222.304 2.208-.287 2.513-1.509zM21.575 56.771l-8.331 3.711c-1.151.511-1.563 1.586-1.05 2.735.511 1.151 1.586 1.563 2.736 1.052l8.331-3.711c1.151-.511 1.563-1.586 1.05-2.735-.512-1.15-1.585-1.562-2.736-1.052z"/>\u003c/svg>');
}
.lh-report-icon--treemap::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 0 24 24" width="24px" fill="black"><path d="M3 5v14h19V5H3zm2 2h15v4H5V7zm0 10v-4h4v4H5zm6 0v-4h9v4h-9z"/>\u003c/svg>');
}
.lh-report-icon--date::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 11h2v2H7v-2zm14-5v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6c0-1.1.9-2 2-2h1V2h2v2h8V2h2v2h1a2 2 0 012 2zM5 8h14V6H5v2zm14 12V10H5v10h14zm-4-7h2v-2h-2v2zm-4 0h2v-2h-2v2z"/>\u003c/svg>');
}
.lh-report-icon--devices::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M4 6h18V4H4a2 2 0 00-2 2v11H0v3h14v-3H4V6zm19 2h-6a1 1 0 00-1 1v10c0 .6.5 1 1 1h6c.6 0 1-.5 1-1V9c0-.6-.5-1-1-1zm-1 9h-4v-7h4v7z"/>\u003c/svg>');
}
.lh-report-icon--world::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 2a10 10 0 1 0 0 20 10 10 0 0 0 0-20zm7 6h-3c-.3-1.3-.8-2.5-1.4-3.6A8 8 0 0 1 18.9 8zm-7-4a14 14 0 0 1 2 4h-4a14 14 0 0 1 2-4zM4.3 14a8.2 8.2 0 0 1 0-4h3.3a16.5 16.5 0 0 0 0 4H4.3zm.8 2h3a14 14 0 0 0 1.3 3.6A8 8 0 0 1 5.1 16zm3-8H5a8 8 0 0 1 4.3-3.6L8 8zM12 20a14 14 0 0 1-2-4h4a14 14 0 0 1-2 4zm2.3-6H9.7a14.7 14.7 0 0 1 0-4h4.6a14.6 14.6 0 0 1 0 4zm.3 5.6c.6-1.2 1-2.4 1.4-3.6h3a8 8 0 0 1-4.4 3.6zm1.8-5.6a16.5 16.5 0 0 0 0-4h3.3a8.2 8.2 0 0 1 0 4h-3.3z"/>\u003c/svg>');
}
.lh-report-icon--stopwatch::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15 1H9v2h6V1zm-4 13h2V8h-2v6zm8.1-6.6L20.5 6l-1.4-1.4L17.7 6A9 9 0 0 0 3 13a9 9 0 1 0 16-5.6zm-7 12.6a7 7 0 1 1 0-14 7 7 0 0 1 0 14z"/>\u003c/svg>');
}
.lh-report-icon--networkspeed::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M15.9 5c-.2 0-.3 0-.4.2v.2L10.1 17a2 2 0 0 0-.2 1 2 2 0 0 0 4 .4l2.4-12.9c0-.3-.2-.5-.5-.5zM1 9l2 2c2.9-2.9 6.8-4 10.5-3.6l1.2-2.7C10 3.8 4.7 5.3 1 9zm20 2 2-2a15.4 15.4 0 0 0-5.6-3.6L17 8.2c1.5.7 2.9 1.6 4.1 2.8zm-4 4 2-2a9.9 9.9 0 0 0-2.7-1.9l-.5 3 1.2.9zM5 13l2 2a7.1 7.1 0 0 1 4-2l1.3-2.9C9.7 10.1 7 11 5 13z"/>\u003c/svg>');
}
.lh-report-icon--samples-one::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="7" cy="14" r="3"/><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>\u003c/svg>');
}
.lh-report-icon--samples-many::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 18a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm4-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm5.6 17.6a4 4 0 1 1 0-8 4 4 0 0 1 0 8zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/><circle cx="7" cy="14" r="3"/><circle cx="11" cy="6" r="3"/>\u003c/svg>');
}
.lh-report-icon--chrome::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="-50 -50 562 562"><path d="M256 25.6v25.6a204 204 0 0 1 144.8 60 204 204 0 0 1 60 144.8 204 204 0 0 1-60 144.8 204 204 0 0 1-144.8 60 204 204 0 0 1-144.8-60 204 204 0 0 1-60-144.8 204 204 0 0 1 60-144.8 204 204 0 0 1 144.8-60V0a256 256 0 1 0 0 512 256 256 0 0 0 0-512v25.6z"/><path d="M256 179.2v25.6a51.3 51.3 0 0 1 0 102.4 51.3 51.3 0 0 1 0-102.4v-51.2a102.3 102.3 0 1 0-.1 204.7 102.3 102.3 0 0 0 .1-204.7v25.6z"/><path d="M256 204.8h217.6a25.6 25.6 0 0 0 0-51.2H256a25.6 25.6 0 0 0 0 51.2m44.3 76.8L191.5 470.1a25.6 25.6 0 1 0 44.4 25.6l108.8-188.5a25.6 25.6 0 1 0-44.4-25.6m-88.6 0L102.9 93.2a25.7 25.7 0 0 0-35-9.4 25.7 25.7 0 0 0-9.4 35l108.8 188.5a25.7 25.7 0 0 0 35 9.4 25.9 25.9 0 0 0 9.4-35.1"/>\u003c/svg>');
}
.lh-report-icon--external::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg"><path d="M3.15 11.9a1.01 1.01 0 0 1-.743-.307 1.01 1.01 0 0 1-.306-.743v-7.7c0-.292.102-.54.306-.744a1.01 1.01 0 0 1 .744-.306H7v1.05H3.15v7.7h7.7V7h1.05v3.85c0 .291-.103.54-.307.743a1.01 1.01 0 0 1-.743.307h-7.7Zm2.494-2.8-.743-.744 5.206-5.206H8.401V2.1h3.5v3.5h-1.05V3.893L5.644 9.1Z"/>\u003c/svg>');
}
.lh-report-icon--experiment::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="none"><path d="M4.50002 17C3.86136 17 3.40302 16.7187 3.12502 16.156C2.84702 15.5933 2.90936 15.069 3.31202 14.583L7.50002 9.5V4.5H6.75002C6.54202 4.5 6.36502 4.427 6.21902 4.281C6.07302 4.135 6.00002 3.958 6.00002 3.75C6.00002 3.542 6.07302 3.365 6.21902 3.219C6.36502 3.073 6.54202 3 6.75002 3H13.25C13.458 3 13.635 3.073 13.781 3.219C13.927 3.365 14 3.542 14 3.75C14 3.958 13.927 4.135 13.781 4.281C13.635 4.427 13.458 4.5 13.25 4.5H12.5V9.5L16.688 14.583C17.0767 15.069 17.132 15.5933 16.854 16.156C16.5767 16.7187 16.1254 17 15.5 17H4.50002ZM4.50002 15.5H15.5L11 10V4.5H9.00002V10L4.50002 15.5Z" fill="black"/>\u003c/svg>');
}

/** These are still icons, but w/o the auto-color invert / opacity / etc. that come with .lh-report-icon */

.lh-report-plain-icon {
  display: flex;
  align-items: center;
}
.lh-report-plain-icon::before {
  content: "";
  background-repeat: no-repeat;
  width: var(--report-icon-size);
  height: var(--report-icon-size);
  display: inline-block;
  margin-right: 5px;
}

.lh-report-plain-icon--checklist-pass::before {
  --icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M8.938 13L13.896 8.062L12.833 7L8.938 10.875L7.167 9.125L6.104 10.188L8.938 13ZM10 18C8.90267 18 7.868 17.7917 6.896 17.375C5.924 16.9583 5.07333 16.3853 4.344 15.656C3.61467 14.9267 3.04167 14.076 2.625 13.104C2.20833 12.132 2 11.0973 2 10C2 8.88867 2.20833 7.85033 2.625 6.885C3.04167 5.92033 3.61467 5.07333 4.344 4.344C5.07333 3.61467 5.924 3.04167 6.896 2.625C7.868 2.20833 8.90267 2 10 2C11.1113 2 12.1497 2.20833 13.115 2.625C14.0797 3.04167 14.9267 3.61467 15.656 4.344C16.3853 5.07333 16.9583 5.92033 17.375 6.885C17.7917 7.85033 18 8.88867 18 10C18 11.0973 17.7917 12.132 17.375 13.104C16.9583 14.076 16.3853 14.9267 15.656 15.656C14.9267 16.3853 14.0797 16.9583 13.115 17.375C12.1497 17.7917 11.1113 18 10 18ZM10 16.5C11.8053 16.5 13.34 15.868 14.604 14.604C15.868 13.34 16.5 11.8053 16.5 10C16.5 8.19467 15.868 6.66 14.604 5.396C13.34 4.132 11.8053 3.5 10 3.5C8.19467 3.5 6.66 4.132 5.396 5.396C4.132 6.66 3.5 8.19467 3.5 10C3.5 11.8053 4.132 13.34 5.396 14.604C6.66 15.868 8.19467 16.5 10 16.5Z" fill="black"/>\u003c/svg>');
  background-color: var(--color-pass);
  mask: var(--icon-url) center / contain no-repeat;
}
.lh-report-plain-icon--checklist-fail::before {
  --icon-url: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path fill-rule="evenodd" clip-rule="evenodd" d="M17.5 10C17.5 14.1421 14.1421 17.5 10 17.5C5.85786 17.5 2.5 14.1421 2.5 10C2.5 5.85786 5.85786 2.5 10 2.5C14.1421 2.5 17.5 5.85786 17.5 10ZM16 10C16 13.3137 13.3137 16 10 16C8.6135 16 7.33683 15.5297 6.32083 14.7399L14.7399 6.32083C15.5297 7.33683 16 8.6135 16 10ZM5.26016 13.6793L13.6793 5.26016C12.6633 4.47033 11.3866 4 10 4C6.68629 4 4 6.68629 4 10C4 11.3866 4.47033 12.6633 5.26016 13.6793Z" fill="black"/>\u003c/svg>');
  background-color: var(--color-fail);
  mask: var(--icon-url) center / contain no-repeat;
}

.lh-buttons {
  display: flex;
  flex-wrap: wrap;
  margin: var(--default-padding) 0;
}
.lh-button {
  height: 32px;
  border: 1px solid var(--report-border-color-secondary);
  border-radius: 3px;
  color: var(--link-color);
  background-color: var(--report-background-color);
  margin: 5px;
}

.lh-button:first-of-type {
  margin-left: 0;
}

/* Node */
.lh-node__snippet {
  font-family: var(--report-font-family-monospace);
  color: var(--snippet-color);
  font-size: var(--report-monospace-font-size);
  line-height: 20px;
}

.lh-checklist {
  list-style: none;
  padding: 0;
}

.lh-checklist-item {
  margin: 10px 0 10px 0;
}

/* Score */

.lh-audit__score-icon {
  width: var(--score-icon-size);
  height: var(--score-icon-size);
  margin: var(--score-icon-margin);
}

.lh-audit--pass .lh-audit__display-text {
  color: var(--color-pass-secondary);
}
.lh-audit--pass .lh-audit__score-icon,
.lh-scorescale-range--pass::before {
  border-radius: 100%;
  background: var(--color-pass);
}

.lh-audit--average .lh-audit__display-text {
  color: var(--color-average-secondary);
}
.lh-audit--average .lh-audit__score-icon,
.lh-scorescale-range--average::before {
  background: var(--color-average);
  width: var(--icon-square-size);
  height: var(--icon-square-size);
}

.lh-audit--fail .lh-audit__display-text {
  color: var(--color-fail-secondary);
}
.lh-audit--fail .lh-audit__score-icon,
.lh-audit--error .lh-audit__score-icon,
.lh-scorescale-range--fail::before {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}

.lh-audit--error .lh-audit__score-icon,
.lh-metric--error .lh-metric__icon {
  background-image: var(--error-icon-url);
  background-repeat: no-repeat;
  background-position: center;
  border: none;
}

.lh-gauge__wrapper--fail .lh-gauge--error {
  background-image: var(--error-icon-url);
  background-repeat: no-repeat;
  background-position: center;
  transform: scale(0.5);
  top: var(--score-container-padding);
}

.lh-audit--manual .lh-audit__display-text,
.lh-audit--notapplicable .lh-audit__display-text {
  color: var(--color-gray-600);
}
.lh-audit--manual .lh-audit__score-icon,
.lh-audit--notapplicable .lh-audit__score-icon {
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);
  border-radius: 100%;
  background: none;
}

.lh-audit--informative .lh-audit__display-text {
  color: var(--color-gray-600);
}

.lh-audit--informative .lh-audit__score-icon {
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-400);
  border-radius: 100%;
}

.lh-audit__description,
.lh-audit__stackpack {
  color: var(--report-text-color-secondary);
}
.lh-audit__adorn {
  border: 1px solid var(--color-gray-500);
  border-radius: 3px;
  margin: 0 3px;
  padding: 0 2px;
  line-height: 1.1;
  display: inline-block;
  font-size: 90%;
  color: var(--report-text-color-secondary);
}

.lh-category-header__description  {
  text-align: center;
  color: var(--color-gray-700);
  margin: 0px auto;
  max-width: 400px;
}


.lh-audit__display-text,
.lh-chevron-container {
  margin: 0 var(--audit-margin-horizontal);
}
.lh-chevron-container {
  margin-right: 0;
}

.lh-audit__title-and-text {
  flex: 1;
}

.lh-audit__title-and-text code {
  color: var(--snippet-color);
  font-size: var(--report-monospace-font-size);
}

/* Prepend display text with em dash separator. */
.lh-audit__display-text:not(:empty):before {
  content: '\u2014';
  margin-right: var(--audit-margin-horizontal);
}

/* Expandable Details (Audit Groups, Audits) */
.lh-audit__header {
  display: flex;
  align-items: center;
  padding: var(--default-padding);
}


.lh-metricfilter {
  display: grid;
  justify-content: end;
  align-items: center;
  grid-auto-flow: column;
  gap: 4px;
  color: var(--color-gray-700);
}

.lh-metricfilter__radio {
  /*
   * Instead of hiding, position offscreen so it's still accessible to screen readers
   * https://bugs.chromium.org/p/chromium/issues/detail?id=1439785
   */
  position: fixed;
  left: -9999px;
}
.lh-metricfilter input[type='radio']:focus-visible + label {
  outline: -webkit-focus-ring-color auto 1px;
}

.lh-metricfilter__label {
  display: inline-flex;
  padding: 0 4px;
  height: 16px;
  text-decoration: underline;
  align-items: center;
  cursor: pointer;
  font-size: 90%;
}

.lh-metricfilter__label--active {
  background: var(--color-blue-primary);
  color: var(--color-white);
  border-radius: 3px;
  text-decoration: none;
}
/* Give the 'All' choice a more muted display */
.lh-metricfilter__label--active[for="metric-All"] {
  background-color: var(--color-blue-200) !important;
  color: black !important;
}

.lh-metricfilter__text {
  margin-right: 8px;
}

/* If audits are filtered, hide the itemcount for Passed Audits\u2026 */
.lh-category--filtered .lh-audit-group .lh-audit-group__itemcount {
  display: none;
}


.lh-audit__header:hover {
  background-color: var(--color-hover);
}

/* We want to hide the browser's default arrow marker on summary elements. Admittedly, it's complicated. */
.lh-root details > summary {
  /* Blink 89+ and Firefox will hide the arrow when display is changed from (new) default of \`list-item\` to block.  https://chromestatus.com/feature/6730096436051968*/
  display: block;
}
/* Safari and Blink <=88 require using the -webkit-details-marker selector */
.lh-root details > summary::-webkit-details-marker {
  display: none;
}

/* Perf Metric */

.lh-metrics-container {
  display: grid;
  grid-auto-rows: 1fr;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: var(--report-line-height);
  margin-bottom: var(--default-padding);
}

.lh-metric {
  border-top: 1px solid var(--report-border-color-secondary);
}

.lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(-n+2) {
  border-bottom: 1px solid var(--report-border-color-secondary);
}

.lh-metric__innerwrap {
  display: grid;
  /**
   * Icon -- Metric Name
   *      -- Metric Value
   */
  grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 1fr;
  align-items: center;
  padding: var(--default-padding);
}

.lh-metric__details {
  order: -1;
}

.lh-metric__title {
  flex: 1;
}

.lh-calclink {
  padding-left: calc(1ex / 3);
}

.lh-metric__description {
  display: none;
  grid-column-start: 2;
  grid-column-end: 4;
  color: var(--report-text-color-secondary);
}

.lh-metric__value {
  font-size: var(--metric-value-font-size);
  margin: calc(var(--default-padding) / 2) 0;
  white-space: nowrap; /* No wrapping between metric value and the icon */
  grid-column-start: 2;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-metrics-container {
    display: block;
  }

  .lh-metric {
    border-bottom: none !important;
  }
  .lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(1) {
    border-bottom: 1px solid var(--report-border-color-secondary) !important;
  }

  /* Change the grid to 3 columns for narrow viewport. */
  .lh-metric__innerwrap {
  /**
   * Icon -- Metric Name -- Metric Value
   */
    grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 2fr 1fr;
  }
  .lh-metric__value {
    justify-self: end;
    grid-column-start: unset;
  }
}

@container lh-container (max-width: 535px) {
  .lh-metrics-container {
    display: block;
  }

  .lh-metric {
    border-bottom: none !important;
  }
  .lh-category:not(.lh--hoisted-meta) .lh-metric:nth-last-child(1) {
    border-bottom: 1px solid var(--report-border-color-secondary) !important;
  }

  /* Change the grid to 3 columns for narrow viewport. */
  .lh-metric__innerwrap {
  /**
   * Icon -- Metric Name -- Metric Value
   */
    grid-template-columns: calc(var(--score-icon-size) + var(--score-icon-margin-left) + var(--score-icon-margin-right)) 2fr 1fr;
  }
  .lh-metric__value {
    justify-self: end;
    grid-column-start: unset;
  }
}

/* No-JS toggle switch */
/* Keep this selector sync'd w/ \`magicSelector\` in report-ui-features-test.js */
 .lh-metrics-toggle__input:checked ~ .lh-metrics-container .lh-metric__description {
  display: block;
}

/* TODO get rid of the SVGS and clean up these some more */
.lh-metrics-toggle__input {
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0px;
}

.lh-metrics-toggle__input + div > label > .lh-metrics-toggle__labeltext--hide,
.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--show {
  display: none;
}
.lh-metrics-toggle__input:checked + div > label > .lh-metrics-toggle__labeltext--hide {
  display: inline;
}
.lh-metrics-toggle__input:focus + div > label {
  outline: -webkit-focus-ring-color auto 3px;
}

.lh-metrics-toggle__label {
  cursor: pointer;
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
  color: var(--color-gray-700);
}

/* Pushes the metric description toggle button to the right. */
.lh-audit-group--metrics .lh-audit-group__header {
  display: flex;
  justify-content: space-between;
}

.lh-metric__icon,
.lh-scorescale-range::before {
  content: '';
  width: var(--score-icon-size);
  height: var(--score-icon-size);
  display: inline-block;
  margin: var(--score-icon-margin);
}

.lh-metric--pass .lh-metric__value {
  color: var(--color-pass-secondary);
}
.lh-metric--pass .lh-metric__icon {
  border-radius: 100%;
  background: var(--color-pass);
}

.lh-metric--average .lh-metric__value {
  color: var(--color-average-secondary);
}
.lh-metric--average .lh-metric__icon {
  background: var(--color-average);
  width: var(--icon-square-size);
  height: var(--icon-square-size);
}

.lh-metric--fail .lh-metric__value {
  color: var(--color-fail-secondary);
}
.lh-metric--fail .lh-metric__icon {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}

.lh-metric--error .lh-metric__value,
.lh-metric--error .lh-metric__description {
  color: var(--color-fail-secondary);
}

/* Filmstrip */

.lh-filmstrip-container {
  /* smaller gap between metrics and filmstrip */
  margin: -8px auto 0 auto;
}

.lh-filmstrip {
  display: flex;
  justify-content: space-between;
  justify-items: center;
  margin-bottom: var(--default-padding);
  width: 100%;
}

.lh-filmstrip__frame {
  overflow: hidden;
  line-height: 0;
}

.lh-filmstrip__thumbnail {
  border: 1px solid var(--report-border-color-secondary);
  max-height: 150px;
  max-width: 120px;
}

/* Audit */

.lh-audit {
  border-bottom: 1px solid var(--report-border-color-secondary);
}

/* Apply border-top to just the first audit. */
.lh-audit {
  border-top: 1px solid var(--report-border-color-secondary);
}
.lh-audit ~ .lh-audit {
  border-top: none;
}


.lh-audit--error .lh-audit__display-text {
  color: var(--color-fail-secondary);
}

/* Audit Group */

.lh-audit-group {
  margin-bottom: var(--audit-group-margin-bottom);
  position: relative;
}
.lh-audit-group--metrics {
  margin-bottom: calc(var(--audit-group-margin-bottom) / 2);
}

.lh-audit-group--metrics .lh-audit-group__summary {
  margin-top: 0;
  margin-bottom: 0;
}

.lh-audit-group__summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lh-audit-group__header .lh-chevron {
  margin-top: calc((var(--report-line-height) - 5px) / 2);
}

.lh-audit-group__header {
  letter-spacing: 0.8px;
  padding: var(--default-padding);
  padding-left: 0;
}

.lh-audit-group__header, .lh-audit-group__summary {
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
  color: var(--color-gray-700);
}

.lh-audit-group__title {
  text-transform: uppercase;
  font-weight: 500;
}

.lh-audit-group__itemcount {
  color: var(--color-gray-600);
}

.lh-audit-group__footer {
  color: var(--color-gray-600);
  display: block;
  margin-top: var(--default-padding);
}

.lh-details,
.lh-category-header__description,
.lh-audit-group__footer {
  font-size: var(--report-font-size-secondary);
  line-height: var(--report-line-height-secondary);
}

.lh-audit-explanation {
  margin: var(--audit-padding-vertical) 0 calc(var(--audit-padding-vertical) / 2) var(--audit-margin-horizontal);
  line-height: var(--audit-explanation-line-height);
  display: inline-block;
}

.lh-audit--fail .lh-audit-explanation {
  color: var(--color-fail-secondary);
}

/* Report */
.lh-list > :not(:last-child) {
  margin-bottom: calc(var(--default-padding) * 2);
}

.lh-header-container {
  display: block;
  margin: 0 auto;
  position: relative;
  word-wrap: break-word;
}

.lh-header-container .lh-scores-wrapper {
  border-bottom: 1px solid var(--color-gray-200);
}


.lh-report {
  min-width: var(--report-content-min-width);
}

.lh-exception {
  font-size: large;
}

.lh-code {
  white-space: normal;
  margin-top: 0;
  font-size: var(--report-monospace-font-size);
}

.lh-warnings {
  --item-margin: calc(var(--report-line-height) / 6);
  color: var(--color-average-secondary);
  margin: var(--audit-padding-vertical) 0;
  padding: var(--default-padding)
    var(--default-padding)
    var(--default-padding)
    calc(var(--audit-description-padding-left));
  background-color: var(--toplevel-warning-background-color);
}
.lh-warnings span {
  font-weight: bold;
}

.lh-warnings--toplevel {
  --item-margin: calc(var(--header-line-height) / 4);
  color: var(--toplevel-warning-text-color);
  margin-left: auto;
  margin-right: auto;
  max-width: var(--report-content-max-width-minus-edge-gap);
  padding: var(--toplevel-warning-padding);
  border-radius: 8px;
}

.lh-warnings__msg {
  color: var(--toplevel-warning-message-text-color);
  margin: 0;
}

.lh-warnings ul {
  margin: 0;
}
.lh-warnings li {
  margin: var(--item-margin) 0;
}
.lh-warnings li:last-of-type {
  margin-bottom: 0;
}

.lh-scores-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.lh-scores-header__solo {
  padding: 0;
  border: 0;
}

/* Gauge */

.lh-gauge__wrapper--pass {
  color: var(--color-pass-secondary);
  fill: var(--color-pass);
  stroke: var(--color-pass);
}

.lh-gauge__wrapper--average {
  color: var(--color-average-secondary);
  fill: var(--color-average);
  stroke: var(--color-average);
}

.lh-gauge__wrapper--fail {
  color: var(--color-fail-secondary);
  fill: var(--color-fail);
  stroke: var(--color-fail);
}

.lh-gauge__wrapper--not-applicable {
  color: var(--color-not-applicable);
  fill: var(--color-not-applicable);
  stroke: var(--color-not-applicable);
}

.lh-fraction__wrapper .lh-fraction__content::before {
  content: '';
  height: var(--score-icon-size);
  width: var(--score-icon-size);
  margin: var(--score-icon-margin);
  display: inline-block;
}
.lh-fraction__wrapper--pass .lh-fraction__content {
  color: var(--color-pass-secondary);
}
.lh-fraction__wrapper--pass .lh-fraction__background {
  background-color: var(--color-pass);
}
.lh-fraction__wrapper--pass .lh-fraction__content::before {
  background-color: var(--color-pass);
  border-radius: 50%;
}
.lh-fraction__wrapper--average .lh-fraction__content {
  color: var(--color-average-secondary);
}
.lh-fraction__wrapper--average .lh-fraction__background,
.lh-fraction__wrapper--average .lh-fraction__content::before {
  background-color: var(--color-average);
}
.lh-fraction__wrapper--fail .lh-fraction__content {
  color: var(--color-fail);
}
.lh-fraction__wrapper--fail .lh-fraction__background {
  background-color: var(--color-fail);
}
.lh-fraction__wrapper--fail .lh-fraction__content::before {
  border-left: calc(var(--score-icon-size) / 2) solid transparent;
  border-right: calc(var(--score-icon-size) / 2) solid transparent;
  border-bottom: var(--score-icon-size) solid var(--color-fail);
}
.lh-fraction__wrapper--null .lh-fraction__content {
  color: var(--color-gray-700);
}
.lh-fraction__wrapper--null .lh-fraction__background {
  background-color: var(--color-gray-700);
}
.lh-fraction__wrapper--null .lh-fraction__content::before {
  border-radius: 50%;
  border: calc(0.2 * var(--score-icon-size)) solid var(--color-gray-700);
}

.lh-fraction__background {
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: calc(var(--gauge-circle-size) / 2);
  opacity: 0.1;
  z-index: -1;
}

.lh-fraction__content-wrapper {
  height: var(--gauge-circle-size);
  display: flex;
  align-items: center;
}

.lh-fraction__content {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  font-size: calc(0.3 * var(--gauge-circle-size));
  line-height: calc(0.4 * var(--gauge-circle-size));
  width: max-content;
  min-width: calc(1.5 * var(--gauge-circle-size));
  padding: calc(0.1 * var(--gauge-circle-size)) calc(0.2 * var(--gauge-circle-size));
  --score-icon-size: calc(0.21 * var(--gauge-circle-size));
  --score-icon-margin: 0 calc(0.15 * var(--gauge-circle-size)) 0 0;
}

.lh-gauge {
  stroke-linecap: round;
  width: var(--gauge-circle-size);
  height: var(--gauge-circle-size);
}

.lh-category .lh-gauge {
  --gauge-circle-size: var(--gauge-circle-size-big);
}

.lh-gauge-base {
  opacity: 0.1;
}

.lh-gauge-arc {
  fill: none;
  transform-origin: 50% 50%;
  animation: load-gauge var(--transition-length) ease both;
  animation-delay: 250ms;
}

.lh-gauge__svg-wrapper {
  position: relative;
  height: var(--gauge-circle-size);
}
.lh-category .lh-gauge__svg-wrapper,
.lh-category .lh-fraction__wrapper {
  --gauge-circle-size: var(--gauge-circle-size-big);
}

/* The plugin badge overlay */
.lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {
  width: var(--plugin-badge-size);
  height: var(--plugin-badge-size);
  background-color: var(--plugin-badge-background-color);
  background-image: var(--plugin-icon-url);
  background-repeat: no-repeat;
  background-size: var(--plugin-icon-size);
  background-position: 58% 50%;
  content: "";
  position: absolute;
  right: -6px;
  bottom: 0px;
  display: block;
  z-index: 100;
  box-shadow: 0 0 4px rgba(0,0,0,.2);
  border-radius: 25%;
}
.lh-category .lh-gauge__wrapper--plugin .lh-gauge__svg-wrapper::before {
  width: var(--plugin-badge-size-big);
  height: var(--plugin-badge-size-big);
}

@keyframes load-gauge {
  from { stroke-dasharray: 0 352; }
}

.lh-gauge__percentage {
  width: 100%;
  height: var(--gauge-circle-size);
  line-height: var(--gauge-circle-size);
  position: absolute;
  font-family: var(--report-font-family-monospace);
  font-size: calc(var(--gauge-circle-size) * 0.34 + 1.3px);
  text-align: center;
  top: var(--score-container-padding);
}

.lh-category .lh-gauge__percentage {
  --gauge-circle-size: var(--gauge-circle-size-big);
  --gauge-percentage-font-size: var(--gauge-percentage-font-size-big);
}

.lh-gauge__wrapper,
.lh-fraction__wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  text-decoration: none;
  padding: var(--score-container-padding);

  --transition-length: 1s;

  /* Contain the layout style paint & layers during animation*/
  contain: content;
  will-change: opacity; /* Only using for layer promotion */
}

.lh-gauge__label,
.lh-fraction__label {
  font-size: var(--gauge-label-font-size);
  font-weight: 500;
  line-height: var(--gauge-label-line-height);
  margin-top: 10px;
  text-align: center;
  color: var(--report-text-color);
  word-break: keep-all;
}

/* TODO(#8185) use more BEM (.lh-gauge__label--big) instead of relying on descendant selector */
.lh-category .lh-gauge__label,
.lh-category .lh-fraction__label {
  --gauge-label-font-size: var(--gauge-label-font-size-big);
  --gauge-label-line-height: var(--gauge-label-line-height-big);
  margin-top: 14px;
}

.lh-scores-header .lh-gauge__wrapper,
.lh-scores-header .lh-fraction__wrapper,
.lh-sticky-header .lh-gauge__wrapper,
.lh-sticky-header .lh-fraction__wrapper {
  width: var(--gauge-wrapper-width);
}

.lh-scorescale {
  display: inline-flex;

  gap: calc(var(--default-padding) * 4);
  margin: 16px auto 0 auto;
  font-size: var(--report-font-size-secondary);
  color: var(--color-gray-700);

}

.lh-scorescale-range {
  display: flex;
  align-items: center;
  font-family: var(--report-font-family-monospace);
  white-space: nowrap;
}

.lh-category-header__finalscreenshot .lh-scorescale {
  border: 0;
  display: flex;
  justify-content: center;
}

.lh-category-header__finalscreenshot .lh-scorescale-range {
  font-family: unset;
  font-size: 12px;
}

.lh-scorescale-wrap {
  display: contents;
}

/* Hide category score gauages if it's a single category report */
.lh-header--solo-category .lh-scores-wrapper {
  display: none;
}


.lh-categories {
  width: 100%;
}

.lh-category {
  padding: var(--category-padding);
  max-width: var(--report-content-max-width);
  margin: 0 auto;

  scroll-margin-top: calc(var(--sticky-header-buffer) - 1em);
}

.lh-category-wrapper {
  border-bottom: 1px solid var(--color-gray-200);
}
.lh-category-wrapper:last-of-type {
  border-bottom: 0;
}

.lh-category-header {
  margin-bottom: var(--section-padding-vertical);
}

.lh-category-header .lh-score__gauge {
  max-width: 400px;
  width: auto;
  margin: 0px auto;
}

.lh-category-header__finalscreenshot {
  display: grid;
  grid-template: none / 1fr 1px 1fr;
  justify-items: center;
  align-items: center;
  gap: var(--report-line-height);
  min-height: 288px;
  margin-bottom: var(--default-padding);
}

.lh-final-ss-image {
  /* constrain the size of the image to not be too large */
  max-height: calc(var(--gauge-circle-size-big) * 2.8);
  max-width: calc(var(--gauge-circle-size-big) * 3.5);
  border: 1px solid var(--color-gray-200);
  padding: 4px;
  border-radius: 3px;
  display: block;
}

.lh-category-headercol--separator {
  background: var(--color-gray-200);
  width: 1px;
  height: var(--gauge-circle-size-big);
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 780px) {
  .lh-category-header__finalscreenshot {
    grid-template: 1fr 1fr / none
  }
  .lh-category-headercol--separator {
    display: none;
  }
}

@container lh-container (max-width: 780px) {
  .lh-category-header__finalscreenshot {
    grid-template: 1fr 1fr / none
  }
  .lh-category-headercol--separator {
    display: none;
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 964px) {
  .lh-report {
    margin-left: 0;
    width: 100%;
  }
}

/* 964 fits the min-width of the filmstrip */
@container lh-container (max-width: 964px) {
  .lh-report {
    margin-left: 0;
    width: 100%;
  }
}

@media print {
  body {
    -webkit-print-color-adjust: exact; /* print background colors */
  }
  .lh-container {
    display: block;
  }
  .lh-report {
    margin-left: 0;
    padding-top: 0;
  }
  .lh-categories {
    margin-top: 0;
  }
}

.lh-table {
  position: relative;
  border-collapse: separate;
  border-spacing: 0;
  /* Can't assign padding to table, so shorten the width instead. */
  width: calc(100% - var(--audit-description-padding-left) - var(--stackpack-padding-horizontal));
  border: 1px solid var(--report-border-color-secondary);
}

.lh-table thead th {
  position: sticky;
  top: var(--sticky-header-buffer);
  z-index: 1;
  background-color: var(--report-background-color);
  border-bottom: 1px solid var(--report-border-color-secondary);
  font-weight: normal;
  color: var(--color-gray-600);
  /* See text-wrapping comment on .lh-container. */
  word-break: normal;
}

.lh-row--group {
  background-color: var(--table-group-header-background-color);
}

.lh-row--group td {
  font-weight: bold;
  font-size: 1.05em;
  color: var(--table-group-header-text-color);
}

.lh-row--group td:first-child {
  display: block;
  min-width: max-content;
  font-weight: normal;
}

.lh-row--group .lh-text {
  color: inherit;
  text-decoration: none;
  display: inline-block;
}

.lh-row--group a.lh-link:hover {
  text-decoration: underline;
}

.lh-row--group .lh-audit__adorn {
  text-transform: capitalize;
  font-weight: normal;
  padding: 2px 3px 1px 3px;
}

.lh-row--group .lh-audit__adorn1p {
  color: var(--link-color);
  border-color: var(--link-color);
}

.lh-row--group .lh-report-icon--external::before {
  content: "";
  background-repeat: no-repeat;
  width: 14px;
  height: 16px;
  opacity: 0.7;
  display: inline-block;
  vertical-align: middle;
}

.lh-row--group .lh-report-icon--external {
  visibility: hidden;
}

.lh-row--group:hover .lh-report-icon--external {
  visibility: visible;
}

.lh-dark .lh-report-icon--external::before {
  filter: invert(1);
}

/** Manages indentation of two-level and three-level nested adjacent rows */

.lh-row--group ~ [data-entity]:not(.lh-row--group) td:first-child {
  padding-left: 20px;
}

.lh-row--group ~ [data-entity]:not(.lh-row--group) ~ .lh-sub-item-row td:first-child {
  padding-left: 40px;
}

.lh-row--even {
  background-color: var(--table-group-header-background-color);
}
.lh-row--hidden {
  display: none;
}

.lh-table th,
.lh-table td {
  padding: var(--default-padding);
}

.lh-table tr {
  vertical-align: middle;
}

.lh-table tr:hover {
  background-color: var(--table-higlight-background-color);
}

/* Looks unnecessary, but mostly for keeping the <th>s left-aligned */
.lh-table-column--text,
.lh-table-column--source-location,
.lh-table-column--url,
/* .lh-table-column--thumbnail, */
/* .lh-table-column--empty,*/
.lh-table-column--code,
.lh-table-column--node {
  text-align: left;
}

.lh-table-column--code {
  min-width: 100px;
}

.lh-table-column--bytes,
.lh-table-column--timespanMs,
.lh-table-column--ms,
.lh-table-column--numeric {
  text-align: right;
  word-break: normal;
}



.lh-table .lh-table-column--thumbnail {
  width: var(--image-preview-size);
}

.lh-table-column--url {
  min-width: 250px;
}

.lh-table-column--text {
  min-width: 80px;
}

/* Keep columns narrow if they follow the URL column */
/* 12% was determined to be a decent narrow width, but wide enough for column headings */
.lh-table-column--url + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--ms,
.lh-table-column--url + .lh-table-column--ms + th.lh-table-column--bytes,
.lh-table-column--url + .lh-table-column--bytes + th.lh-table-column--timespanMs {
  width: 12%;
}

.lh-text__url-host {
  display: inline;
}

.lh-text__url-host {
  margin-left: calc(var(--report-font-size) / 2);
  opacity: 0.6;
  font-size: 90%
}

.lh-thumbnail {
  object-fit: cover;
  width: var(--image-preview-size);
  height: var(--image-preview-size);
  display: block;
}

.lh-unknown pre {
  overflow: scroll;
  border: solid 1px var(--color-gray-200);
}

.lh-text__url > a {
  color: inherit;
  text-decoration: none;
}

.lh-text__url > a:hover {
  text-decoration: underline dotted #999;
}

.lh-sub-item-row {
  margin-left: 20px;
  margin-bottom: 0;
  color: var(--color-gray-700);
}

.lh-sub-item-row td {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 20px;
}

.lh-sub-item-row .lh-element-screenshot {
  zoom: 0.6;
}

/* Chevron
   https://codepen.io/paulirish/pen/LmzEmK
 */
.lh-chevron {
  --chevron-angle: 42deg;
  /* Edge doesn't support transform: rotate(calc(...)), so we define it here */
  --chevron-angle-right: -42deg;
  width: var(--chevron-size);
  height: var(--chevron-size);
  margin-top: calc((var(--report-line-height) - 12px) / 2);
}

.lh-chevron__lines {
  transition: transform 0.4s;
  transform: translateY(var(--report-line-height));
}
.lh-chevron__line {
 stroke: var(--chevron-line-stroke);
 stroke-width: var(--chevron-size);
 stroke-linecap: square;
 transform-origin: 50%;
 transform: rotate(var(--chevron-angle));
 transition: transform 300ms, stroke 300ms;
}

.lh-expandable-details .lh-chevron__line-right,
.lh-expandable-details[open] .lh-chevron__line-left {
 transform: rotate(var(--chevron-angle-right));
}

.lh-expandable-details[open] .lh-chevron__line-right {
  transform: rotate(var(--chevron-angle));
}


.lh-expandable-details[open]  .lh-chevron__lines {
 transform: translateY(calc(var(--chevron-size) * -1));
}

.lh-expandable-details[open] {
  animation: 300ms openDetails forwards;
  padding-bottom: var(--default-padding);
}

@keyframes openDetails {
  from {
    outline: 1px solid var(--report-background-color);
  }
  to {
   outline: 1px solid;
   box-shadow: 0 2px 4px rgba(0, 0, 0, .24);
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 780px) {
  /* no black outline if we're not confident the entire table can be displayed within bounds */
  .lh-expandable-details[open] {
    animation: none;
  }
}

@container lh-container (max-width: 780px) {
  /* no black outline if we're not confident the entire table can be displayed within bounds */
  .lh-expandable-details[open] {
    animation: none;
  }
}

.lh-expandable-details[open] summary, details.lh-clump > summary {
  border-bottom: 1px solid var(--report-border-color-secondary);
}
details.lh-clump[open] > summary {
  border-bottom-width: 0;
}



details .lh-clump-toggletext--hide,
details[open] .lh-clump-toggletext--show { display: none; }
details[open] .lh-clump-toggletext--hide { display: block;}


/* Tooltip */
.lh-tooltip-boundary {
  position: relative;
}

.lh-tooltip {
  position: absolute;
  display: none; /* Don't retain these layers when not needed */
  opacity: 0;
  background: #ffffff;
  white-space: pre-line; /* Render newlines in the text */
  min-width: 246px;
  max-width: 275px;
  padding: 15px;
  border-radius: 5px;
  text-align: initial;
  line-height: 1.4;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-tooltip {
    min-width: 45vw;
    padding: 3vw;
  }
}

/* shrink tooltips to not be cutoff on left edge of narrow container
   45vw is chosen to be ~= width of the left column of metrics
*/
@container lh-container (max-width: 535px) {
  .lh-tooltip {
    min-width: 45vw;
    padding: 3vw;
  }
}

.lh-tooltip-boundary:hover .lh-tooltip {
  display: block;
  animation: fadeInTooltip 250ms;
  animation-fill-mode: forwards;
  animation-delay: 850ms;
  bottom: 100%;
  z-index: 1;
  will-change: opacity;
  right: 0;
  pointer-events: none;
}

.lh-tooltip::before {
  content: "";
  border: solid transparent;
  border-bottom-color: #fff;
  border-width: 10px;
  position: absolute;
  bottom: -20px;
  right: 6px;
  transform: rotate(180deg);
  pointer-events: none;
}

@keyframes fadeInTooltip {
  0% { opacity: 0; }
  75% { opacity: 1; }
  100% { opacity: 1;  filter: drop-shadow(1px 0px 1px #aaa) drop-shadow(0px 2px 4px hsla(206, 6%, 25%, 0.15)); pointer-events: auto; }
}

/* Element screenshot */
.lh-element-screenshot {
  float: left;
  margin-right: 20px;
}
.lh-element-screenshot__content {
  overflow: hidden;
  min-width: 110px;
  display: flex;
  justify-content: center;
  background-color: var(--report-background-color);
}
.lh-element-screenshot__image {
  position: relative;
  /* Set by ElementScreenshotRenderer.installFullPageScreenshotCssVariable */
  background-image: var(--element-screenshot-url);
  outline: 2px solid #777;
  background-color: white;
  background-repeat: no-repeat;
}
.lh-element-screenshot__mask {
  position: absolute;
  background: #555;
  opacity: 0.8;
}
.lh-element-screenshot__element-marker {
  position: absolute;
  outline: 2px solid var(--color-lime-400);
}
.lh-element-screenshot__overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000; /* .lh-topbar is 1000 */
  background: var(--screenshot-overlay-background);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-out;
}

.lh-element-screenshot__overlay .lh-element-screenshot {
  margin-right: 0; /* clearing margin used in thumbnail case */
  outline: 1px solid var(--color-gray-700);
}

.lh-screenshot-overlay--enabled .lh-element-screenshot {
  cursor: zoom-out;
}
.lh-screenshot-overlay--enabled .lh-node .lh-element-screenshot {
  cursor: zoom-in;
}


.lh-meta__items {
  --meta-icon-size: calc(var(--report-icon-size) * 0.667);
  padding: var(--default-padding);
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background-color: var(--env-item-background-color);
  border-radius: 3px;
  margin: 0 0 var(--default-padding) 0;
  font-size: 12px;
  column-gap: var(--default-padding);
  color: var(--color-gray-700);
}

.lh-meta__item {
  display: block;
  list-style-type: none;
  position: relative;
  padding: 0 0 0 calc(var(--meta-icon-size) + var(--default-padding) * 2);
  cursor: unset; /* disable pointer cursor from report-icon */
}

.lh-meta__item.lh-tooltip-boundary {
  text-decoration: dotted underline var(--color-gray-500);
  cursor: help;
}

.lh-meta__item.lh-report-icon::before {
  position: absolute;
  left: var(--default-padding);
  width: var(--meta-icon-size);
  height: var(--meta-icon-size);
}

.lh-meta__item.lh-report-icon:hover::before {
  opacity: 0.7;
}

.lh-meta__item .lh-tooltip {
  color: var(--color-gray-800);
}

.lh-meta__item .lh-tooltip::before {
  right: auto; /* Set the tooltip arrow to the leftside */
  left: 6px;
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 640px) {
  .lh-meta__items {
    grid-template-columns: 1fr 1fr;
  }
}

/* Change the grid for narrow container */
@container lh-container (max-width: 640px) {
  .lh-meta__items {
    grid-template-columns: 1fr 1fr;
  }
}

/**
* This media query is a temporary fallback for browsers that do not support \`@container query\`.
* TODO: remove this media query when \`@container query\` is fully supported by browsers
* See https://github.com/GoogleChrome/lighthouse/pull/16332
*/
@media screen and (max-width: 535px) {
  .lh-meta__items {
    display: block;
  }
}

@container lh-container (max-width: 535px) {
  .lh-meta__items {
    display: block;
  }
}

/* Explodey gauge */

.lh-exp-gauge-component {
  margin-bottom: 10px;
}

.lh-exp-gauge-component circle {
  stroke: currentcolor;
  r: var(--radius);
}

.lh-exp-gauge-component text {
  font-size: calc(var(--radius) * 0.2);
}

.lh-exp-gauge-component .lh-exp-gauge {
  margin: 0 auto;
  width: 225px;
  stroke-width: var(--stroke-width);
  stroke-linecap: round;

  /* for better rendering perf */
  contain: strict;
  height: 225px;
  will-change: transform;
}
.lh-exp-gauge-component .lh-exp-gauge--faded {
  opacity: 0.1;
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper {
  font-family: var(--report-font-family-monospace);
  text-align: center;
  text-decoration: none;
  transition: .3s;
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass {
  color: var(--color-pass);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--average {
  color: var(--color-average);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail {
  color: var(--color-fail);
}
.lh-exp-gauge-component .state--expanded {
  transition: color .3s;
}
.lh-exp-gauge-component .state--highlight {
  color: var(--color-highlight);
}
.lh-exp-gauge-component .lh-exp-gauge__svg-wrapper {
  display: flex;
  flex-direction: column-reverse;
}

.lh-exp-gauge-component .lh-exp-gauge__label {
  fill: var(--report-text-color);
  font-family: var(--report-font-family);
  font-size: 12px;
}

.lh-exp-gauge-component .lh-exp-gauge__cutout {
  opacity: .999;
  transition: opacity .3s;
}
.lh-exp-gauge-component .state--highlight .lh-exp-gauge__cutout {
  opacity: 0;
}

.lh-exp-gauge-component .lh-exp-gauge__inner {
  color: inherit;
}
.lh-exp-gauge-component .lh-exp-gauge__base {
  fill: currentcolor;
}


.lh-exp-gauge-component .lh-exp-gauge__arc {
  fill: none;
  transition: opacity .3s;
}
.lh-exp-gauge-component .lh-exp-gauge__arc--metric {
  color: var(--metric-color);
  stroke-dashoffset: var(--metric-offset);
  opacity: 0.3;
}
.lh-exp-gauge-component .lh-exp-gauge-hovertarget {
  color: currentcolor;
  opacity: 0.001;
  stroke-linecap: butt;
  stroke-width: 24;
  /* hack. move the hover target out of the center. ideally i tweak the r instead but that rquires considerably more math. */
  transform: scale(1.15);
}
.lh-exp-gauge-component .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  opacity: 0;
  stroke-dasharray: 0 calc(var(--circle-meas) * var(--radius));
  transition: 0s .005s;
}
.lh-exp-gauge-component .state--expanded .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  opacity: .999;
  stroke-dasharray: var(--metric-array);
  transition: 0.3s; /*  calc(.005s + var(--i)*.05s); entrace animation */
}
.lh-exp-gauge-component .state--expanded .lh-exp-gauge__inner .lh-exp-gauge__arc {
  opacity: 0;
}


.lh-exp-gauge-component .lh-exp-gauge__percentage {
  text-anchor: middle;
  dominant-baseline: middle;
  opacity: .999;
  font-size: calc(var(--radius) * 0.625);
  transition: opacity .3s ease-in;
}
.lh-exp-gauge-component .state--highlight .lh-exp-gauge__percentage {
  opacity: 0;
}

.lh-exp-gauge-component .lh-exp-gauge__wrapper--fail .lh-exp-gauge__percentage {
  fill: var(--color-fail);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--average .lh-exp-gauge__percentage {
  fill: var(--color-average);
}
.lh-exp-gauge-component .lh-exp-gauge__wrapper--pass .lh-exp-gauge__percentage {
  fill: var(--color-pass);
}

.lh-exp-gauge-component .lh-cover {
  fill: none;
  opacity: .001;
  pointer-events: none;
}
.lh-exp-gauge-component .state--expanded .lh-cover {
  pointer-events: auto;
}

.lh-exp-gauge-component .metric {
  transform: scale(var(--scale-initial));
  opacity: 0;
  transition: transform .1s .2s ease-out,  opacity .3s ease-out;
  pointer-events: none;
}
.lh-exp-gauge-component .metric text {
  pointer-events: none;
}
.lh-exp-gauge-component .metric__value {
  fill: currentcolor;
  opacity: 0;
  transition: opacity 0.2s;
}
.lh-exp-gauge-component .state--expanded .metric {
  transform: scale(1);
  opacity: .999;
  transition: transform .3s ease-out,  opacity .3s ease-in,  stroke-width .1s ease-out;
  transition-delay: calc(var(--i)*.05s);
  pointer-events: auto;
}
.lh-exp-gauge-component .state--highlight .metric {
  opacity: .3;
}
.lh-exp-gauge-component .state--highlight .metric--highlight {
  opacity: .999;
  stroke-width: calc(1.5*var(--stroke-width));
}
.lh-exp-gauge-component .state--highlight .metric--highlight .metric__value {
  opacity: 0.999;
}


/*
 the initial first load peek
*/
.lh-exp-gauge-component .lh-exp-gauge__bg {  /* needed for the use zindex stacking w/ transparency */
  fill: var(--report-background-color);
  stroke: var(--report-background-color);
}
.lh-exp-gauge-component .state--peek .metric {
  transition-delay: 0ms;
  animation: peek var(--peek-dur) cubic-bezier(0.46, 0.03, 0.52, 0.96);
  animation-fill-mode: forwards;
}
.lh-exp-gauge-component .state--peek .lh-exp-gauge__inner .lh-exp-gauge__arc {
  opacity: 1;
}
.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc.lh-exp-gauge--faded {
  opacity: 0.3; /* just a tad stronger cuz its fighting with a big solid arg */
}
/* do i need to set expanded and override this? */
.lh-exp-gauge-component .state--peek .lh-exp-gauge__arc--metric.lh-exp-gauge--miniarc {
  transition: opacity 0.3s;
}
.lh-exp-gauge-component .state--peek {
  color: unset;
}
.lh-exp-gauge-component .state--peek .metric__label {
  display: none;
}

.lh-exp-gauge-component .metric__label {
  fill: var(--report-text-color);
}

@keyframes peek {
  /* biggest it should go is 0.92. smallest is 0.8 */
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }

  50% {
    transform: scale(0.92);
    opacity: 1;
  }

  100% {
    transform: scale(0.8);
    opacity: 0.8;
  }
}

.lh-exp-gauge-component .wrapper {
  width: 620px;
}

/*# sourceURL=report-styles.css */
`),e.append(t),e}function pt(o){let e=o.createFragment(),t=o.createElement("style");t.append(`
    .lh-topbar {
      position: sticky;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
      display: flex;
      align-items: center;
      height: var(--topbar-height);
      padding: var(--topbar-padding);
      font-size: var(--report-font-size-secondary);
      background-color: var(--topbar-background-color);
      border-bottom: 1px solid var(--color-gray-200);
    }

    .lh-topbar__logo {
      width: var(--topbar-logo-size);
      height: var(--topbar-logo-size);
      user-select: none;
      flex: none;
    }

    .lh-topbar__url {
      margin: var(--topbar-padding);
      text-decoration: none;
      color: var(--report-text-color);
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .lh-tools {
      display: flex;
      align-items: center;
      margin-left: auto;
      will-change: transform;
      min-width: var(--report-icon-size);
    }
    .lh-tools__button {
      width: var(--report-icon-size);
      min-width: 24px;
      height: var(--report-icon-size);
      cursor: pointer;
      margin-right: 5px;
      /* This is actually a button element, but we want to style it like a transparent div. */
      display: flex;
      background: none;
      color: inherit;
      border: none;
      padding: 0;
      font: inherit;
      outline: inherit;
    }
    .lh-tools__button svg {
      fill: var(--tools-icon-color);
    }
    .lh-dark .lh-tools__button svg {
      filter: invert(1);
    }
    .lh-tools__button.lh-active + .lh-tools__dropdown {
      opacity: 1;
      clip: rect(-1px, 194px, 270px, -3px);
      visibility: visible;
    }
    .lh-tools__dropdown {
      position: absolute;
      background-color: var(--report-background-color);
      border: 1px solid var(--report-border-color);
      border-radius: 3px;
      padding: calc(var(--default-padding) / 2) 0;
      cursor: pointer;
      top: 36px;
      right: 0;
      box-shadow: 1px 1px 3px #ccc;
      min-width: 125px;
      clip: rect(0, 164px, 0, 0);
      visibility: hidden;
      opacity: 0;
      transition: all 200ms cubic-bezier(0,0,0.2,1);
    }
    .lh-tools__dropdown a {
      color: currentColor;
      text-decoration: none;
      white-space: nowrap;
      padding: 0 6px;
      line-height: 2;
    }
    .lh-tools__dropdown a:hover,
    .lh-tools__dropdown a:focus {
      background-color: var(--color-gray-200);
      outline: none;
    }
    /* save-gist option hidden in report. */
    .lh-tools__dropdown a[data-action='save-gist'] {
      display: none;
    }

    .lh-locale-selector {
      width: 100%;
      color: var(--report-text-color);
      background-color: var(--locale-selector-background-color);
      padding: 2px;
    }
    .lh-tools-locale {
      display: flex;
      align-items: center;
      flex-direction: row-reverse;
    }
    .lh-tools-locale__selector-wrapper {
      transition: opacity 0.15s;
      opacity: 0;
      max-width: 200px;
    }
    .lh-button.lh-tool-locale__button {
      height: var(--topbar-height);
      color: var(--tools-icon-color);
      padding: calc(var(--default-padding) / 2);
    }
    .lh-tool-locale__button.lh-active + .lh-tools-locale__selector-wrapper {
      opacity: 1;
      clip: rect(-1px, 194px, 242px, -3px);
      visibility: visible;
      margin: 0 4px;
    }

    /**
    * This media query is a temporary fallback for browsers that do not support \`@container query\`.
    * TODO: remove this media query when \`@container query\` is fully supported by browsers
    * See https://github.com/GoogleChrome/lighthouse/pull/16332
    */
    @media screen and (max-width: 964px) {
      .lh-tools__dropdown {
        right: 0;
        left: initial;
      }
    }

    @container lh-container (max-width: 964px) {
      .lh-tools__dropdown {
        right: 0;
        left: initial;
      }
    }

    @media print {
      .lh-topbar {
        position: static;
        margin-left: 0;
      }

      .lh-tools__dropdown {
        display: none;
      }
    }
  `),e.append(t);let n=o.createElement("div","lh-topbar"),r=o.createElementNS("http://www.w3.org/2000/svg","svg","lh-topbar__logo");r.setAttribute("role","img"),r.setAttribute("title","Lighthouse logo"),r.setAttribute("fill","none"),r.setAttribute("xmlns","http://www.w3.org/2000/svg"),r.setAttribute("viewBox","0 0 48 48");let i=o.createElementNS("http://www.w3.org/2000/svg","path");i.setAttribute("d","m14 7 10-7 10 7v10h5v7h-5l5 24H9l5-24H9v-7h5V7Z"),i.setAttribute("fill","#F63");let a=o.createElementNS("http://www.w3.org/2000/svg","path");a.setAttribute("d","M31.561 24H14l-1.689 8.105L31.561 24ZM18.983 48H9l1.022-4.907L35.723 32.27l1.663 7.98L18.983 48Z"),a.setAttribute("fill","#FFA385");let l=o.createElementNS("http://www.w3.org/2000/svg","path");l.setAttribute("fill","#FF3"),l.setAttribute("d","M20.5 10h7v7h-7z"),r.append(" ",i," ",a," ",l," ");let s=o.createElement("a","lh-topbar__url");s.setAttribute("href",""),s.setAttribute("target","_blank"),s.setAttribute("rel","noopener");let c=o.createElement("div","lh-tools"),d=o.createElement("div","lh-tools-locale lh-hidden"),p=o.createElement("button","lh-button lh-tool-locale__button");p.setAttribute("id","lh-button__swap-locales"),p.setAttribute("title","Show Language Picker"),p.setAttribute("aria-label","Toggle language picker"),p.setAttribute("aria-haspopup","menu"),p.setAttribute("aria-expanded","false"),p.setAttribute("aria-controls","lh-tools-locale__selector-wrapper");let h=o.createElementNS("http://www.w3.org/2000/svg","svg");h.setAttribute("width","20px"),h.setAttribute("height","20px"),h.setAttribute("viewBox","0 0 24 24"),h.setAttribute("fill","currentColor");let u=o.createElementNS("http://www.w3.org/2000/svg","path");u.setAttribute("d","M0 0h24v24H0V0z"),u.setAttribute("fill","none");let v=o.createElementNS("http://www.w3.org/2000/svg","path");v.setAttribute("d","M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"),h.append(u,v),p.append(" ",h," ");let _=o.createElement("div","lh-tools-locale__selector-wrapper");_.setAttribute("id","lh-tools-locale__selector-wrapper"),_.setAttribute("role","menu"),_.setAttribute("aria-labelledby","lh-button__swap-locales"),_.setAttribute("aria-hidden","true"),_.append(" "," "),d.append(" ",p," ",_," ");let f=o.createElement("button","lh-tools__button");f.setAttribute("id","lh-tools-button"),f.setAttribute("title","Tools menu"),f.setAttribute("aria-label","Toggle report tools menu"),f.setAttribute("aria-haspopup","menu"),f.setAttribute("aria-expanded","false"),f.setAttribute("aria-controls","lh-tools-dropdown");let g=o.createElementNS("http://www.w3.org/2000/svg","svg");g.setAttribute("width","100%"),g.setAttribute("height","100%"),g.setAttribute("viewBox","0 0 24 24");let b=o.createElementNS("http://www.w3.org/2000/svg","path");b.setAttribute("d","M0 0h24v24H0z"),b.setAttribute("fill","none");let w=o.createElementNS("http://www.w3.org/2000/svg","path");w.setAttribute("d","M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"),g.append(" ",b," ",w," "),f.append(" ",g," ");let x=o.createElement("div","lh-tools__dropdown");x.setAttribute("id","lh-tools-dropdown"),x.setAttribute("role","menu"),x.setAttribute("aria-labelledby","lh-tools-button");let S=o.createElement("a","lh-report-icon lh-report-icon--print");S.setAttribute("role","menuitem"),S.setAttribute("tabindex","-1"),S.setAttribute("href","#"),S.setAttribute("data-i18n","dropdownPrintSummary"),S.setAttribute("data-action","print-summary");let A=o.createElement("a","lh-report-icon lh-report-icon--print");A.setAttribute("role","menuitem"),A.setAttribute("tabindex","-1"),A.setAttribute("href","#"),A.setAttribute("data-i18n","dropdownPrintExpanded"),A.setAttribute("data-action","print-expanded");let L=o.createElement("a","lh-report-icon lh-report-icon--copy");L.setAttribute("role","menuitem"),L.setAttribute("tabindex","-1"),L.setAttribute("href","#"),L.setAttribute("data-i18n","dropdownCopyJSON"),L.setAttribute("data-action","copy");let T=o.createElement("a","lh-report-icon lh-report-icon--download lh-hidden");T.setAttribute("role","menuitem"),T.setAttribute("tabindex","-1"),T.setAttribute("href","#"),T.setAttribute("data-i18n","dropdownSaveHTML"),T.setAttribute("data-action","save-html");let F=o.createElement("a","lh-report-icon lh-report-icon--download");F.setAttribute("role","menuitem"),F.setAttribute("tabindex","-1"),F.setAttribute("href","#"),F.setAttribute("data-i18n","dropdownSaveJSON"),F.setAttribute("data-action","save-json");let N=o.createElement("a","lh-report-icon lh-report-icon--open");N.setAttribute("role","menuitem"),N.setAttribute("tabindex","-1"),N.setAttribute("href","#"),N.setAttribute("data-i18n","dropdownViewer"),N.setAttribute("data-action","open-viewer");let I=o.createElement("a","lh-report-icon lh-report-icon--open");I.setAttribute("role","menuitem"),I.setAttribute("tabindex","-1"),I.setAttribute("href","#"),I.setAttribute("data-i18n","dropdownSaveGist"),I.setAttribute("data-action","save-gist");let P=o.createElement("a","lh-report-icon lh-report-icon--open lh-hidden");P.setAttribute("role","menuitem"),P.setAttribute("tabindex","-1"),P.setAttribute("href","#"),P.setAttribute("data-i18n","dropdownViewUnthrottledTrace"),P.setAttribute("data-action","view-unthrottled-trace");let H=o.createElement("a","lh-report-icon lh-report-icon--dark");H.setAttribute("role","menuitem"),H.setAttribute("tabindex","-1"),H.setAttribute("href","#"),H.setAttribute("data-i18n","dropdownDarkTheme"),H.setAttribute("data-action","toggle-dark");let V=o.createElement("a","lh-report-icon lh-report-icon--experiment");return V.setAttribute("role","menuitem"),V.setAttribute("tabindex","-1"),V.setAttribute("href","#"),V.setAttribute("data-i18n","dropdownInsightsToggle"),V.setAttribute("data-action","toggle-insights"),x.append(" ",S," ",A," ",L," "," ",T," ",F," ",N," ",I," "," ",P," ",H," ",V," "),c.append(" ",d," ",f," ",x," "),n.append(" "," ",r," ",s," ",c," "),e.append(n),e}function ht(o){let e=o.createFragment(),t=o.createElement("div","lh-warnings lh-warnings--toplevel"),n=o.createElement("p","lh-warnings__msg"),r=o.createElement("ul");return t.append(" ",n," ",r," "),e.append(t),e}function we(o,e){switch(e){case"3pFilter":return Be(o);case"audit":return qe(o);case"categoryHeader":return je(o);case"chevron":return We(o);case"clump":return Ke(o);case"crc":return Je(o);case"crcChain":return Ze(o);case"elementScreenshot":return Qe(o);case"explodeyGauge":return Ye(o);case"footer":return Xe(o);case"fraction":return et(o);case"gauge":return tt(o);case"heading":return nt(o);case"metric":return rt(o);case"scorescale":return ot(o);case"scoresWrapper":return it(o);case"snippet":return at(o);case"snippetContent":return lt(o);case"snippetHeader":return st(o);case"snippetLine":return ct(o);case"styles":return dt(o);case"topbar":return pt(o);case"warningsToplevel":return ht(o)}throw new Error("unexpected component: "+e)}var te=class{constructor(e,t){this._document=e,this._lighthouseChannel="unknown",this._componentCache=new Map,this.rootEl=t,this._swappableSections=new WeakMap}createElement(e,t){let n=this._document.createElement(e);if(t)for(let r of t.split(/\s+/))r&&n.classList.add(r);return n}createElementNS(e,t,n){let r=this._document.createElementNS(e,t);if(n)for(let i of n.split(/\s+/))i&&r.classList.add(i);return r}createSVGElement(e,t){return this._document.createElementNS("http://www.w3.org/2000/svg",e,t)}createFragment(){return this._document.createDocumentFragment()}createTextNode(e){return this._document.createTextNode(e)}createChildOf(e,t,n){let r=this.createElement(t,n);return e.append(r),r}createComponent(e){let t=this._componentCache.get(e);if(t){let r=t.cloneNode(!0);return this.findAll("style",r).forEach(i=>i.remove()),r}return t=we(this,e),this._componentCache.set(e,t),t.cloneNode(!0)}clearComponentCache(){this._componentCache.clear()}convertMarkdownLinkSnippets(e,t={}){let n=this.createElement("span");for(let r of E.splitMarkdownLink(e)){let i=r.text.includes("`")?this.convertMarkdownCodeSnippets(r.text):r.text;if(!r.isLink){n.append(i);continue}let a=new URL(r.linkHref);(["https://developers.google.com","https://web.dev","https://developer.chrome.com"].includes(a.origin)||t.alwaysAppendUtmSource)&&(a.searchParams.set("utm_source","lighthouse"),a.searchParams.set("utm_medium",this._lighthouseChannel));let s=this.createElement("a");s.rel="noopener",s.target="_blank",s.append(i),this.safelySetHref(s,a.href),n.append(s)}return n}safelySetHref(e,t){if(t=t||"",t.startsWith("#")){e.href=t;return}let n=["https:","http:"],r;try{r=new URL(t)}catch{}r&&n.includes(r.protocol)&&(e.href=r.href)}safelySetBlobHref(e,t){if(t.type!=="text/html"&&t.type!=="application/json")throw new Error("Unsupported blob type");let n=URL.createObjectURL(t);e.href=n}convertMarkdownCodeSnippets(e){let t=this.createElement("span");for(let n of E.splitMarkdownCodeSpans(e))if(n.isCode){let r=this.createElement("code");r.textContent=n.text,t.append(r)}else t.append(this._document.createTextNode(n.text));return t}setLighthouseChannel(e){this._lighthouseChannel=e}document(){return this._document}isDevTools(){return!!this._document.querySelector(".lh-devtools")}find(e,t=this.rootEl??this._document){let n=this.maybeFind(e,t);if(n===null)throw new Error(`query ${e} not found`);return n}maybeFind(e,t=this.rootEl??this._document){return t.querySelector(e)}findAll(e,t){return Array.from(t.querySelectorAll(e))}fireEventOn(e,t=this._document,n){let r=new CustomEvent(e,n?{detail:n}:void 0);t.dispatchEvent(r)}saveFile(e,t){let n=this.createElement("a");n.download=t,this.safelySetBlobHref(n,e),this._document.body.append(n),n.click(),this._document.body.removeChild(n),setTimeout(()=>URL.revokeObjectURL(n.href),500)}registerSwappableSections(e,t){this._swappableSections.set(e,t),this._swappableSections.set(t,e)}swapSectionIfPossible(e){let t=this._swappableSections.get(e);if(!t)return;let n=e.parentNode;n&&(n.insertBefore(t,e),e.remove())}};var ye=0,m=class o{static i18n=null;static strings={};static reportJson=null;static apply(e){o.strings={...xe,...e.providedStrings},o.i18n=e.i18n,o.reportJson=e.reportJson}static getUniqueSuffix(){return ye++}static resetUniqueSuffix(){ye=0}};var ke="data:image/jpeg;base64,";function Ee(o){o.configSettings.locale||(o.configSettings.locale="en"),o.configSettings.formFactor||(o.configSettings.formFactor=o.configSettings.emulatedFormFactor),o.finalDisplayedUrl=E.getFinalDisplayedUrl(o),o.mainDocumentUrl=E.getMainDocumentUrl(o);for(let n of Object.values(o.audits))if((n.scoreDisplayMode==="not_applicable"||n.scoreDisplayMode==="not-applicable")&&(n.scoreDisplayMode="notApplicable"),n.scoreDisplayMode==="informative"&&(n.score=1),n.details){if((n.details.type===void 0||n.details.type==="diagnostic")&&(n.details.type="debugdata"),n.details.type==="filmstrip")for(let r of n.details.items)r.data.startsWith(ke)||(r.data=ke+r.data);if(n.details.type==="table")for(let r of n.details.headings){let{itemType:i,text:a}=r;i!==void 0&&(r.valueType=i,delete r.itemType),a!==void 0&&(r.label=a,delete r.text);let l=r.subItemsHeading?.itemType;r.subItemsHeading&&l!==void 0&&(r.subItemsHeading.valueType=l,delete r.subItemsHeading.itemType)}if(n.id==="third-party-summary"&&(n.details.type==="opportunity"||n.details.type==="table")){let{headings:r,items:i}=n.details;if(r[0].valueType==="link"){r[0].valueType="text";for(let a of i)typeof a.entity=="object"&&a.entity.type==="link"&&(a.entity=a.entity.text);n.details.isEntityGrouped=!0}}}let[e]=o.lighthouseVersion.split(".").map(Number),t=o.categories.performance;if(t){if(e<9){o.categoryGroups||(o.categoryGroups={}),o.categoryGroups.hidden={title:""};for(let n of t.auditRefs)n.group?n.group==="load-opportunities"&&(n.group="diagnostics"):n.group="hidden"}else if(e<12)for(let n of t.auditRefs)n.group||(n.group="diagnostics")}if(e<12&&t){let n=new Map;for(let r of t.auditRefs){let i=r.relevantAudits;if(!(!i||!r.acronym))for(let a of i){let l=n.get(a)||[];l.push(r.acronym),n.set(a,l)}}for(let[r,i]of n){if(!i.length)continue;let a=o.audits[r];if(a&&!a.metricSavings){a.metricSavings={};for(let l of i)a.metricSavings[l]=0}}}if(o.environment||(o.environment={benchmarkIndex:0,networkUserAgent:o.userAgent,hostUserAgent:o.userAgent}),o.configSettings.screenEmulation||(o.configSettings.screenEmulation={width:-1,height:-1,deviceScaleFactor:-1,mobile:/mobile/i.test(o.environment.hostUserAgent),disabled:!1}),o.i18n||(o.i18n={}),o.audits["full-page-screenshot"]){let n=o.audits["full-page-screenshot"].details;n?o.fullPageScreenshot={screenshot:n.screenshot,nodes:n.nodes}:o.fullPageScreenshot=null,delete o.audits["full-page-screenshot"]}}var $=E.RATINGS,k=class o{static prepareReportResult(e){let t=JSON.parse(JSON.stringify(e));Ee(t);for(let r of Object.values(t.audits))r.details&&(r.details.type==="opportunity"||r.details.type==="table")&&!r.details.isEntityGrouped&&t.entities&&o.classifyEntities(t.entities,r.details);if(typeof t.categories!="object")throw new Error("No categories provided.");let n=new Map;for(let r of Object.values(t.categories))r.auditRefs.forEach(i=>{i.acronym&&n.set(i.acronym,i)}),r.auditRefs.forEach(i=>{let a=t.audits[i.id];i.result=a;let l=Object.keys(i.result.metricSavings||{});if(l.length){i.relevantMetrics=[];for(let s of l){let c=n.get(s);c&&i.relevantMetrics.push(c)}}t.stackPacks&&t.stackPacks.forEach(s=>{s.descriptions[i.id]&&(i.stackPacks=i.stackPacks||[],i.stackPacks.push({title:s.title,iconDataURL:s.iconDataURL,description:s.descriptions[i.id]}))})});return t}static getUrlLocatorFn(e){let t=e.find(r=>r.valueType==="url")?.key;if(t&&typeof t=="string")return r=>{let i=r[t];if(typeof i=="string")return i};let n=e.find(r=>r.valueType==="source-location")?.key;if(n)return r=>{let i=r[n];if(typeof i=="object"&&i.type==="source-location")return i.url}}static classifyEntities(e,t){let{items:n,headings:r}=t;if(!n.length||n.some(a=>a.entity))return;let i=o.getUrlLocatorFn(r);if(i)for(let a of n){let l=i(a);if(!l)continue;let s="";try{s=E.parseURL(l).origin}catch{}if(!s)continue;let c=e.find(d=>d.origins.includes(s));c&&(a.entity=c.name)}}static getTableItemSortComparator(e){return(t,n)=>{for(let r of e){let i=t[r],a=n[r];if((typeof i!=typeof a||!["number","string"].includes(typeof i))&&console.warn(`Warning: Attempting to sort unsupported value type: ${r}.`),typeof i=="number"&&typeof a=="number"&&i!==a)return a-i;if(typeof i=="string"&&typeof a=="string"&&i!==a)return i.localeCompare(a)}return 0}}static getEmulationDescriptions(e){let t,n,r,i=e.throttling,a=m.i18n,l=m.strings;switch(e.throttlingMethod){case"provided":r=n=t=l.throttlingProvided;break;case"devtools":{let{cpuSlowdownMultiplier:h,requestLatencyMs:u}=i;t=`${a.formatNumber(h)}x slowdown (DevTools)`,n=`${a.formatMilliseconds(u)} HTTP RTT, ${a.formatKbps(i.downloadThroughputKbps)} down, ${a.formatKbps(i.uploadThroughputKbps)} up (DevTools)`,r=u===150*3.75&&i.downloadThroughputKbps===1.6*1024*.9&&i.uploadThroughputKbps===750*.9?l.runtimeSlow4g:l.runtimeCustom;break}case"simulate":{let{cpuSlowdownMultiplier:h,rttMs:u,throughputKbps:v}=i;t=`${a.formatNumber(h)}x slowdown (Simulated)`,n=`${a.formatMilliseconds(u)} TCP RTT, ${a.formatKbps(v)} throughput (Simulated)`,r=u===150&&v===1.6*1024?l.runtimeSlow4g:l.runtimeCustom;break}default:r=t=n=l.runtimeUnknown}let s=e.channel==="devtools"?!1:e.screenEmulation.disabled,c=e.channel==="devtools"?e.formFactor==="mobile":e.screenEmulation.mobile,d=l.runtimeMobileEmulation;s?d=l.runtimeNoEmulation:c||(d=l.runtimeDesktopEmulation);let p=s?void 0:`${e.screenEmulation.width}x${e.screenEmulation.height}, DPR ${e.screenEmulation.deviceScaleFactor}`;return{deviceEmulation:d,screenEmulation:p,cpuThrottling:t,networkThrottling:n,summary:r}}static showAsPassed(e){switch(e.scoreDisplayMode){case"manual":case"notApplicable":return!0;case"error":case"informative":return!1;case"numeric":case"binary":default:return Number(e.score)>=$.PASS.minScore}}static calculateRating(e,t){if(t==="manual"||t==="notApplicable")return $.PASS.label;if(t==="error")return $.ERROR.label;if(e===null)return $.FAIL.label;let n=$.FAIL.label;return e>=$.PASS.minScore?n=$.PASS.label:e>=$.AVERAGE.minScore&&(n=$.AVERAGE.label),n}static calculateCategoryFraction(e){let t=0,n=0,r=0,i=0;for(let a of e.auditRefs){let l=o.showAsPassed(a.result);if(!(a.group==="hidden"||a.result.scoreDisplayMode==="manual"||a.result.scoreDisplayMode==="notApplicable")){if(a.result.scoreDisplayMode==="informative"){l||++r;continue}++t,i+=a.weight,l&&n++}}return{numPassed:n,numPassableAudits:t,numInformative:r,totalWeight:i}}static isPluginCategory(e){return e.startsWith("lighthouse-plugin-")}static shouldDisplayAsFraction(e){return e==="timespan"||e==="snapshot"}},xe={varianceDisclaimer:"Values are estimated and may vary. The [performance score is calculated](https://developer.chrome.com/docs/lighthouse/performance/performance-scoring/) directly from these metrics.",calculatorLink:"See calculator.",showRelevantAudits:"Show audits relevant to:",opportunityResourceColumnLabel:"Opportunity",opportunitySavingsColumnLabel:"Estimated Savings",errorMissingAuditInfo:"Report error: no audit information",errorLabel:"Error!",warningHeader:"Warnings: ",warningAuditsGroupTitle:"Passed audits but with warnings",passedAuditsGroupTitle:"Passed audits",notApplicableAuditsGroupTitle:"Not applicable",manualAuditsGroupTitle:"Additional items to manually check",toplevelWarningsMessage:"There were issues affecting this run of Lighthouse:",crcInitialNavigation:"Initial Navigation",crcLongestDurationLabel:"Maximum critical path latency:",snippetExpandButtonLabel:"Expand snippet",snippetCollapseButtonLabel:"Collapse snippet",lsPerformanceCategoryDescription:"[Lighthouse](https://developers.google.com/web/tools/lighthouse/) analysis of the current page on an emulated mobile network. Values are estimated and may vary.",labDataTitle:"Lab Data",thirdPartyResourcesLabel:"Show 3rd-party resources",viewTreemapLabel:"View Treemap",viewTraceLabel:"View Trace",dropdownPrintSummary:"Print Summary",dropdownPrintExpanded:"Print Expanded",dropdownCopyJSON:"Copy JSON",dropdownSaveHTML:"Save as HTML",dropdownSaveJSON:"Save as JSON",dropdownViewer:"Open in Viewer",dropdownSaveGist:"Save as Gist",dropdownDarkTheme:"Toggle Dark Theme",dropdownInsightsToggle:"Toggle experimental insights",dropdownViewUnthrottledTrace:"View Unthrottled Trace",runtimeSettingsDevice:"Device",runtimeSettingsNetworkThrottling:"Network throttling",runtimeSettingsCPUThrottling:"CPU throttling",runtimeSettingsUANetwork:"User agent (network)",runtimeSettingsBenchmark:"Unthrottled CPU/Memory Power",runtimeSettingsAxeVersion:"Axe version",runtimeSettingsScreenEmulation:"Screen emulation",footerIssue:"File an issue",runtimeNoEmulation:"No emulation",runtimeMobileEmulation:"Emulated Moto G Power",runtimeDesktopEmulation:"Emulated Desktop",runtimeUnknown:"Unknown",runtimeSingleLoad:"Single page session",runtimeAnalysisWindow:"Initial page load",runtimeAnalysisWindowTimespan:"User interactions timespan",runtimeAnalysisWindowSnapshot:"Point-in-time snapshot",runtimeSingleLoadTooltip:"This data is taken from a single page session, as opposed to field data summarizing many sessions.",throttlingProvided:"Provided by environment",show:"Show",hide:"Hide",expandView:"Expand view",collapseView:"Collapse view",runtimeSlow4g:"Slow 4G throttling",runtimeCustom:"Custom throttling",firstPartyChipLabel:"1st party",openInANewTabTooltip:"Open in a new tab",unattributable:"Unattributable"};var B=class{constructor(e,t){this.dom=e,this.detailsRenderer=t}get _clumpTitles(){return{warning:m.strings.warningAuditsGroupTitle,manual:m.strings.manualAuditsGroupTitle,passed:m.strings.passedAuditsGroupTitle,notApplicable:m.strings.notApplicableAuditsGroupTitle}}renderAudit(e){let t=m.strings,n=this.dom.createComponent("audit"),r=this.dom.find("div.lh-audit",n);r.id=e.result.id;let i=e.result.scoreDisplayMode;e.result.displayValue&&(this.dom.find(".lh-audit__display-text",r).textContent=e.result.displayValue);let a=this.dom.find(".lh-audit__title",r);a.append(this.dom.convertMarkdownCodeSnippets(e.result.title));let l=this.dom.find(".lh-audit__description",r);l.append(this.dom.convertMarkdownLinkSnippets(e.result.description));for(let h of e.relevantMetrics||[]){let u=this.dom.createChildOf(l,"span","lh-audit__adorn");u.title=`Relevant to ${h.result.title}`,u.textContent=h.acronym||h.id}e.stackPacks&&e.stackPacks.forEach(h=>{let u=this.dom.createElement("img","lh-audit__stackpack__img");u.src=h.iconDataURL,u.alt=h.title;let v=this.dom.convertMarkdownLinkSnippets(h.description,{alwaysAppendUtmSource:!0}),_=this.dom.createElement("div","lh-audit__stackpack");_.append(u,v),this.dom.find(".lh-audit__stackpacks",r).append(_)});let s=this.dom.find("details",r);if(e.result.details){let h=this.detailsRenderer.render(e.result.details);h&&(h.classList.add("lh-details"),s.append(h))}if(this.dom.find(".lh-chevron-container",r).append(this._createChevron()),this._setRatingClass(r,e.result.score,i),e.result.scoreDisplayMode==="error"){r.classList.add("lh-audit--error");let h=this.dom.find(".lh-audit__display-text",r);h.textContent=t.errorLabel,h.classList.add("lh-tooltip-boundary");let u=this.dom.createChildOf(h,"div","lh-tooltip lh-tooltip--error");u.textContent=e.result.errorMessage||t.errorMissingAuditInfo}else if(e.result.explanation){let h=this.dom.createChildOf(a,"div","lh-audit-explanation");h.textContent=e.result.explanation}let c=e.result.warnings;if(!c||c.length===0)return r;let d=this.dom.find("summary",s),p=this.dom.createChildOf(d,"div","lh-warnings");if(this.dom.createChildOf(p,"span").textContent=t.warningHeader,c.length===1)p.append(this.dom.createTextNode(c.join("")));else{let h=this.dom.createChildOf(p,"ul");for(let u of c){let v=this.dom.createChildOf(h,"li");v.textContent=u}}return r}injectFinalScreenshot(e,t,n){let r=t["final-screenshot"];if(!r||r.scoreDisplayMode==="error"||!r.details||r.details.type!=="screenshot")return null;let i=this.dom.createElement("img","lh-final-ss-image"),a=r.details.data;i.src=a,i.alt=r.title;let l=this.dom.find(".lh-category .lh-category-header",e),s=this.dom.createElement("div","lh-category-headercol"),c=this.dom.createElement("div","lh-category-headercol lh-category-headercol--separator"),d=this.dom.createElement("div","lh-category-headercol");s.append(...l.childNodes),s.append(n),d.append(i),l.append(s,c,d),l.classList.add("lh-category-header__finalscreenshot")}_createChevron(){let e=this.dom.createComponent("chevron");return this.dom.find("svg.lh-chevron",e)}_setRatingClass(e,t,n){let r=k.calculateRating(t,n);return e.classList.add(`lh-audit--${n.toLowerCase()}`),n!=="informative"&&e.classList.add(`lh-audit--${r}`),e}renderCategoryHeader(e,t,n){let r=this.dom.createComponent("categoryHeader"),i=this.dom.find(".lh-score__gauge",r),a=this.renderCategoryScore(e,t,n);if(i.append(a),e.description){let l=this.dom.convertMarkdownLinkSnippets(e.description);this.dom.find(".lh-category-header__description",r).append(l)}return r}renderAuditGroup(e){let t=this.dom.createElement("div","lh-audit-group"),n=this.dom.createElement("div","lh-audit-group__header");this.dom.createChildOf(n,"span","lh-audit-group__title").textContent=e.title,t.append(n);let r=null;return e.description&&(r=this.dom.convertMarkdownLinkSnippets(e.description),r.classList.add("lh-audit-group__description","lh-audit-group__footer"),t.append(r)),[t,r]}_renderGroupedAudits(e,t){let n=new Map,r="NotAGroup";n.set(r,[]);for(let a of e){let l=a.group||r,s=n.get(l)||[];s.push(a),n.set(l,s)}let i=[];for(let[a,l]of n){if(a===r){for(let p of l)i.push(this.renderAudit(p));continue}let s=t[a],[c,d]=this.renderAuditGroup(s);for(let p of l)c.insertBefore(this.renderAudit(p),d);c.classList.add(`lh-audit-group--${a}`),i.push(c)}return i}renderUnexpandableClump(e,t){let n=this.dom.createElement("div");return this._renderGroupedAudits(e,t).forEach(i=>n.append(i)),n}renderClump(e,{auditRefsOrEls:t,description:n,openByDefault:r}){let i=this.dom.createComponent("clump"),a=this.dom.find(".lh-clump",i);r&&a.setAttribute("open","");let l=this.dom.find(".lh-audit-group__header",a),s=this._clumpTitles[e];this.dom.find(".lh-audit-group__title",l).textContent=s;let c=this.dom.find(".lh-audit-group__itemcount",a);c.textContent=`(${t.length})`;let d=t.map(h=>h instanceof HTMLElement?h:this.renderAudit(h));a.append(...d);let p=this.dom.find(".lh-audit-group",i);if(n){let h=this.dom.convertMarkdownLinkSnippets(n);h.classList.add("lh-audit-group__description","lh-audit-group__footer"),p.append(h)}return this.dom.find(".lh-clump-toggletext--show",p).textContent=m.strings.show,this.dom.find(".lh-clump-toggletext--hide",p).textContent=m.strings.hide,a.classList.add(`lh-clump--${e.toLowerCase()}`),p}renderCategoryScore(e,t,n){let r;if(n&&k.shouldDisplayAsFraction(n.gatherMode)?r=this.renderCategoryFraction(e):r=this.renderScoreGauge(e,t),n?.omitLabel&&this.dom.find(".lh-gauge__label,.lh-fraction__label",r).remove(),n?.onPageAnchorRendered){let i=this.dom.find("a",r);n.onPageAnchorRendered(i)}return r}renderScoreGauge(e,t){let n=this.dom.createComponent("gauge"),r=this.dom.find("a.lh-gauge__wrapper",n);k.isPluginCategory(e.id)&&r.classList.add("lh-gauge__wrapper--plugin");let i=Number(e.score),a=this.dom.find(".lh-gauge",n),l=this.dom.find("circle.lh-gauge-arc",a);l&&this._setGaugeArc(l,i);let s=Math.round(i*100),c=this.dom.find("div.lh-gauge__percentage",n);return c.textContent=s.toString(),e.score===null&&(c.classList.add("lh-gauge--error"),c.textContent="",c.title=m.strings.errorLabel),e.auditRefs.length===0||this.hasApplicableAudits(e)?r.classList.add(`lh-gauge__wrapper--${k.calculateRating(e.score)}`):(r.classList.add("lh-gauge__wrapper--not-applicable"),c.textContent="-",c.title=m.strings.notApplicableAuditsGroupTitle),this.dom.find(".lh-gauge__label",n).textContent=e.title,n}renderCategoryFraction(e){let t=this.dom.createComponent("fraction"),n=this.dom.find("a.lh-fraction__wrapper",t),{numPassed:r,numPassableAudits:i,totalWeight:a}=k.calculateCategoryFraction(e),l=r/i,s=this.dom.find(".lh-fraction__content",t),c=this.dom.createElement("span");c.textContent=`${r}/${i}`,s.append(c);let d=k.calculateRating(l);return a===0&&(d="null"),n.classList.add(`lh-fraction__wrapper--${d}`),this.dom.find(".lh-fraction__label",t).textContent=e.title,t}hasApplicableAudits(e){return e.auditRefs.some(t=>t.result.scoreDisplayMode!=="notApplicable")}_setGaugeArc(e,t){let n=2*Math.PI*Number(e.getAttribute("r")),r=Number(e.getAttribute("stroke-width")),i=.25*r/n;e.style.transform=`rotate(${-90+i*360}deg)`;let a=t*n-r/2;t===0&&(e.style.opacity="0"),t===1&&(a=n),e.style.strokeDasharray=`${Math.max(a,0)} ${n}`}_auditHasWarning(e){return!!e.result.warnings?.length}_getClumpIdForAuditRef(e){let t=e.result.scoreDisplayMode;return t==="manual"||t==="notApplicable"?t:k.showAsPassed(e.result)?this._auditHasWarning(e)?"warning":"passed":"failed"}render(e,t={},n){let r=this.dom.createElement("div","lh-category");r.id=e.id,r.append(this.renderCategoryHeader(e,t,n));let i=new Map;i.set("failed",[]),i.set("warning",[]),i.set("manual",[]),i.set("passed",[]),i.set("notApplicable",[]);for(let l of e.auditRefs){if(l.group==="hidden")continue;let s=this._getClumpIdForAuditRef(l),c=i.get(s);c.push(l),i.set(s,c)}for(let l of i.values())l.sort((s,c)=>c.weight-s.weight);let a=i.get("failed")?.length;for(let[l,s]of i){if(s.length===0)continue;if(l==="failed"){let h=this.renderUnexpandableClump(s,t);h.classList.add("lh-clump--failed"),r.append(h);continue}let c=l==="manual"?e.manualDescription:void 0,d=l==="warning"||l==="manual"&&a===0,p=this.renderClump(l,{auditRefsOrEls:s,description:c,openByDefault:d});r.append(p)}return r}};var X=class{static initTree(e){let t=0,n=Object.keys(e);return n.length>0&&(t=e[n[0]].request.startTime),{tree:e,startTime:t,transferSize:0}}static createSegment(e,t,n,r,i,a){let l=e[t],s=Object.keys(e),c=s.indexOf(t)===s.length-1,d=!!l.children&&Object.keys(l.children).length>0,p=Array.isArray(i)?i.slice(0):[];return typeof a<"u"&&p.push(!a),{node:l,isLastChild:c,hasChildren:d,startTime:n,transferSize:r+l.request.transferSize,treeMarkers:p}}static createChainNode(e,t,n){let r=e.createComponent("crcChain");e.find(".lh-crc-node",r).setAttribute("title",t.node.request.url);let i=e.find(".lh-crc-node__tree-marker",r);t.treeMarkers.forEach(p=>{let h=p?"lh-tree-marker lh-vert":"lh-tree-marker";i.append(e.createElement("span",h),e.createElement("span","lh-tree-marker"))});let a=t.isLastChild?"lh-tree-marker lh-up-right":"lh-tree-marker lh-vert-right",l=t.hasChildren?"lh-tree-marker lh-horiz-down":"lh-tree-marker lh-right";i.append(e.createElement("span",a),e.createElement("span","lh-tree-marker lh-right"),e.createElement("span",l));let s=t.node.request.url,c=n.renderTextURL(s),d=e.find(".lh-crc-node__tree-value",r);if(d.append(c),!t.hasChildren){let{startTime:p,endTime:h,transferSize:u}=t.node.request,v=e.createElement("span","lh-crc-node__chain-duration");v.textContent=" - "+m.i18n.formatMilliseconds((h-p)*1e3)+", ";let _=e.createElement("span","lh-crc-node__chain-duration");_.textContent=m.i18n.formatBytesToKiB(u,.01),d.append(v,_)}return r}static buildTree(e,t,n,r,i,a){if(r.append(K.createChainNode(e,n,a)),n.node.children)for(let l of Object.keys(n.node.children)){let s=K.createSegment(n.node.children,l,n.startTime,n.transferSize,n.treeMarkers,n.isLastChild);K.buildTree(e,t,s,r,i,a)}}static render(e,t,n){let r=e.createComponent("crc"),i=e.find(".lh-crc",r);e.find(".lh-crc-initial-nav",r).textContent=m.strings.crcInitialNavigation,e.find(".lh-crc__longest_duration_label",r).textContent=m.strings.crcLongestDurationLabel,e.find(".lh-crc__longest_duration",r).textContent=m.i18n.formatMilliseconds(t.longestChain.duration);let a=K.initTree(t.chains);for(let l of Object.keys(a.tree)){let s=K.createSegment(a.tree,l,a.startTime,a.transferSize);K.buildTree(e,r,s,i,t,n)}return e.find(".lh-crc-container",r)}},K=X;function ut(o,e){return e.left<=o.width&&0<=e.right&&e.top<=o.height&&0<=e.bottom}function Se(o,e,t){return o<e?e:o>t?t:o}function gt(o){return{x:o.left+o.width/2,y:o.top+o.height/2}}var G=class o{static getScreenshotPositions(e,t,n){let r=gt(e),i=Se(r.x-t.width/2,0,n.width-t.width),a=Se(r.y-t.height/2,0,n.height-t.height);return{screenshot:{left:i,top:a},clip:{left:e.left-i,top:e.top-a}}}static renderClipPathInScreenshot(e,t,n,r,i){let a=e.find("clipPath",t),l=`clip-${m.getUniqueSuffix()}`;a.id=l,t.style.clipPath=`url(#${l})`;let s=n.top/i.height,c=s+r.height/i.height,d=n.left/i.width,p=d+r.width/i.width,h=[`0,0             1,0            1,${s}          0,${s}`,`0,${c}     1,${c}    1,1               0,1`,`0,${s}        ${d},${s} ${d},${c} 0,${c}`,`${p},${s} 1,${s}       1,${c}       ${p},${c}`];for(let u of h){let v=e.createElementNS("http://www.w3.org/2000/svg","polygon");v.setAttribute("points",u),a.append(v)}}static installFullPageScreenshot(e,t){e.style.setProperty("--element-screenshot-url",`url('${t.data}')`)}static installOverlayFeature(e){let{dom:t,rootEl:n,overlayContainerEl:r,fullPageScreenshot:i}=e,a="lh-screenshot-overlay--enabled";n.classList.contains(a)||(n.classList.add(a),n.addEventListener("click",l=>{let s=l.target;if(!s)return;let c=s.closest(".lh-node > .lh-element-screenshot");if(!c)return;let d=t.createElement("div","lh-element-screenshot__overlay");r.append(d);let p={width:d.clientWidth*.95,height:d.clientHeight*.8},h={width:Number(c.dataset.rectWidth),height:Number(c.dataset.rectHeight),left:Number(c.dataset.rectLeft),right:Number(c.dataset.rectLeft)+Number(c.dataset.rectWidth),top:Number(c.dataset.rectTop),bottom:Number(c.dataset.rectTop)+Number(c.dataset.rectHeight)},u=o.render(t,i.screenshot,h,p);if(!u){d.remove();return}d.append(u),d.addEventListener("click",()=>d.remove())}))}static _computeZoomFactor(e,t){let r={x:t.width/e.width,y:t.height/e.height},i=.75*Math.min(r.x,r.y);return Math.min(1,i)}static render(e,t,n,r){if(!ut(t,n))return null;let i=e.createComponent("elementScreenshot"),a=e.find("div.lh-element-screenshot",i);a.dataset.rectWidth=n.width.toString(),a.dataset.rectHeight=n.height.toString(),a.dataset.rectLeft=n.left.toString(),a.dataset.rectTop=n.top.toString();let l=this._computeZoomFactor(n,r),s={width:r.width/l,height:r.height/l};s.width=Math.min(t.width,s.width),s.height=Math.min(t.height,s.height);let c={width:s.width*l,height:s.height*l},d=o.getScreenshotPositions(n,s,{width:t.width,height:t.height}),p=e.find("div.lh-element-screenshot__image",a);p.style.width=c.width+"px",p.style.height=c.height+"px",p.style.backgroundPositionY=-(d.screenshot.top*l)+"px",p.style.backgroundPositionX=-(d.screenshot.left*l)+"px",p.style.backgroundSize=`${t.width*l}px ${t.height*l}px`;let h=e.find("div.lh-element-screenshot__element-marker",a);h.style.width=n.width*l+"px",h.style.height=n.height*l+"px",h.style.left=d.clip.left*l+"px",h.style.top=d.clip.top*l+"px";let u=e.find("div.lh-element-screenshot__mask",a);return u.style.width=c.width+"px",u.style.height=c.height+"px",o.renderClipPathInScreenshot(e,u,d.clip,n,s),a}};var mt=["http://","https://","data:"],ft=["bytes","numeric","ms","timespanMs"],ee=class{constructor(e,t={}){this._dom=e,this._fullPageScreenshot=t.fullPageScreenshot,this._entities=t.entities}render(e){switch(e.type){case"filmstrip":return this._renderFilmstrip(e);case"list":return this._renderList(e);case"checklist":return this._renderChecklist(e);case"table":case"opportunity":return this._renderTable(e);case"criticalrequestchain":return X.render(this._dom,e,this);case"screenshot":case"debugdata":case"treemap-data":return null;default:return this._renderUnknown(e.type,e)}}_renderBytes(e){let t=m.i18n.formatBytesToKiB(e.value,e.granularity||.1),n=this._renderText(t);return n.title=m.i18n.formatBytes(e.value),n}_renderMilliseconds(e){let t;return e.displayUnit==="duration"?t=m.i18n.formatDuration(e.value):t=m.i18n.formatMilliseconds(e.value,e.granularity||10),this._renderText(t)}renderTextURL(e){let t=e,n,r,i;try{let l=E.parseURL(t);n=l.file==="/"?l.origin:l.file,r=l.file==="/"||l.hostname===""?"":`(${l.hostname})`,i=t}catch{n=t}let a=this._dom.createElement("div","lh-text__url");if(a.append(this._renderLink({text:n,url:t})),r){let l=this._renderText(r);l.classList.add("lh-text__url-host"),a.append(l)}return i&&(a.title=t,a.dataset.url=t),a}_renderLink(e){let t=this._dom.createElement("a");if(this._dom.safelySetHref(t,e.url),!t.href){let n=this._renderText(e.text);return n.classList.add("lh-link"),n}return t.rel="noopener",t.target="_blank",t.textContent=e.text,t.classList.add("lh-link"),t}_renderText(e){let t=this._dom.createElement("div","lh-text");return t.textContent=e,t}_renderNumeric(e){let t=m.i18n.formatNumber(e.value,e.granularity||.1),n=this._dom.createElement("div","lh-numeric");return n.textContent=t,n}_renderThumbnail(e){let t=this._dom.createElement("img","lh-thumbnail"),n=e;return t.src=n,t.title=n,t.alt="",t}_renderUnknown(e,t){console.error(`Unknown details type: ${e}`,t);let n=this._dom.createElement("details","lh-unknown");return this._dom.createChildOf(n,"summary").textContent=`We don't know how to render audit details of type \`${e}\`. The Lighthouse version that collected this data is likely newer than the Lighthouse version of the report renderer. Expand for the raw JSON.`,this._dom.createChildOf(n,"pre").textContent=JSON.stringify(t,null,2),n}_renderTableValue(e,t){if(e==null)return null;if(typeof e=="object")switch(e.type){case"code":return this._renderCode(e.value);case"link":return this._renderLink(e);case"node":return this.renderNode(e);case"numeric":return this._renderNumeric(e);case"text":return this._renderText(e.value);case"source-location":return this.renderSourceLocation(e);case"url":return this.renderTextURL(e.value);default:return this._renderUnknown(e.type,e)}switch(t.valueType){case"bytes":{let n=Number(e);return this._renderBytes({value:n,granularity:t.granularity})}case"code":{let n=String(e);return this._renderCode(n)}case"ms":{let n={value:Number(e),granularity:t.granularity,displayUnit:t.displayUnit};return this._renderMilliseconds(n)}case"numeric":{let n=Number(e);return this._renderNumeric({value:n,granularity:t.granularity})}case"text":{let n=String(e);return this._renderText(n)}case"thumbnail":{let n=String(e);return this._renderThumbnail(n)}case"timespanMs":{let n=Number(e);return this._renderMilliseconds({value:n})}case"url":{let n=String(e);return mt.some(r=>n.startsWith(r))?this.renderTextURL(n):this._renderCode(n)}default:return this._renderUnknown(t.valueType,e)}}_getDerivedSubItemsHeading(e){return e.subItemsHeading?{key:e.subItemsHeading.key||"",valueType:e.subItemsHeading.valueType||e.valueType,granularity:e.subItemsHeading.granularity||e.granularity,displayUnit:e.subItemsHeading.displayUnit||e.displayUnit,label:""}:null}_renderTableRow(e,t){let n=this._dom.createElement("tr");for(let r of t){if(!r||!r.key){this._dom.createChildOf(n,"td","lh-table-column--empty");continue}let i=e[r.key],a;if(i!=null&&(a=this._renderTableValue(i,r)),a){let l=`lh-table-column--${r.valueType}`;this._dom.createChildOf(n,"td",l).append(a)}else this._dom.createChildOf(n,"td","lh-table-column--empty")}return n}_renderTableRowsFromItem(e,t){let n=this._dom.createFragment();if(n.append(this._renderTableRow(e,t)),!e.subItems)return n;let r=t.map(this._getDerivedSubItemsHeading);if(!r.some(Boolean))return n;for(let i of e.subItems.items){let a=this._renderTableRow(i,r);a.classList.add("lh-sub-item-row"),n.append(a)}return n}_adornEntityGroupRow(e){let t=e.dataset.entity;if(!t)return;let n=this._entities?.find(i=>i.name===t);if(!n)return;let r=this._dom.find("td",e);if(n.category){let i=this._dom.createElement("span");i.classList.add("lh-audit__adorn"),i.textContent=n.category,r.append(" ",i)}if(n.isFirstParty){let i=this._dom.createElement("span");i.classList.add("lh-audit__adorn","lh-audit__adorn1p"),i.textContent=m.strings.firstPartyChipLabel,r.append(" ",i)}if(n.homepage){let i=this._dom.createElement("a");i.href=n.homepage,i.target="_blank",i.title=m.strings.openInANewTabTooltip,i.classList.add("lh-report-icon--external"),r.append(" ",i)}}_renderEntityGroupRow(e,t){let n={...t[0]};n.valueType="text";let r=[n,...t.slice(1)],i=this._dom.createFragment();return i.append(this._renderTableRow(e,r)),this._dom.find("tr",i).classList.add("lh-row--group"),i}_getEntityGroupItems(e){let{items:t,headings:n,sortedBy:r}=e;if(!t.length||e.isEntityGrouped||!t.some(d=>d.entity))return[];let i=new Set(e.skipSumming||[]),a=[];for(let d of n)!d.key||i.has(d.key)||ft.includes(d.valueType)&&a.push(d.key);let l=n[0].key;if(!l)return[];let s=new Map;for(let d of t){let p=typeof d.entity=="string"?d.entity:void 0,h=s.get(p)||{[l]:p||m.strings.unattributable,entity:p};for(let u of a)h[u]=Number(h[u]||0)+Number(d[u]||0);s.set(p,h)}let c=[...s.values()];return r&&c.sort(k.getTableItemSortComparator(r)),c}_renderTable(e){if(!e.items.length)return this._dom.createElement("span");let t=this._dom.createElement("table","lh-table"),n=this._dom.createChildOf(t,"thead"),r=this._dom.createChildOf(n,"tr");for(let l of e.headings){let c=`lh-table-column--${l.valueType||"text"}`,d=this._dom.createElement("div","lh-text");d.textContent=l.label,this._dom.createChildOf(r,"th",c).append(d)}let i=this._getEntityGroupItems(e),a=this._dom.createChildOf(t,"tbody");if(i.length)for(let l of i){let s=typeof l.entity=="string"?l.entity:void 0,c=this._renderEntityGroupRow(l,e.headings);for(let p of e.items.filter(h=>h.entity===s))c.append(this._renderTableRowsFromItem(p,e.headings));let d=this._dom.findAll("tr",c);s&&d.length&&(d.forEach(p=>p.dataset.entity=s),this._adornEntityGroupRow(d[0])),a.append(c)}else{let l=!0;for(let s of e.items){let c=this._renderTableRowsFromItem(s,e.headings),d=this._dom.findAll("tr",c),p=d[0];if(typeof s.entity=="string"&&(p.dataset.entity=s.entity),e.isEntityGrouped&&s.entity)p.classList.add("lh-row--group"),this._adornEntityGroupRow(p);else for(let h of d)h.classList.add(l?"lh-row--even":"lh-row--odd");l=!l,a.append(c)}}return t}_renderList(e){let t=this._dom.createElement("div","lh-list");return e.items.forEach(n=>{let r=this.render(n);r&&t.append(r)}),t}_renderChecklist(e){let t=this._dom.createElement("ul","lh-checklist");return Object.values(e.items).forEach(n=>{let r=this._dom.createChildOf(t,"li","lh-checklist-item"),i=n.value?"lh-report-plain-icon--checklist-pass":"lh-report-plain-icon--checklist-fail";this._dom.createChildOf(r,"span",`lh-report-plain-icon ${i}`).textContent=n.label}),t}renderNode(e){let t=this._dom.createElement("span","lh-node");if(e.nodeLabel){let a=this._dom.createElement("div");a.textContent=e.nodeLabel,t.append(a)}if(e.snippet){let a=this._dom.createElement("div");a.classList.add("lh-node__snippet"),a.textContent=e.snippet,t.append(a)}if(e.selector&&(t.title=e.selector),e.path&&t.setAttribute("data-path",e.path),e.selector&&t.setAttribute("data-selector",e.selector),e.snippet&&t.setAttribute("data-snippet",e.snippet),!this._fullPageScreenshot)return t;let n=e.lhId&&this._fullPageScreenshot.nodes[e.lhId];if(!n||n.width===0||n.height===0)return t;let r={width:147,height:100},i=G.render(this._dom,this._fullPageScreenshot.screenshot,n,r);return i&&t.prepend(i),t}renderSourceLocation(e){if(!e.url)return null;let t=`${e.url}:${e.line+1}:${e.column}`,n;e.original&&(n=`${e.original.file||"<unmapped>"}:${e.original.line+1}:${e.original.column}`);let r;if(e.urlProvider==="network"&&n)r=this._renderLink({url:e.url,text:n}),r.title=`maps to generated location ${t}`;else if(e.urlProvider==="network"&&!n)r=this.renderTextURL(e.url),this._dom.find(".lh-link",r).textContent+=`:${e.line+1}:${e.column}`;else if(e.urlProvider==="comment"&&n)r=this._renderText(`${n} (from source map)`),r.title=`${t} (from sourceURL)`;else if(e.urlProvider==="comment"&&!n)r=this._renderText(`${t} (from sourceURL)`);else return null;return r.classList.add("lh-source-location"),r.setAttribute("data-source-url",e.url),r.setAttribute("data-source-line",String(e.line)),r.setAttribute("data-source-column",String(e.column)),r}_renderFilmstrip(e){let t=this._dom.createElement("div","lh-filmstrip");for(let n of e.items){let r=this._dom.createChildOf(t,"div","lh-filmstrip__frame"),i=this._dom.createChildOf(r,"img","lh-filmstrip__thumbnail");i.src=n.data,i.alt="Screenshot"}return t}_renderCode(e){let t=this._dom.createElement("pre","lh-code");return t.textContent=e,t}};var ce="\xA0";var ne=class{constructor(e){e==="en-XA"&&(e="de"),this._locale=e,this._cachedNumberFormatters=new Map}_formatNumberWithGranularity(e,t,n={}){if(t!==void 0){let a=-Math.log10(t);Number.isInteger(a)||(console.warn(`granularity of ${t} is invalid. Using 1 instead`),t=1),t<1&&(n={...n},n.minimumFractionDigits=n.maximumFractionDigits=Math.ceil(a)),e=Math.round(e/t)*t,Object.is(e,-0)&&(e=0)}else Math.abs(e)<5e-4&&(e=0);let r,i=[n.minimumFractionDigits,n.maximumFractionDigits,n.style,n.unit,n.unitDisplay,this._locale].join("");return r=this._cachedNumberFormatters.get(i),r||(r=new Intl.NumberFormat(this._locale,n),this._cachedNumberFormatters.set(i,r)),r.format(e).replace(" ",ce)}formatNumber(e,t){return this._formatNumberWithGranularity(e,t)}formatInteger(e){return this._formatNumberWithGranularity(e,1)}formatPercent(e){return new Intl.NumberFormat(this._locale,{style:"percent"}).format(e)}formatBytesToKiB(e,t=void 0){return this._formatNumberWithGranularity(e/1024,t)+`${ce}KiB`}formatBytesToMiB(e,t=void 0){return this._formatNumberWithGranularity(e/1048576,t)+`${ce}MiB`}formatBytes(e,t=1){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"long"})}formatBytesWithBestUnit(e,t=.1){return e>=1048576?this.formatBytesToMiB(e,t):e>=1024?this.formatBytesToKiB(e,t):this._formatNumberWithGranularity(e,t,{style:"unit",unit:"byte",unitDisplay:"narrow"})}formatKbps(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"kilobit-per-second",unitDisplay:"short"})}formatMilliseconds(e,t=void 0){return this._formatNumberWithGranularity(e,t,{style:"unit",unit:"millisecond",unitDisplay:"short"})}formatSeconds(e,t=void 0){return this._formatNumberWithGranularity(e/1e3,t,{style:"unit",unit:"second",unitDisplay:"narrow"})}formatDateTime(e){let t={month:"short",day:"numeric",year:"numeric",hour:"numeric",minute:"numeric",timeZoneName:"short"},n;try{n=new Intl.DateTimeFormat(this._locale,t)}catch{t.timeZone="UTC",n=new Intl.DateTimeFormat(this._locale,t)}return n.format(new Date(e))}formatDuration(e){let t=e/1e3;if(Math.round(t)===0)return"None";let n=[],r={day:60*60*24,hour:60*60,minute:60,second:1};return Object.keys(r).forEach(i=>{let a=r[i],l=Math.floor(t/a);if(l>0){t-=l*a;let s=this._formatNumberWithGranularity(l,1,{style:"unit",unit:i,unitDisplay:"narrow"});n.push(s)}}),n.join(" ")}};function Ce(o){let e=o.createComponent("explodeyGauge");return o.find(".lh-exp-gauge-component",e)}function Ae(o,e,t){let n=o.find("div.lh-exp-gauge__wrapper",e);n.className="",n.classList.add("lh-exp-gauge__wrapper",`lh-exp-gauge__wrapper--${k.calculateRating(t.score)}`),vt(o,n,t)}function bt(o,e,t){t=t||o/32;let n=o/t,r=.5*t,i=n+r+t,a=2*Math.PI*n,l=Math.acos(1-.5*Math.pow(.5*t/n,2))*n,s=2*Math.PI*i,c=Math.acos(1-.5*Math.pow(.5*t/i,2))*i;return{radiusInner:n,radiusOuter:i,circumferenceInner:a,circumferenceOuter:s,getArcLength:()=>Math.max(0,Number(e*a)),getMetricArcLength:(d,p=!1)=>{let h=p?0:2*c;return Math.max(0,Number(d*s-r-h))},endDiffInner:l,endDiffOuter:c,strokeWidth:t,strokeGap:r}}function vt(o,e,t){let i=Number(t.score),{radiusInner:a,radiusOuter:l,circumferenceInner:s,circumferenceOuter:c,getArcLength:d,getMetricArcLength:p,endDiffInner:h,endDiffOuter:u,strokeWidth:v,strokeGap:_}=bt(128,i),f=o.find("svg.lh-exp-gauge",e);o.find(".lh-exp-gauge__label",f).textContent=t.title,f.setAttribute("viewBox",[-64,-64/2,128,128/2].join(" ")),f.style.setProperty("--stroke-width",`${v}px`),f.style.setProperty("--circle-meas",(2*Math.PI).toFixed(4));let g=o.find("g.lh-exp-gauge__outer",e),b=o.find("g.lh-exp-gauge__inner",e),w=o.find("circle.lh-cover",g),x=o.find("circle.lh-exp-gauge__arc",b),S=o.find("text.lh-exp-gauge__percentage",b);g.style.setProperty("--scale-initial",String(a/l)),g.style.setProperty("--radius",`${l}px`),w.style.setProperty("--radius",`${.5*(a+l)}px`),w.setAttribute("stroke-width",String(_)),f.style.setProperty("--radius",`${a}px`),x.setAttribute("stroke-dasharray",`${d()} ${(s-d()).toFixed(4)}`),x.setAttribute("stroke-dashoffset",String(.25*s-h)),S.textContent=Math.round(i*100).toString();let A=l+v,L=l-v,T=t.auditRefs.filter(y=>y.group==="metrics"&&y.weight),F=T.reduce((y,C)=>y+=C.weight,0),N=.25*c-u-.5*_,I=-.5*Math.PI;g.querySelectorAll(".metric").forEach(y=>{T.map(D=>`metric--${D.id}`).find(D=>y.classList.contains(D))||y.remove()}),T.forEach((y,C)=>{let z=y.acronym??y.id,D=!g.querySelector(`.metric--${z}`),M=o.maybeFind(`g.metric--${z}`,g)||o.createSVGElement("g"),q=o.maybeFind(`.metric--${z} circle.lh-exp-gauge--faded`,g)||o.createSVGElement("circle"),Z=o.maybeFind(`.metric--${z} circle.lh-exp-gauge--miniarc`,g)||o.createSVGElement("circle"),j=o.maybeFind(`.metric--${z} circle.lh-exp-gauge-hovertarget`,g)||o.createSVGElement("circle"),R=o.maybeFind(`.metric--${z} text.metric__label`,g)||o.createSVGElement("text"),U=o.maybeFind(`.metric--${z} text.metric__value`,g)||o.createSVGElement("text");M.classList.add("metric",`metric--${z}`),q.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--faded"),Z.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge--miniarc"),j.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget");let W=y.weight/F,he=p(W),ue=y.result.score?y.result.score*W:0,ge=p(ue),Re=W*c,me=p(W,!0),fe=k.calculateRating(y.result.score,y.result.scoreDisplayMode);M.style.setProperty("--metric-rating",fe),M.style.setProperty("--metric-color",`var(--color-${fe})`),M.style.setProperty("--metric-offset",`${N}`),M.style.setProperty("--i",C.toString()),q.setAttribute("stroke-dasharray",`${he} ${c-he}`),Z.style.setProperty("--metric-array",`${ge} ${c-ge}`),j.setAttribute("stroke-dasharray",`${me} ${c-me-u}`),R.classList.add("metric__label"),U.classList.add("metric__value"),R.textContent=z,U.textContent=`+${Math.round(ue*100)}`;let be=I+W*Math.PI,Q=Math.cos(be),Y=Math.sin(be);switch(!0){case Q>0:U.setAttribute("text-anchor","end");break;case Q<0:R.setAttribute("text-anchor","end");break;case Q===0:R.setAttribute("text-anchor","middle"),U.setAttribute("text-anchor","middle");break}switch(!0){case Y>0:R.setAttribute("dominant-baseline","hanging");break;case Y<0:U.setAttribute("dominant-baseline","hanging");break;case Y===0:R.setAttribute("dominant-baseline","middle"),U.setAttribute("dominant-baseline","middle");break}R.setAttribute("x",(A*Q).toFixed(2)),R.setAttribute("y",(A*Y).toFixed(2)),U.setAttribute("x",(L*Q).toFixed(2)),U.setAttribute("y",(L*Y).toFixed(2)),D&&(M.appendChild(q),M.appendChild(Z),M.appendChild(j),M.appendChild(R),M.appendChild(U),g.appendChild(M)),N-=Re,I+=W*2*Math.PI});let P=g.querySelector(".lh-exp-gauge-underhovertarget")||o.createSVGElement("circle");P.classList.add("lh-exp-gauge__arc","lh-exp-gauge__arc--metric","lh-exp-gauge-hovertarget","lh-exp-gauge-underhovertarget");let H=p(1,!0);if(P.setAttribute("stroke-dasharray",`${H} ${c-H-u}`),P.isConnected||g.prepend(P),f.dataset.listenersSetup)return;f.dataset.listenersSetup=!0,V(f),f.addEventListener("pointerover",y=>{if(y.target===f&&f.classList.contains("state--expanded")){f.classList.remove("state--expanded"),f.classList.contains("state--highlight")&&(f.classList.remove("state--highlight"),o.find(".metric--highlight",f).classList.remove("metric--highlight"));return}if(!(y.target instanceof Element))return;let C=y.target.parentNode;if(C instanceof SVGElement){if(C&&C===b){f.classList.contains("state--expanded")?f.classList.contains("state--highlight")&&(f.classList.remove("state--highlight"),o.find(".metric--highlight",f).classList.remove("metric--highlight")):f.classList.add("state--expanded");return}if(C&&C.classList&&C.classList.contains("metric")){let z=C.style.getPropertyValue("--metric-rating");if(e.style.setProperty("--color-highlight",`var(--color-${z}-secondary)`),!f.classList.contains("state--highlight"))f.classList.add("state--highlight"),C.classList.add("metric--highlight");else{let D=o.find(".metric--highlight",f);C!==D&&(D.classList.remove("metric--highlight"),C.classList.add("metric--highlight"))}}}}),f.addEventListener("mouseleave",()=>{f.classList.remove("state--highlight"),f.querySelector(".metric--highlight")?.classList.remove("metric--highlight")});async function V(y){if(await new Promise(R=>setTimeout(R,1e3)),y.classList.contains("state--expanded"))return;let C=o.find(".lh-exp-gauge__inner",y),z=`uniq-${Math.random()}`;C.setAttribute("id",z);let D=o.createSVGElement("use");D.setAttribute("href",`#${z}`),y.appendChild(D);let M=2.5;y.style.setProperty("--peek-dur",`${M}s`),y.classList.add("state--peek","state--expanded");let q=()=>{y.classList.remove("state--peek","state--expanded"),D.remove()},Z=setTimeout(()=>{y.removeEventListener("mouseenter",j),q()},M*1e3*1.5);function j(){clearTimeout(Z),q()}y.addEventListener("mouseenter",j,{once:!0})}}var re=class extends B{_renderMetric(e){let t=this.dom.createComponent("metric"),n=this.dom.find(".lh-metric",t);n.id=e.result.id;let r=k.calculateRating(e.result.score,e.result.scoreDisplayMode);n.classList.add(`lh-metric--${r}`);let i=this.dom.find(".lh-metric__title",t);i.textContent=e.result.title;let a=this.dom.find(".lh-metric__value",t);a.textContent=e.result.displayValue||"";let l=this.dom.find(".lh-metric__description",t);if(l.append(this.dom.convertMarkdownLinkSnippets(e.result.description)),e.result.scoreDisplayMode==="error"){l.textContent="",a.textContent="Error!";let s=this.dom.createChildOf(l,"span");s.textContent=e.result.errorMessage||"Report error: no metric information"}else e.result.scoreDisplayMode==="notApplicable"&&(a.textContent="--");return n}_getScoringCalculatorHref(e){let t=e.filter(p=>p.group==="metrics"),n=e.find(p=>p.id==="interactive"),r=e.find(p=>p.id==="first-cpu-idle"),i=e.find(p=>p.id==="first-meaningful-paint");n&&t.push(n),r&&t.push(r),i&&typeof i.result.score=="number"&&t.push(i);let a=p=>Math.round(p*100)/100,s=[...t.map(p=>{let h;return typeof p.result.numericValue=="number"?(h=p.id==="cumulative-layout-shift"?a(p.result.numericValue):Math.round(p.result.numericValue),h=h.toString()):h="null",[p.acronym||p.id,h]})];m.reportJson&&(s.push(["device",m.reportJson.configSettings.formFactor]),s.push(["version",m.reportJson.lighthouseVersion]));let c=new URLSearchParams(s),d=new URL("https://googlechrome.github.io/lighthouse/scorecalc/");return d.hash=c.toString(),d.href}overallImpact(e,t){if(!e.result.metricSavings)return{overallImpact:0,overallLinearImpact:0};let n=0,r=0;for(let[i,a]of Object.entries(e.result.metricSavings)){if(a===void 0)continue;let l=t.find(u=>u.acronym===i);if(!l||l.result.score===null)continue;let s=l.result.numericValue;if(!s)continue;let c=a/s*l.weight;r+=c;let d=l.result.scoringOptions;if(!d)continue;let h=(E.computeLogNormalScore(d,s-a)-l.result.score)*l.weight;n+=h}return{overallImpact:n,overallLinearImpact:r}}render(e,t,n){let r=m.strings,i=this.dom.createElement("div","lh-category");i.id=e.id,i.append(this.renderCategoryHeader(e,t,n));let a=e.auditRefs.filter(u=>u.group==="metrics");if(a.length){let[u,v]=this.renderAuditGroup(t.metrics),_=this.dom.createElement("input","lh-metrics-toggle__input"),f=`lh-metrics-toggle${m.getUniqueSuffix()}`;_.setAttribute("aria-label","Toggle the display of metric descriptions"),_.type="checkbox",_.id=f,u.prepend(_);let g=this.dom.find(".lh-audit-group__header",u),b=this.dom.createChildOf(g,"label","lh-metrics-toggle__label");b.htmlFor=f;let w=this.dom.createChildOf(b,"span","lh-metrics-toggle__labeltext--show"),x=this.dom.createChildOf(b,"span","lh-metrics-toggle__labeltext--hide");w.textContent=m.strings.expandView,x.textContent=m.strings.collapseView;let S=this.dom.createElement("div","lh-metrics-container");if(u.insertBefore(S,v),a.forEach(A=>{S.append(this._renderMetric(A))}),i.querySelector(".lh-gauge__wrapper")){let A=this.dom.find(".lh-category-header__description",i),L=this.dom.createChildOf(A,"div","lh-metrics__disclaimer"),T=this.dom.convertMarkdownLinkSnippets(r.varianceDisclaimer);L.append(T);let F=this.dom.createChildOf(L,"a","lh-calclink");F.target="_blank",F.textContent=r.calculatorLink,this.dom.safelySetHref(F,this._getScoringCalculatorHref(e.auditRefs))}u.classList.add("lh-audit-group--metrics"),i.append(u)}let l=this.dom.createChildOf(i,"div","lh-filmstrip-container"),c=e.auditRefs.find(u=>u.id==="screenshot-thumbnails")?.result;if(c?.details){l.id=c.id;let u=this.detailsRenderer.render(c.details);u&&l.append(u)}let d=this.renderFilterableSection(e,t,["diagnostics"],a);d?.classList.add("lh-perf-audits--swappable","lh-perf-audits--legacy");let p=this.renderFilterableSection(e,t,["insights","diagnostics"],a);if(p?.classList.add("lh-perf-audits--swappable","lh-perf-audits--experimental"),d&&(i.append(d),p&&this.dom.registerSwappableSections(d,p)),(!n||n?.gatherMode==="navigation")&&e.score!==null){let u=Ce(this.dom);Ae(this.dom,u,e),this.dom.find(".lh-score__gauge",i).replaceWith(u)}return i}renderFilterableSection(e,t,n,r){if(n.some(g=>!t[g]))return null;let i=this.dom.createElement("div"),a=new Set,l=e.auditRefs.filter(g=>g.group&&n.includes(g.group));for(let g of l)g.result.replacesAudits?.forEach(b=>{a.add(b)});let s=l.filter(g=>!a.has(g.id)).map(g=>{let{overallImpact:b,overallLinearImpact:w}=this.overallImpact(g,r),x=g.result.guidanceLevel||1,S=this.renderAudit(g);return{auditRef:g,auditEl:S,overallImpact:b,overallLinearImpact:w,guidanceLevel:x}}),c=s.filter(g=>!k.showAsPassed(g.auditRef.result)),d=s.filter(g=>k.showAsPassed(g.auditRef.result)),p={};for(let g of n){let b=this.renderAuditGroup(t[g]);b[0].classList.add(`lh-audit-group--${g}`),p[g]=b}function h(g){for(let b of s)if(g==="All")b.auditEl.hidden=!1;else{let w=b.auditRef.result.metricSavings?.[g]===void 0;b.auditEl.hidden=w}c.sort((b,w)=>{let x=b.auditRef.result.score||0,S=w.auditRef.result.score||0;if(x!==S)return x-S;if(g!=="All"){let A=b.auditRef.result.metricSavings?.[g]??-1,L=w.auditRef.result.metricSavings?.[g]??-1;if(A!==L)return L-A}return b.overallImpact!==w.overallImpact?w.overallImpact*w.guidanceLevel-b.overallImpact*b.guidanceLevel:b.overallImpact===0&&w.overallImpact===0&&b.overallLinearImpact!==w.overallLinearImpact?w.overallLinearImpact*w.guidanceLevel-b.overallLinearImpact*b.guidanceLevel:w.guidanceLevel-b.guidanceLevel});for(let b of c){if(!b.auditRef.group)continue;let w=p[b.auditRef.group];if(!w)continue;let[x,S]=w;x.insertBefore(b.auditEl,S)}}let u=new Set;for(let g of c){let b=g.auditRef.result.metricSavings||{};for(let[w,x]of Object.entries(b))typeof x=="number"&&u.add(w)}let v=r.filter(g=>g.acronym&&u.has(g.acronym));v.length&&this.renderMetricAuditFilter(v,i,h),h("All");for(let g of n)if(c.some(b=>b.auditRef.group===g)){let b=p[g];if(!b)continue;i.append(b[0])}if(!d.length)return i;let _={auditRefsOrEls:d.map(g=>g.auditEl),groupDefinitions:t},f=this.renderClump("passed",_);return i.append(f),i}renderMetricAuditFilter(e,t,n){let r=this.dom.createElement("div","lh-metricfilter"),i=this.dom.createChildOf(r,"span","lh-metricfilter__text");i.textContent=m.strings.showRelevantAudits;let a=[{acronym:"All",id:"All"},...e],l=m.getUniqueSuffix();for(let s of a){let c=`metric-${s.acronym}-${l}`,d=this.dom.createChildOf(r,"input","lh-metricfilter__radio");d.type="radio",d.name=`metricsfilter-${l}`,d.id=c;let p=this.dom.createChildOf(r,"label","lh-metricfilter__label");p.htmlFor=c,p.title="result"in s?s.result.title:"",p.textContent=s.acronym||s.id,s.acronym==="All"&&(d.checked=!0,p.classList.add("lh-metricfilter__label--active")),t.append(r),d.addEventListener("input",h=>{for(let v of t.querySelectorAll("label.lh-metricfilter__label"))v.classList.toggle("lh-metricfilter__label--active",v.htmlFor===c);t.classList.toggle("lh-category--filtered",s.acronym!=="All"),n(s.acronym||"All");let u=t.querySelectorAll("div.lh-audit-group, details.lh-audit-group");for(let v of u){v.hidden=!1;let _=Array.from(v.querySelectorAll("div.lh-audit")),f=!!_.length&&_.every(g=>g.hidden);v.hidden=f}})}}};var oe=class{constructor(e){this._dom=e,this._opts={}}renderReport(e,t,n){if(!this._dom.rootEl&&t){console.warn("Please adopt the new report API in renderer/api.js.");let i=t.closest(".lh-root");i?this._dom.rootEl=i:(t.classList.add("lh-root","lh-vars"),this._dom.rootEl=t)}else this._dom.rootEl&&t&&(this._dom.rootEl=t);n&&(this._opts=n),this._dom.setLighthouseChannel(e.configSettings.channel||"unknown");let r=k.prepareReportResult(e);return this._dom.rootEl.textContent="",this._dom.rootEl.append(this._renderReport(r)),this._opts.occupyEntireViewport&&this._dom.rootEl.classList.add("lh-max-viewport"),this._dom.rootEl}_renderReportTopbar(e){let t=this._dom.createComponent("topbar"),n=this._dom.find("a.lh-topbar__url",t);return n.textContent=e.finalDisplayedUrl,n.title=e.finalDisplayedUrl,this._dom.safelySetHref(n,e.finalDisplayedUrl),t}_renderReportHeader(){let e=this._dom.createComponent("heading"),t=this._dom.createComponent("scoresWrapper");return this._dom.find(".lh-scores-wrapper-placeholder",e).replaceWith(t),e}_renderReportFooter(e){let t=this._dom.createComponent("footer");return this._renderMetaBlock(e,t),this._dom.find(".lh-footer__version_issue",t).textContent=m.strings.footerIssue,this._dom.find(".lh-footer__version",t).textContent=e.lighthouseVersion,t}_renderMetaBlock(e,t){let n=k.getEmulationDescriptions(e.configSettings||{}),r=e.userAgent.match(/(\w*Chrome\/[\d.]+)/),i=Array.isArray(r)?r[1].replace("/"," ").replace("Chrome","Chromium"):"Chromium",a=e.configSettings.channel,l=e.environment.benchmarkIndex.toFixed(0),s=e.environment.credits?.["axe-core"],c=[`${m.strings.runtimeSettingsBenchmark}: ${l}`,`${m.strings.runtimeSettingsCPUThrottling}: ${n.cpuThrottling}`];n.screenEmulation&&c.push(`${m.strings.runtimeSettingsScreenEmulation}: ${n.screenEmulation}`),s&&c.push(`${m.strings.runtimeSettingsAxeVersion}: ${s}`);let d=m.strings.runtimeAnalysisWindow;e.gatherMode==="timespan"?d=m.strings.runtimeAnalysisWindowTimespan:e.gatherMode==="snapshot"&&(d=m.strings.runtimeAnalysisWindowSnapshot);let p=[["date",`Captured at ${m.i18n.formatDateTime(e.fetchTime)}`],["devices",`${n.deviceEmulation} with Lighthouse ${e.lighthouseVersion}`,c.join(`
`)],["samples-one",m.strings.runtimeSingleLoad,m.strings.runtimeSingleLoadTooltip],["stopwatch",d],["networkspeed",`${n.summary}`,`${m.strings.runtimeSettingsNetworkThrottling}: ${n.networkThrottling}`],["chrome",`Using ${i}`+(a?` with ${a}`:""),`${m.strings.runtimeSettingsUANetwork}: "${e.environment.networkUserAgent}"`]],h=this._dom.find(".lh-meta__items",t);for(let[u,v,_]of p){let f=this._dom.createChildOf(h,"li","lh-meta__item");if(f.textContent=v,_){f.classList.add("lh-tooltip-boundary");let g=this._dom.createChildOf(f,"div","lh-tooltip");g.textContent=_}f.classList.add("lh-report-icon",`lh-report-icon--${u}`)}}_renderReportWarnings(e){if(!e.runWarnings||e.runWarnings.length===0)return this._dom.createElement("div");let t=this._dom.createComponent("warningsToplevel"),n=this._dom.find(".lh-warnings__msg",t);n.textContent=m.strings.toplevelWarningsMessage;let r=[];for(let i of e.runWarnings){let a=this._dom.createElement("li");a.append(this._dom.convertMarkdownLinkSnippets(i)),r.push(a)}return this._dom.find("ul",t).append(...r),t}_renderScoreGauges(e,t,n){let r=[],i=[];for(let a of Object.values(e.categories)){let s=(n[a.id]||t).renderCategoryScore(a,e.categoryGroups||{},{gatherMode:e.gatherMode}),c=this._dom.find("a.lh-gauge__wrapper, a.lh-fraction__wrapper",s);c&&(this._dom.safelySetHref(c,`#${a.id}`),c.addEventListener("click",d=>{if(!c.matches('[href^="#"]'))return;let p=c.getAttribute("href"),h=this._dom.rootEl;if(!p||!h)return;let u=this._dom.find(p,h);d.preventDefault(),u.scrollIntoView()}),this._opts.onPageAnchorRendered?.(c)),k.isPluginCategory(a.id)?i.push(s):r.push(s)}return[...r,...i]}_renderReport(e){m.apply({providedStrings:e.i18n.rendererFormattedStrings,i18n:new ne(e.configSettings.locale),reportJson:e});let t=new ee(this._dom,{fullPageScreenshot:e.fullPageScreenshot??void 0,entities:e.entities}),n=new B(this._dom,t),r={performance:new re(this._dom,t)},i=this._dom.createElement("div");i.append(this._renderReportHeader());let a=this._dom.createElement("div","lh-container"),l=this._dom.createElement("div","lh-report");l.append(this._renderReportWarnings(e));let s;Object.keys(e.categories).length===1?i.classList.add("lh-header--solo-category"):s=this._dom.createElement("div","lh-scores-header");let d=this._dom.createElement("div");if(d.classList.add("lh-scorescale-wrap"),d.append(this._dom.createComponent("scorescale")),s){let v=this._dom.find(".lh-scores-container",i);s.append(...this._renderScoreGauges(e,n,r)),v.append(s,d);let _=this._dom.createElement("div","lh-sticky-header");_.append(...this._renderScoreGauges(e,n,r)),a.append(_)}let p=this._dom.createElement("div","lh-categories");l.append(p);let h={gatherMode:e.gatherMode};for(let v of Object.values(e.categories)){let _=r[v.id]||n;_.dom.createChildOf(p,"div","lh-category-wrapper").append(_.render(v,e.categoryGroups,h))}n.injectFinalScreenshot(p,e.audits,d);let u=this._dom.createFragment();return this._opts.omitGlobalStyles||u.append(this._dom.createComponent("styles")),this._opts.omitTopbar||u.append(this._renderReportTopbar(e)),u.append(a),l.append(this._renderReportFooter(e)),a.append(i,l),e.fullPageScreenshot&&G.installFullPageScreenshot(this._dom.rootEl,e.fullPageScreenshot.screenshot),u}};function J(o,e){let t=o.rootEl;typeof e>"u"?t.classList.toggle("lh-dark"):t.classList.toggle("lh-dark",e)}var _t=typeof btoa<"u"?btoa:o=>Buffer.from(o).toString("base64"),wt=typeof atob<"u"?atob:o=>Buffer.from(o,"base64").toString();async function yt(o,e){let t=new TextEncoder().encode(o);if(e.gzip)if(typeof CompressionStream<"u"){let i=new CompressionStream("gzip"),a=i.writable.getWriter();a.write(t),a.close();let l=await new Response(i.readable).arrayBuffer();t=new Uint8Array(l)}else t=window.pako.gzip(o);let n="",r=5e3;for(let i=0;i<t.length;i+=r)n+=String.fromCharCode(...t.subarray(i,i+r));return _t(n)}function xt(o,e){let t=wt(o),n=Uint8Array.from(t,r=>r.charCodeAt(0));return e.gzip?window.pako.ungzip(n,{to:"string"}):new TextDecoder().decode(n)}var Le={toBase64:yt,fromBase64:xt};function de(){let o=window.location.host.endsWith(".vercel.app"),e=new URLSearchParams(window.location.search).has("dev");return o?`https://${window.location.host}/gh-pages`:e?"http://localhost:7333":"https://googlechrome.github.io/lighthouse"}function pe(o){let e=o.generatedTime,t=o.fetchTime||e;return`${o.lighthouseVersion}-${o.finalDisplayedUrl}-${t}`}function kt(o,e,t){let n=new URL(e).origin;window.addEventListener("message",function i(a){a.origin===n&&r&&a.data.opened&&(r.postMessage(o,n),window.removeEventListener("message",i))});let r=window.open(e,t)}async function ze(o,e,t){let n=new URL(e),r=!!window.CompressionStream;n.hash=await Le.toBase64(JSON.stringify(o),{gzip:r}),r&&n.searchParams.set("gzip","1"),window.open(n.toString(),t)}async function Me(o){let e="viewer-"+pe(o),t=de()+"/viewer/";await ze({lhr:o},t,e)}async function Te(o){let e="viewer-"+pe(o),t=de()+"/viewer/";kt({lhr:o},t,e)}function Fe(o){if(!o.audits["script-treemap-data"].details)throw new Error("no script treemap data found");let t={lhr:{mainDocumentUrl:o.mainDocumentUrl,finalUrl:o.finalUrl,finalDisplayedUrl:o.finalDisplayedUrl,audits:{"script-treemap-data":o.audits["script-treemap-data"]},configSettings:{locale:o.configSettings.locale}}},n=de()+"/treemap/",r="treemap-"+pe(o);ze(t,n,r)}var ie=class{constructor(e){this._dom=e,this._toggleEl,this._menuEl,this.onDocumentKeyDown=this.onDocumentKeyDown.bind(this),this.onToggleClick=this.onToggleClick.bind(this),this.onToggleKeydown=this.onToggleKeydown.bind(this),this.onMenuFocusOut=this.onMenuFocusOut.bind(this),this.onMenuKeydown=this.onMenuKeydown.bind(this),this._getNextMenuItem=this._getNextMenuItem.bind(this),this._getNextSelectableNode=this._getNextSelectableNode.bind(this),this._getPreviousMenuItem=this._getPreviousMenuItem.bind(this)}setup(e){this._toggleEl=this._dom.find(".lh-topbar button.lh-tools__button",this._dom.rootEl),this._toggleEl.addEventListener("click",this.onToggleClick),this._toggleEl.addEventListener("keydown",this.onToggleKeydown),this._menuEl=this._dom.find(".lh-topbar div.lh-tools__dropdown",this._dom.rootEl),this._menuEl.addEventListener("keydown",this.onMenuKeydown),this._menuEl.addEventListener("click",e)}close(){this._toggleEl.classList.remove("lh-active"),this._toggleEl.setAttribute("aria-expanded","false"),this._menuEl.contains(this._dom.document().activeElement)&&this._toggleEl.focus(),this._menuEl.removeEventListener("focusout",this.onMenuFocusOut),this._dom.document().removeEventListener("keydown",this.onDocumentKeyDown)}open(e){this._toggleEl.classList.contains("lh-active")?e.focus():this._menuEl.addEventListener("transitionend",()=>{e.focus()},{once:!0}),this._toggleEl.classList.add("lh-active"),this._toggleEl.setAttribute("aria-expanded","true"),this._menuEl.addEventListener("focusout",this.onMenuFocusOut),this._dom.document().addEventListener("keydown",this.onDocumentKeyDown)}onToggleClick(e){e.preventDefault(),e.stopImmediatePropagation(),this._toggleEl.classList.contains("lh-active")?this.close():this.open(this._getNextMenuItem())}onToggleKeydown(e){switch(e.code){case"ArrowUp":e.preventDefault(),this.open(this._getPreviousMenuItem());break;case"ArrowDown":case"Enter":case" ":e.preventDefault(),this.open(this._getNextMenuItem());break;default:}}onMenuKeydown(e){let t=e.target;switch(e.code){case"ArrowUp":e.preventDefault(),this._getPreviousMenuItem(t).focus();break;case"ArrowDown":e.preventDefault(),this._getNextMenuItem(t).focus();break;case"Home":e.preventDefault(),this._getNextMenuItem().focus();break;case"End":e.preventDefault(),this._getPreviousMenuItem().focus();break;default:}}onDocumentKeyDown(e){e.keyCode===27&&this.close()}onMenuFocusOut(e){let t=e.relatedTarget;this._menuEl.contains(t)||this.close()}_getNextSelectableNode(e,t){let n=e.filter(i=>i instanceof HTMLElement).filter(i=>!(i.hasAttribute("disabled")||window.getComputedStyle(i).display==="none")),r=t?n.indexOf(t)+1:0;return r>=n.length&&(r=0),n[r]}_getNextMenuItem(e){let t=Array.from(this._menuEl.childNodes);return this._getNextSelectableNode(t,e)}_getPreviousMenuItem(e){let t=Array.from(this._menuEl.childNodes).reverse();return this._getNextSelectableNode(t,e)}};var ae=class{constructor(e,t){this.lhr,this._reportUIFeatures=e,this._dom=t,this._dropDownMenu=new ie(this._dom),this._copyAttempt=!1,this.topbarEl,this.categoriesEl,this.stickyHeaderEl,this.highlightEl,this.onDropDownMenuClick=this.onDropDownMenuClick.bind(this),this.onKeyUp=this.onKeyUp.bind(this),this.onCopy=this.onCopy.bind(this),this.collapseAllDetails=this.collapseAllDetails.bind(this)}enable(e){this.lhr=e,this._dom.rootEl.addEventListener("keyup",this.onKeyUp),this._dom.document().addEventListener("copy",this.onCopy),this._dropDownMenu.setup(this.onDropDownMenuClick),this._setUpCollapseDetailsAfterPrinting(),this._dom.find(".lh-topbar__logo",this._dom.rootEl).addEventListener("click",()=>J(this._dom)),this._setupStickyHeader()}onDropDownMenuClick(e){e.preventDefault();let t=e.target;if(!(!t||!t.hasAttribute("data-action"))){switch(t.getAttribute("data-action")){case"copy":this.onCopyButtonClick();break;case"print-summary":this.collapseAllDetails(),this._print();break;case"print-expanded":this.expandAllDetails(),this._print();break;case"save-json":{let n=JSON.stringify(this.lhr,null,2);this._reportUIFeatures._saveFile(new Blob([n],{type:"application/json"}));break}case"save-html":{let n=this._reportUIFeatures.getReportHtml();try{this._reportUIFeatures._saveFile(new Blob([n],{type:"text/html"}))}catch(r){this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"error",msg:"Could not export as HTML. "+r.message})}break}case"open-viewer":{this._dom.isDevTools()?Me(this.lhr):Te(this.lhr);break}case"save-gist":{this._reportUIFeatures.saveAsGist();break}case"toggle-dark":{J(this._dom);break}case"toggle-insights":{let n=this._dom.maybeFind(".lh-perf-audits--swappable");n&&this._dom.swapSectionIfPossible(n);break}case"view-unthrottled-trace":this._reportUIFeatures._opts.onViewTrace?.()}this._dropDownMenu.close()}}onCopy(e){this._copyAttempt&&e.clipboardData&&(e.preventDefault(),e.clipboardData.setData("text/plain",JSON.stringify(this.lhr,null,2)),this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:"Report JSON copied to clipboard"})),this._copyAttempt=!1}onCopyButtonClick(){this._dom.fireEventOn("lh-analytics",this._dom.document(),{cmd:"send",fields:{hitType:"event",eventCategory:"report",eventAction:"copy"}});try{this._dom.document().queryCommandSupported("copy")&&(this._copyAttempt=!0,this._dom.document().execCommand("copy")||(this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"warn",msg:"Your browser does not support copy to clipboard."})))}catch(e){this._copyAttempt=!1,this._dom.fireEventOn("lh-log",this._dom.document(),{cmd:"log",msg:e.message})}}onKeyUp(e){(e.ctrlKey||e.metaKey)&&e.keyCode===80&&this._dropDownMenu.close()}expandAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map(t=>t.open=!0)}collapseAllDetails(){this._dom.findAll(".lh-categories details",this._dom.rootEl).map(t=>t.open=!1)}_print(){this._reportUIFeatures._opts.onPrintOverride?this._reportUIFeatures._opts.onPrintOverride(this._dom.rootEl):self.print()}resetUIState(){this._dropDownMenu.close()}_getScrollParent(e){let{overflowY:t}=window.getComputedStyle(e);return t!=="visible"&&t!=="hidden"?e:e.parentElement?this._getScrollParent(e.parentElement):document}_setUpCollapseDetailsAfterPrinting(){"onbeforeprint"in self?self.addEventListener("afterprint",this.collapseAllDetails):self.matchMedia("print").addListener(t=>{t.matches?this.expandAllDetails():this.collapseAllDetails()})}_setupStickyHeader(){this.topbarEl=this._dom.find("div.lh-topbar",this._dom.rootEl),this.categoriesEl=this._dom.find("div.lh-categories",this._dom.rootEl),window.requestAnimationFrame(()=>window.requestAnimationFrame(()=>{try{this.stickyHeaderEl=this._dom.find("div.lh-sticky-header",this._dom.rootEl)}catch{return}this.highlightEl=this._dom.createChildOf(this.stickyHeaderEl,"div","lh-highlighter");let e=this._getScrollParent(this._dom.find(".lh-container",this._dom.rootEl));e.addEventListener("scroll",()=>this._updateStickyHeader());let t=e instanceof window.Document?document.documentElement:e;new window.ResizeObserver(()=>this._updateStickyHeader()).observe(t)}))}_updateStickyHeader(){if(!this.stickyHeaderEl)return;let e=this.topbarEl.getBoundingClientRect().bottom,t=this.categoriesEl.getBoundingClientRect().top,n=e>=t,i=Array.from(this._dom.rootEl.querySelectorAll(".lh-category")).filter(p=>p.getBoundingClientRect().top-window.innerHeight/2<0),a=i.length>0?i.length-1:0,l=this.stickyHeaderEl.querySelectorAll(".lh-gauge__wrapper, .lh-fraction__wrapper"),s=l[a],c=l[0].getBoundingClientRect().left,d=s.getBoundingClientRect().left-c;this.highlightEl.style.transform=`translate(${d}px)`,this.stickyHeaderEl.classList.toggle("lh-sticky-header--visible",n)}};function Et(o,e){let t=e?new Date(e):new Date,n=t.toLocaleTimeString("en-US",{hour12:!1}),r=t.toLocaleDateString("en-US",{year:"numeric",month:"2-digit",day:"2-digit"}).split("/");r.unshift(r.pop());let i=r.join("-");return`${o}_${i}_${n}`.replace(/[/?<>\\:*|"]/g,"-")}function De(o){let e=new URL(o.finalDisplayedUrl).hostname;return Et(e,o.fetchTime)}function St(o){return Array.from(o.tBodies[0].rows)}var le=class{constructor(e,t={}){this.json,this._dom=e,this._opts=t,this._topbar=t.omitTopbar?null:new ae(this,e),this.onMediaQueryChange=this.onMediaQueryChange.bind(this)}initFeatures(e){this.json=e,this._fullPageScreenshot=E.getFullPageScreenshot(e),this._topbar&&(this._topbar.enable(e),this._topbar.resetUIState()),this._setupMediaQueryListeners(),this._setupThirdPartyFilter(),this._setupElementScreenshotOverlay(this._dom.rootEl);let t=this._dom.isDevTools()||this._opts.disableDarkMode||this._opts.disableAutoDarkModeAndFireworks;!t&&window.matchMedia("(prefers-color-scheme: dark)").matches&&J(this._dom,!0);let r=["performance","accessibility","best-practices","seo"].every(s=>{let c=e.categories[s];return c&&c.score===1}),i=this._opts.disableFireworks||this._opts.disableAutoDarkModeAndFireworks;if(r&&!i&&(this._enableFireworks(),t||J(this._dom,!0)),e.categories.performance&&e.categories.performance.auditRefs.some(s=>!!(s.group==="metrics"&&e.audits[s.id].errorMessage))){let s=this._dom.find("input.lh-metrics-toggle__input",this._dom.rootEl);s.checked=!0}this.json.audits["script-treemap-data"]&&this.json.audits["script-treemap-data"].details&&this.addButton({text:m.strings.viewTreemapLabel,icon:"treemap",onClick:()=>Fe(this.json)}),this._opts.onViewTrace&&(e.configSettings.throttlingMethod==="simulate"?this._dom.find('a[data-action="view-unthrottled-trace"]',this._dom.rootEl).classList.remove("lh-hidden"):this.addButton({text:m.strings.viewTraceLabel,onClick:()=>this._opts.onViewTrace?.()})),this._opts.getStandaloneReportHTML&&this._dom.find('a[data-action="save-html"]',this._dom.rootEl).classList.remove("lh-hidden");for(let s of this._dom.findAll("[data-i18n]",this._dom.rootEl)){let d=s.getAttribute("data-i18n");s.textContent=m.strings[d]}}addButton(e){let t=this._dom.rootEl.querySelector(".lh-audit-group--metrics");if(!t)return;let n=t.querySelector(".lh-buttons");n||(n=this._dom.createChildOf(t,"div","lh-buttons"));let r=["lh-button"];e.icon&&(r.push("lh-report-icon"),r.push(`lh-report-icon--${e.icon}`));let i=this._dom.createChildOf(n,"button",r.join(" "));return i.textContent=e.text,i.addEventListener("click",e.onClick),i}resetUIState(){this._topbar&&this._topbar.resetUIState()}getReportHtml(){if(!this._opts.getStandaloneReportHTML)throw new Error("`getStandaloneReportHTML` is not set");return this.resetUIState(),this._opts.getStandaloneReportHTML()}saveAsGist(){throw new Error("Cannot save as gist from base report")}_enableFireworks(){this._dom.find(".lh-scores-container",this._dom.rootEl).classList.add("lh-score100")}_setupMediaQueryListeners(){let e=self.matchMedia("(max-width: 500px)");e.addListener(this.onMediaQueryChange),this.onMediaQueryChange(e)}_resetUIState(){this._topbar&&this._topbar.resetUIState()}onMediaQueryChange(e){this._dom.rootEl.classList.toggle("lh-narrow",e.matches)}_setupThirdPartyFilter(){let e=["uses-rel-preconnect","third-party-facades"],t=["legacy-javascript"];Array.from(this._dom.rootEl.querySelectorAll("table.lh-table")).filter(i=>i.querySelector("td.lh-table-column--url, td.lh-table-column--source-location")).filter(i=>{let a=i.closest(".lh-audit");if(!a)throw new Error(".lh-table not within audit");return!e.includes(a.id)}).forEach(i=>{let a=St(i),l=a.filter(f=>!f.classList.contains("lh-sub-item-row")),s=this._getThirdPartyRows(l,E.getFinalDisplayedUrl(this.json)),c=a.some(f=>f.classList.contains("lh-row--even")),d=this._dom.createComponent("3pFilter"),p=this._dom.find("input",d);p.addEventListener("change",f=>{let g=f.target instanceof HTMLInputElement&&!f.target.checked,b=!0,w=l[0];for(;w;){let x=g&&s.includes(w);do w.classList.toggle("lh-row--hidden",x),c&&(w.classList.toggle("lh-row--even",!x&&b),w.classList.toggle("lh-row--odd",!x&&!b)),w=w.nextElementSibling;while(w&&w.classList.contains("lh-sub-item-row"));x||(b=!b)}});let h=s.filter(f=>!f.classList.contains("lh-row--group")).length;this._dom.find(".lh-3p-filter-count",d).textContent=`${h}`,this._dom.find(".lh-3p-ui-string",d).textContent=m.strings.thirdPartyResourcesLabel;let u=s.length===l.length,v=!s.length;if((u||v)&&(this._dom.find("div.lh-3p-filter",d).hidden=!0),!i.parentNode)return;i.parentNode.insertBefore(d,i);let _=i.closest(".lh-audit");if(!_)throw new Error(".lh-table not within audit");t.includes(_.id)&&!u&&p.click()})}_setupElementScreenshotOverlay(e){this._fullPageScreenshot&&G.installOverlayFeature({dom:this._dom,rootEl:e,overlayContainerEl:e,fullPageScreenshot:this._fullPageScreenshot})}_getThirdPartyRows(e,t){let n=E.getEntityFromUrl(t,this.json.entities),r=this.json.entities?.find(a=>a.isFirstParty===!0)?.name,i=[];for(let a of e){if(r){if(!a.dataset.entity||a.dataset.entity===r)continue}else{let l=a.querySelector("div.lh-text__url");if(!l)continue;let s=l.dataset.url;if(!s||!(E.getEntityFromUrl(s,this.json.entities)!==n))continue}i.push(a)}return i}_saveFile(e){let t=e.type.match("json")?".json":".html",n=De({finalDisplayedUrl:E.getFinalDisplayedUrl(this.json),fetchTime:this.json.fetchTime})+t;this._opts.onSaveFileOverride?this._opts.onSaveFileOverride(e,n):this._dom.saveFile(e,n)}};function Pe(o,e={}){let t=document.createElement("article");t.classList.add("lh-root","lh-vars");let n=new te(t.ownerDocument,t);return new oe(n).renderReport(o,t,e),new le(n,e).initFeatures(o),t}var se=class{constructor(e){this.el=e;let t=document.createElement("style");if(t.textContent=`
      #lh-log {
        position: fixed;
        background-color: #323232;
        color: #fff;
        min-height: 48px;
        min-width: 288px;
        padding: 16px 24px;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.26);
        border-radius: 2px;
        margin: 12px;
        font-size: 14px;
        cursor: default;
        transition: transform 0.3s, opacity 0.3s;
        transform: translateY(100px);
        opacity: 0;
        bottom: 0;
        left: 0;
        z-index: 3;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
      }
      
      #lh-log.lh-show {
        opacity: 1;
        transform: translateY(0);
      }
    `,!this.el.parentNode)throw new Error("element needs to be in the DOM");this.el.parentNode.insertBefore(t,this.el),this._id=void 0}log(e,t=!0){this._id&&clearTimeout(this._id),this.el.textContent=e,this.el.classList.add("lh-show"),t&&(this._id=setTimeout(()=>{this.el.classList.remove("lh-show")},7e3))}warn(e){this.log("Warning: "+e)}error(e){this.log(e),setTimeout(()=>{throw new Error(e)},0)}hide(){this._id&&clearTimeout(this._id),this.el.classList.remove("lh-show")}};function Ct(){let o=window.__LIGHTHOUSE_JSON__,e=Pe(o,{occupyEntireViewport:!0,getStandaloneReportHTML(){return document.documentElement.outerHTML}});document.body.append(e),document.addEventListener("lh-analytics",t=>{window.ga&&ga(t.detail.cmd,t.detail.fields)}),document.addEventListener("lh-log",t=>{let n=document.querySelector("div#lh-log");if(!n)return;let r=new se(n),i=t.detail;switch(i.cmd){case"log":r.log(i.msg);break;case"warn":r.warn(i.msg);break;case"error":r.error(i.msg);break;case"hide":r.hide();break}})}window.__initLighthouseReport__=Ct;})();
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2023 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2020 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license Copyright 2023 The Lighthouse Authors. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the specific language governing permissions and limitations under the License.
*/
/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
/**
 * @license
 * Copyright 2017 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 *
 * Dummy text for ensuring report robustness: <\/script> pre$`post %%LIGHTHOUSE_JSON%%
 * (this is handled by terser)
 */
/**
 * @license
 * Copyright 2021 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

  __initLighthouseReport__();
  //# sourceURL=compiled-reportrenderer.js
  </script>
  <script>console.log('window.__LIGHTHOUSE_JSON__', __LIGHTHOUSE_JSON__);</script>
</body>
</html>
