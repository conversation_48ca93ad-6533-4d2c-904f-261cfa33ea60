.dark .pass[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(16, 185, 129, var(--tw-text-opacity));
}
.pass[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(4, 120, 87, var(--tw-text-opacity));
}
.dark .average[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(245, 158, 11, var(--tw-text-opacity));
}
.average[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(180, 83, 9, var(--tw-text-opacity));
}
.dark .fail[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity));
}
.fail[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(185, 28, 28, var(--tw-text-opacity));
}
.na[data-v-61b2e920] {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.na .icon[data-v-61b2e920] {
  --tw-bg-opacity: 1;
  background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
  border-radius: 9999px;
}
.pass .icon[data-v-61b2e920] {
  --tw-bg-opacity: 1;
  background-color: rgba(4, 120, 87, var(--tw-bg-opacity));
  border-radius: 9999px;
}
.dark .pass .icon[data-v-61b2e920] {
  --tw-bg-opacity: 1;
  background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}
.average .icon[data-v-61b2e920] {
  --tw-bg-opacity: 1;
  background-color: rgba(180, 83, 9, var(--tw-bg-opacity));
}
.dark .average .icon[data-v-61b2e920] {
  --tw-bg-opacity: 1;
  background-color: rgba(245, 158, 11, var(--tw-bg-opacity));
}
.fail .icon[data-v-61b2e920] {
  --tw-border-opacity: 1;
  border-color: rgba(185, 28, 28, var(--tw-border-opacity));
  border-left: 0.25rem solid transparent;
  border-right: 0.25rem solid transparent;
  border-bottom: 0.5rem solid;
}
.dark .fail .icon[data-v-61b2e920] {
  --tw-border-opacity: 1;
  border-color: rgba(239, 68, 68, var(--tw-border-opacity));
}* {
  --color-amber-50: #fff8e1;
  --color-blue-200: #90caf9;
  --color-blue-900: #0d47a1;
  --color-blue-A700: #2962ff;
  --color-cyan-500: #00bcd4;
  --color-gray-100: #f5f5f5;
  --color-gray-300: #cfcfcf;
  --color-gray-200: #e0e0e0;
  --color-gray-400: #bdbdbd;
  --color-gray-50: #fafafa;
  --color-gray-500: #9e9e9e;
  --color-gray-600: #757575;
  --color-gray-700: #616161;
  --color-gray-800: #424242;
  --color-gray-900: #212121;
  --color-gray: #000000;
  --color-green-700: #018642;
  --color-green: #0cce6b;
  --color-lime-400: #d3e156;
  --color-orange-50: #fff3e0;
  --color-orange-700: #d04900;
  --color-orange: #ffa400;
  --color-red-700: #eb0f00;
  --color-red: #ff4e42;
  --color-teal-600: #00897b;
  --color-white: #ffffff;
  --color-average-secondary: var(--color-orange-700);
  --color-average: var(--color-orange);
  --color-fail-secondary: var(--color-red-700);
  --color-fail: var(--color-red);
  --color-hover: var(--color-gray-50);
  --color-informative: var(--color-blue-900);
  --color-pass-secondary: var(--color-green-700);
  --color-pass: var(--color-green);
  --color-not-applicable: var(--color-gray-600);
}
.guage__wrapper--pass {
  fill: currentColor;
  stroke: currentColor;
  --tw-text-opacity: 1;
  color: rgba(4, 120, 87, var(--tw-text-opacity));
}
.dark .guage__wrapper--pass {
  --tw-text-opacity: 1;
  color: rgba(16, 185, 129, var(--tw-text-opacity));
}
.guage__wrapper--average {
  fill: currentColor;
  stroke: currentColor;
  --tw-text-opacity: 1;
  color: rgba(180, 83, 9, var(--tw-text-opacity));
}
.dark .guage__wrapper--average {
  --tw-text-opacity: 1;
  color: rgba(245, 158, 11, var(--tw-text-opacity));
}
.guage__wrapper--fail {
  fill: currentColor;
  stroke: currentColor;
  --tw-text-opacity: 1;
  color: rgba(185, 28, 28, var(--tw-text-opacity));
}
.dark .guage__wrapper--fail {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity));
}
.guage__wrapper--not-applicable {
  color: var(--color-not-applicable);
  fill: var(--color-not-applicable);
  stroke: var(--color-not-applicable);
}
.guage__wrapper--huge {
  --gauge-circle-size: 40px;
}
.guage__wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column;
  text-decoration: none;
  padding: var(--score-container-padding);
  --transition-length: 1s;
  contain: content;
  will-change: opacity;
}
.guage__svg-wrapper {
  position: relative;
  height: var(--gauge-circle-size);
}
.guage {
  stroke-linecap: round;
  width: var(--gauge-circle-size);
  height: var(--gauge-circle-size);
}
.guage-base {
  opacity: 0.1;
}
.guage-arc {
  fill: none;
  transform-origin: 50% 50%;
  animation: load-gauge var(--transition-length) ease forwards;
  animation-delay: 250ms;
}.tooltip[data-v-09d3021d] {
  text-align: center;
  position: absolute;
  padding: 4px;
  z-index: 20;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}.tooltip[data-v-63feb3b2] {
  text-align: center;
  position: absolute;
  padding: 4px;
  z-index: 20;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}.tooltip[data-v-5986ad10] {
  text-align: center;
  position: absolute;
  padding: 4px;
  z-index: 20;
  top: 4px;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}/* windicss layer base */
[type='text'], [type='email'], [type='url'], [type='password'], [type='number'], [type='date'], [type='datetime-local'], [type='month'], [type='search'], [type='tel'], [type='time'], [type='week'], [multiple], textarea, select {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
}
[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  border-color: #2563eb;
}
[type='checkbox'], [type='radio'] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
}
[type='checkbox']:focus, [type='radio']:focus {
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  border-color: #6b7280;
}
[type='checkbox']:checked, [type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
[type='checkbox']:checked:hover, [type='checkbox']:checked:focus, [type='radio']:checked:hover, [type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}
[type='checkbox']:indeterminate:hover, [type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}
*, ::before, ::after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}
* {
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
}
:root {
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}
:-moz-focusring {
  outline: 1px dotted ButtonText;
}
:-moz-ui-invalid {
  box-shadow: none;
}
::moz-focus-inner {
  border-style: none;
  padding: 0;
}
::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
  height: auto;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}
::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}
::-webkit-date-and-time-value {
  min-height: 1.5em;
}
[type='search'] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
  print-color-adjust: unset;
}
[type='checkbox'] {
  border-radius: 0px;
}
[type='radio'] {
  border-radius: 100%;
}
[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}
[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}
[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}
[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}
abbr[title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}
a {
  color: inherit;
  text-decoration: inherit;
}
body {
  margin: 0;
  font-family: inherit;
  line-height: inherit;
}
button, input {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
  padding: 0;
  line-height: inherit;
  color: inherit;
}
button {
  text-transform: none;
  background-color: transparent;
  background-image: none;
}
button, [type='button'], [type='reset'], [type='submit'] {
  -webkit-appearance: button;
}
button, [role="button"] {
  cursor: pointer;
}
code, pre {
  font-size: 1em;
  font-family: DM Mono;
}
html {
  -webkit-text-size-adjust: 100%;
  font-family: DM Sans;
  line-height: 1.5;
}
h2, h3, h4 {
  font-size: inherit;
  font-weight: inherit;
}
img {
  border-style: solid;
  max-width: 100%;
  height: auto;
}
input::placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::webkit-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input:-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
input::-ms-input-placeholder {
  opacity: 1;
  color: #9ca3af;
}
iframe, img, svg {
  display: block;
  vertical-align: middle;
}
input::-moz-placeholder, textarea::-moz-placeholder {
  color: #6b7280;
  opacity: 1;
}
input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  color: #6b7280;
  opacity: 1;
}
input::placeholder, textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}
p, h2, pre, h3, h4 {
  margin: 0;
}
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}/* windicss layer components */
.border-main {
  --tw-border-opacity: 1;
  border-color: rgba(156, 163, 175, var(--tw-border-opacity));
  --tw-border-opacity: 0.3;
}/* windicss layer utilities */
.container {
  width: 100%;
}
@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.-space-x-px > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-1px * var(--tw-space-x-reverse));
  margin-left: calc(-1px * calc(1 - var(--tw-space-x-reverse)));
}
.dark .dark\:bg-teal-900 {
  --tw-bg-opacity: 1;
  background-color: rgba(21, 28, 35, var(--tw-bg-opacity));
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(219, 234, 254, var(--tw-bg-opacity));
}
.dark .dark\:bg-teal-700 {
  --tw-bg-opacity: 1;
  background-color: rgba(51, 69, 77, var(--tw-bg-opacity));
}
.hover\:bg-blue-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(59, 130, 246, var(--tw-bg-opacity));
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(107, 114, 128, var(--tw-bg-opacity));
}
.bg-green-700 {
  --tw-bg-opacity: 1;
  background-color: rgba(4, 120, 87, var(--tw-bg-opacity));
}
.dark .dark\:bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}
.bg-yellow-700 {
  --tw-bg-opacity: 1;
  background-color: rgba(180, 83, 9, var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-blue-900\/50:hover {
  --tw-bg-opacity: 0.5;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.hover\:bg-blue-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(191, 219, 254, var(--tw-bg-opacity));
}
.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-white\/\[0\.12\]:hover {
  --tw-bg-opacity: 0.12;
  background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.bg-blue-900\/30 {
  --tw-bg-opacity: 0.3;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 246, 255, var(--tw-bg-opacity));
}
.dark .dark\:bg-teal-700\/30 {
  --tw-bg-opacity: 0.3;
  background-color: rgba(51, 69, 77, var(--tw-bg-opacity));
}
.dark .dark\:hover\:bg-teal-700\/70:hover {
  --tw-bg-opacity: 0.7;
  background-color: rgba(51, 69, 77, var(--tw-bg-opacity));
}
.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(219, 234, 254, var(--tw-bg-opacity));
}
.bg-red-300 {
  --tw-bg-opacity: 1;
  background-color: rgba(252, 165, 165, var(--tw-bg-opacity));
}
.hover\:bg-blue-900\/50:hover {
  --tw-bg-opacity: 0.5;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.bg-blue-300 {
  --tw-bg-opacity: 1;
  background-color: rgba(147, 197, 253, var(--tw-bg-opacity));
}
.dark .dark\:bg-blue-900\/30 {
  --tw-bg-opacity: 0.3;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(247, 249, 249, var(--tw-bg-opacity));
}
.dark .dark\:bg-transparent {
  background-color: transparent;
}
.bg-gray-700 {
  --tw-bg-opacity: 1;
  background-color: rgba(55, 65, 81, var(--tw-bg-opacity));
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(16, 185, 129, var(--tw-bg-opacity));
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(245, 158, 11, var(--tw-bg-opacity));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgba(239, 68, 68, var(--tw-bg-opacity));
}
.bg-teal-100 {
  --tw-bg-opacity: 1;
  background-color: rgba(234, 241, 245, var(--tw-bg-opacity));
}
.bg-teal-50\/90 {
  --tw-bg-opacity: 0.9;
  background-color: rgba(247, 249, 249, var(--tw-bg-opacity));
}
.dark .dark\:bg-teal-900\/99 {
  --tw-bg-opacity: 0.99;
  background-color: rgba(21, 28, 35, var(--tw-bg-opacity));
}
.bg-blue-900\/10 {
  --tw-bg-opacity: 0.1;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.dark .dark\:even\:bg-teal-800\/50:nth-child(even) {
  --tw-bg-opacity: 0.5;
  background-color: rgba(35, 46, 54, var(--tw-bg-opacity));
}
.even\:bg-teal-50\/90:nth-child(even) {
  --tw-bg-opacity: 0.9;
  background-color: rgba(247, 249, 249, var(--tw-bg-opacity));
}
.dark .dark\:bg-blue-900\/20 {
  --tw-bg-opacity: 0.2;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.dark .dark\:bg-blue-900\/70 {
  --tw-bg-opacity: 0.7;
  background-color: rgba(30, 58, 138, var(--tw-bg-opacity));
}
.bg-transparent {
  background-color: transparent;
}
.bg-gray-900\/99 {
  --tw-bg-opacity: 0.99;
  background-color: rgba(17, 24, 39, var(--tw-bg-opacity));
}
.dark .dark\:bg-\[\#020617\] {
  --tw-bg-opacity: 1;
  background-color: rgba(2, 6, 23, var(--tw-bg-opacity));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgba(249, 250, 251, var(--tw-bg-opacity));
}
.dark .dark\:bg-none {
  background-image: none;
}
.bg-gradient-to-b {
  background-image: -o-linear-gradient(top, var(--tw-gradient-stops));
  background-image: -webkit-gradient(linear, left top, left bottom, from(var(--tw-gradient-stops)));
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.from-sky-50\/50 {
  --tw-gradient-from: rgba(240, 249, 255, 0.5);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0));
}
.to-sky-300\/50 {
  --tw-gradient-to: rgba(125, 211, 252, 0.5);
}
.bg-no-repeat {
  background-repeat: no-repeat;
}
.border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgba(185, 28, 28, var(--tw-border-opacity));
}
.border-yellow-600 {
  --tw-border-opacity: 1;
  border-color: rgba(217, 119, 6, var(--tw-border-opacity));
}
.dark .dark\:border-yellow-300\/80 {
  --tw-border-opacity: 0.8;
  border-color: rgba(252, 211, 77, var(--tw-border-opacity));
}
.border-blue-900\/30 {
  --tw-border-opacity: 0.3;
  border-color: rgba(30, 58, 138, var(--tw-border-opacity));
}
.border-blue-100 {
  --tw-border-opacity: 1;
  border-color: rgba(219, 234, 254, var(--tw-border-opacity));
}
.border-transparent {
  border-color: transparent;
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgba(229, 231, 235, var(--tw-border-opacity));
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-full {
  border-radius: 9999px;
}
.first\:rounded-s-md:first-child {
  border-radius: 0.375rem;
}
.last\:rounded-e-md:last-child {
  border-radius: 0.375rem;
}
.dark .dark\:border-none {
  border-style: none;
}
.border-2 {
  border-width: 2px;
}
.border {
  border-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.cursor-pointer {
  cursor: pointer;
}
.block {
  display: block;
}
.dark .dark\:block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.inline-flex {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: -ms-grid;
  display: grid;
}
.hidden {
  display: none;
}
.dark .dark\:hidden {
  display: none;
}
.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  -webkit-flex-direction: column;
  flex-direction: column;
}
.items-start {
  -webkit-box-align: start;
  -ms-flex-align: start;
  -webkit-align-items: flex-start;
  align-items: flex-start;
}
.items-end {
  -webkit-box-align: end;
  -ms-flex-align: end;
  -webkit-align-items: flex-end;
  align-items: flex-end;
}
.items-center {
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-align-items: center;
  align-items: center;
}
.justify-end {
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  -webkit-justify-content: flex-end;
  justify-content: flex-end;
}
.justify-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.justify-between {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}
.justify-around {
  -ms-flex-pack: distribute;
  -webkit-justify-content: space-around;
  justify-content: space-around;
}
.flex-grow {
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  -webkit-flex-grow: 1;
  flex-grow: 1;
}
.flex-shrink {
  -ms-flex-negative: 1;
  -webkit-flex-shrink: 1;
  flex-shrink: 1;
}
.font-mono {
  font-family: DM Mono;
}
.dark .dark\:font-bold {
  font-weight: 700;
}
.font-bold {
  font-weight: 700;
}
.font-medium {
  font-weight: 500;
}
.font-light {
  font-weight: 300;
}
.\!font-bold {
  font-weight: 700 !important;
}
.h-screen {
  height: 100vh;
}
.h-4 {
  height: 1rem;
}
.h-2 {
  height: 0.5rem;
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-5 {
  height: 1.25rem;
}
.h-3 {
  height: 0.75rem;
}
.h-\[200px\] {
  height: 200px;
}
.h-700px {
  height: 700px;
}
.h-\[1\.2em\] {
  height: 1.2em;
}
.h-30px {
  height: 30px;
}
.h-100px {
  height: 100px;
}
.h-112px {
  height: 112px;
}
.h-82px {
  height: 82px;
}
.h-8px {
  height: 8px;
}
.h-12px {
  height: 12px;
}
.h-200px {
  height: 200px;
}
.h-24px {
  height: 24px;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-\[10px\] {
  font-size: 10px;
  line-height: 1;
}
.leading-5 {
  line-height: 1.25rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.children\:my-auto > * {
  margin-top: auto;
  margin-bottom: auto;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mr-0 {
  margin-right: 0px;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mr-5 {
  margin-right: 1.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mr-15 {
  margin-right: 3.75rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mr-3 {
  margin-right: 0.75rem;
}
.mr-6 {
  margin-right: 1.5rem;
}
.max-h-screen {
  max-height: 100vh;
}
.max-h-\[95\%\] {
  max-height: 95%;
}
.max-h-\[calc\(100vh-250px\)\] {
  max-height: calc(100vh - 250px);
}
.max-h-80px {
  max-height: 80px;
}
.max-h-120px {
  max-height: 120px;
}
.max-h-\[500px\] {
  max-height: 500px;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-full {
  max-width: 100%;
}
.max-w-\[10\%\] {
  max-width: 10%;
}
.max-w-\[500px\] {
  max-width: 500px;
}
.min-h-screen {
  min-height: 100vh;
}
.min-h-\[100px\] {
  min-height: 100px;
}
.min-w-1500px {
  min-width: 1500px;
}
.min-w-320px {
  min-width: 320px;
}
.object-contain {
  -o-object-fit: contain;
  object-fit: contain;
}
.object-top {
  -o-object-position: top;
  object-position: top;
}
.object-left {
  -o-object-position: left;
  object-position: left;
}
.opacity-40 {
  opacity: 0.4;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.hover\:opacity-100:hover {
  opacity: 1;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-90 {
  opacity: 0.9;
}
.dark .dark\:opacity-80 {
  opacity: 0.8;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-55 {
  opacity: 0.55;
}
.group:focus .group-focus\:opacity-100 {
  opacity: 1;
}
.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.\!outline-none {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
.outline-green-200 {
  --tw-outline-opacity: 1;
  outline-color: rgba(167, 243, 208, var(--tw-outline-opacity));
}
.outline {
  outline-style: solid;
}
.outline-2 {
  outline-width: 2px;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}
.overflow-y-hidden {
  overflow-y: hidden;
}
.p-6 {
  padding: 1.5rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-0\.3 {
  padding: 0.075rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pb-1 {
  padding-bottom: 0.25rem;
}
.pr-3 {
  padding-right: 0.75rem;
}
.pt-1 {
  padding-top: 0.25rem;
}
.pl-7 {
  padding-left: 1.75rem;
}
.tab {
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
}
.pointer-events-none {
  pointer-events: none;
}
.static {
  position: static;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}
.left-\[50\%\] {
  left: 50%;
}
.top-\[50\%\] {
  top: 50%;
}
.left-1\/2 {
  left: 50%;
}
.right-0 {
  right: 0px;
}
.top-2\/3 {
  top: 66.666667%;
}
.left-2 {
  left: 0.5rem;
}
.-left-80px {
  left: -80px;
}
.top-\[99\%\] {
  top: 99%;
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0/0.1),0 8px 10px -6px rgb(0 0 0/0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color),0 8px 10px -6px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
  box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0/0.1),0 1px 2px -1px rgb(0 0 0/0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
  box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0/0.1),0 2px 4px -2px rgb(0 0 0/0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
  box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0/0.1),0 4px 6px -4px rgb(0 0 0/0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
  box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0/0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
  box-shadow: var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow);
}
.focus-visible\:ring:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-1:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-1:hover {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-offset-blue-400 {
  --tw-ring-offset-opacity: 1;
  --tw-ring-offset-color: rgba(96, 165, 250, var(--tw-ring-offset-opacity));
}
.focus-visible\:ring-white:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(255, 255, 255, var(--tw-ring-opacity));
}
.ring-white {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(255, 255, 255, var(--tw-ring-opacity));
}
.focus-visible\:ring-blue-500:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(59, 130, 246, var(--tw-ring-opacity));
}
.ring-black {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(0, 0, 0, var(--tw-ring-opacity));
}
.ring-blue-200 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(191, 219, 254, var(--tw-ring-opacity));
}
.focus-visible\:ring-teal-800:focus-visible {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgba(35, 46, 54, var(--tw-ring-opacity));
}
.focus-visible\:ring-opacity-75:focus-visible {
  --tw-ring-opacity: 0.75;
}
.ring-opacity-60 {
  --tw-ring-opacity: 0.6;
}
.ring-opacity-5 {
  --tw-ring-opacity: 0.05;
}
.fill-current {
  fill: currentColor;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.dark .dark\:text-gray-200 {
  --tw-text-opacity: 1;
  color: rgba(229, 231, 235, var(--tw-text-opacity));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgba(16, 185, 129, var(--tw-text-opacity));
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgba(245, 158, 11, var(--tw-text-opacity));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity));
}
.dark .dark\:text-gray-300 {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity));
}
.children\:hover\:text-white:hover > * {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgba(59, 130, 246, var(--tw-text-opacity));
}
.dark .dark\:text-green-500 {
  --tw-text-opacity: 1;
  color: rgba(16, 185, 129, var(--tw-text-opacity));
}
.dark .dark\:text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgba(245, 158, 11, var(--tw-text-opacity));
}
.dark .dark\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgba(239, 68, 68, var(--tw-text-opacity));
}
.hover\:text-teal-600:hover {
  --tw-text-opacity: 1;
  color: rgba(65, 92, 102, var(--tw-text-opacity));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgba(29, 78, 216, var(--tw-text-opacity));
}
.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgba(191, 219, 254, var(--tw-text-opacity));
}
.dark .dark\:text-blue-100 {
  --tw-text-opacity: 1;
  color: rgba(219, 234, 254, var(--tw-text-opacity));
}
.dark .dark\:hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-blue-900\/70 {
  --tw-text-opacity: 0.7;
  color: rgba(30, 58, 138, var(--tw-text-opacity));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgba(185, 28, 28, var(--tw-text-opacity));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgba(75, 85, 99, var(--tw-text-opacity));
}
.dark .dark\:text-gray-400 {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgba(217, 119, 6, var(--tw-text-opacity));
}
.dark .dark\:text-yellow-300\/80 {
  --tw-text-opacity: 0.8;
  color: rgba(252, 211, 77, var(--tw-text-opacity));
}
.hover\:text-blue-700:hover {
  --tw-text-opacity: 1;
  color: rgba(29, 78, 216, var(--tw-text-opacity));
}
.dark .dark\:hover\:text-blue-50:hover {
  --tw-text-opacity: 1;
  color: rgba(239, 246, 255, var(--tw-text-opacity));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgba(4, 120, 87, var(--tw-text-opacity));
}
.text-yellow-700 {
  --tw-text-opacity: 1;
  color: rgba(180, 83, 9, var(--tw-text-opacity));
}
.text-teal-700 {
  --tw-text-opacity: 1;
  color: rgba(51, 69, 77, var(--tw-text-opacity));
}
.dark .dark\:text-teal-200 {
  --tw-text-opacity: 1;
  color: rgba(208, 224, 233, var(--tw-text-opacity));
}
.text-teal-900 {
  --tw-text-opacity: 1;
  color: rgba(21, 28, 35, var(--tw-text-opacity));
}
.dark .dark\:text-teal-100 {
  --tw-text-opacity: 1;
  color: rgba(234, 241, 245, var(--tw-text-opacity));
}
.text-teal-100 {
  --tw-text-opacity: 1;
  color: rgba(234, 241, 245, var(--tw-text-opacity));
}
.text-white {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-teal-800 {
  --tw-text-opacity: 1;
  color: rgba(35, 46, 54, var(--tw-text-opacity));
}
.dark .dark\:text-teal-300 {
  --tw-text-opacity: 1;
  color: rgba(162, 192, 204, var(--tw-text-opacity));
}
.dark .dark\:text-gray-100 {
  --tw-text-opacity: 1;
  color: rgba(243, 244, 246, var(--tw-text-opacity));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgba(31, 41, 55, var(--tw-text-opacity));
}
.text-blue-300 {
  --tw-text-opacity: 1;
  color: rgba(147, 197, 253, var(--tw-text-opacity));
}
.text-red-400 {
  --tw-text-opacity: 1;
  color: rgba(248, 113, 113, var(--tw-text-opacity));
}
.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgba(251, 191, 36, var(--tw-text-opacity));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.hover\:text-opacity-100:hover {
  --tw-text-opacity: 1;
}
.group:hover .group-hover\:text-opacity-80 {
  --tw-text-opacity: 0.8;
}
.uppercase {
  text-transform: uppercase;
}
.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}
.hover\:underline:hover {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}
.hover\:no-underline:hover {
  text-decoration: none;
}
.no-underline {
  text-decoration: none;
}
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.align-middle {
  vertical-align: middle;
}
.visible {
  visibility: visible;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.break-all {
  word-break: break-all;
}
.w-full {
  width: 100%;
}
.w-4 {
  width: 1rem;
}
.w-auto {
  width: auto;
}
.w-2 {
  width: 0.5rem;
}
.w-5 {
  width: 1.25rem;
}
.w-3 {
  width: 0.75rem;
}
.w-\[400px\] {
  width: 400px;
}
.w-300px {
  width: 300px;
}
.w-1200px {
  width: 1200px;
}
.w-68px {
  width: 68px;
}
.w-112px {
  width: 112px;
}
.w-250px {
  width: 250px;
}
.w-200px {
  width: 200px;
}
.w-24px {
  width: 24px;
}
.w-225px {
  width: 225px;
}
.z-10 {
  z-index: 10;
}
.z-500 {
  z-index: 500;
}
.gap-4 {
  grid-gap: 1rem;
  gap: 1rem;
}
.gap-2 {
  grid-gap: 0.5rem;
  gap: 0.5rem;
}
.gap-1 {
  grid-gap: 0.25rem;
  gap: 0.25rem;
}
.gap-3 {
  grid-gap: 0.75rem;
  gap: 0.75rem;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}
.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}
.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}
.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.col-span-1 {
  -ms-grid-column-span: span 1 / span 1;
  grid-column: span 1 / span 1;
}
.col-span-2 {
  -ms-grid-column-span: span 2 / span 2;
  grid-column: span 2 / span 2;
}
.col-span-3 {
  -ms-grid-column-span: span 3 / span 3;
  grid-column: span 3 / span 3;
}
.col-span-4 {
  -ms-grid-column-span: span 4 / span 4;
  grid-column: span 4 / span 4;
}
.col-span-5 {
  -ms-grid-column-span: span 5 / span 5;
  grid-column: span 5 / span 5;
}
.col-span-6 {
  -ms-grid-column-span: span 6 / span 6;
  grid-column: span 6 / span 6;
}
.col-span-7 {
  -ms-grid-column-span: span 7 / span 7;
  grid-column: span 7 / span 7;
}
.grid-rows-\[min-content\2c 1fr\] {
  grid-template-rows: min-content 1fr;
}
.transform {
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-translate-z: 0;
  --tw-rotate: 0;
  --tw-rotate-x: 0;
  --tw-rotate-y: 0;
  --tw-rotate-z: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-scale-z: 1;
  -webkit-transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) translateZ(var(--tw-translate-z)) rotate(var(--tw-rotate)) rotateX(var(--tw-rotate-x)) rotateY(var(--tw-rotate-y)) rotateZ(var(--tw-rotate-z)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) scaleZ(var(--tw-scale-z));
  -ms-transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) translateZ(var(--tw-translate-z)) rotate(var(--tw-rotate)) rotateX(var(--tw-rotate-x)) rotateY(var(--tw-rotate-y)) rotateZ(var(--tw-rotate-z)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) scaleZ(var(--tw-scale-z));
  transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) translateZ(var(--tw-translate-z)) rotate(var(--tw-rotate)) rotateX(var(--tw-rotate-x)) rotateY(var(--tw-rotate-y)) rotateZ(var(--tw-rotate-z)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) scaleZ(var(--tw-scale-z));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  --tw-scale-z: .95;
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-scale-z: 1;
}
.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  --tw-scale-z: 1.1;
}
.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  --tw-scale-z: 1.25;
}
.scale-50 {
  --tw-scale-x: .5;
  --tw-scale-y: .5;
  --tw-scale-z: .5;
}
.rotate-180 {
  --tw-rotate: 180deg;
}
.rotate-270 {
  --tw-rotate: 270deg;
}
.translate-y-3 {
  --tw-translate-y: 0.75rem;
}
.translate-y-1 {
  --tw-translate-y: 0.25rem;
}
.translate-y-0 {
  --tw-translate-y: 0px;
}
.-translate-y-\[50\%\] {
  --tw-translate-y: -50%;
}
.-translate-x-\[50\%\] {
  --tw-translate-x: -50%;
}
.-translate-x-\[110\%\] {
  --tw-translate-x: -110%;
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
}
.transition {
  -webkit-transition-property: background-color, border-color, color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, filter, backdrop-filter;
  -o-transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, -webkit-box-shadow, transform, -webkit-transform, filter, backdrop-filter;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.transition-all {
  -webkit-transition-property: all;
  -o-transition-property: all;
  transition-property: all;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.\<transition {
  -webkit-transition-property: background-color, border-color, color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, filter, backdrop-filter;
  -o-transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, -webkit-box-shadow, transform, -webkit-transform, filter, backdrop-filter;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.ease-out {
  -webkit-transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-in {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -o-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.duration-300 {
  -webkit-transition-duration: 300ms;
  -o-transition-duration: 300ms;
  transition-duration: 300ms;
}
.duration-200 {
  -webkit-transition-duration: 200ms;
  -o-transition-duration: 200ms;
  transition-duration: 200ms;
}
.duration-100 {
  -webkit-transition-duration: 100ms;
  -o-transition-duration: 100ms;
  transition-duration: 100ms;
}
.duration-75 {
  -webkit-transition-duration: 75ms;
  -o-transition-duration: 75ms;
  transition-duration: 75ms;
}
.duration-2000 {
  -webkit-transition-duration: 2000ms;
  -o-transition-duration: 2000ms;
  transition-duration: 2000ms;
}
.duration-500 {
  -webkit-transition-duration: 500ms;
  -o-transition-duration: 500ms;
  transition-duration: 500ms;
}
.duration-150 {
  -webkit-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
.duration-\[400ms\] {
  -webkit-transition-duration: 400ms;
  -o-transition-duration: 400ms;
  transition-duration: 400ms;
}
.duration {
  -webkit-transition-duration: 150ms;
  -o-transition-duration: 150ms;
  transition-duration: 150ms;
}
@keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@-webkit-keyframes spin {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
@-webkit-keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}
.animate-spin {
  -webkit-animation: spin 1s linear infinite;
  animation: spin 1s linear infinite;
}
.animate-pulse {
  -webkit-animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.animated {
  -webkit-animation-duration: 1000ms;
  -webkit-animation-fill-mode: both;
  animation-duration: 1000ms;
  animation-fill-mode: both;
}
@media (min-width: 640px) {
  .sm\:block {
    display: block;
  }
  .sm\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
  }
  .sm\:max-h-\[calc\(100vh-220px\)\] {
    max-height: calc(100vh - 220px);
  }
  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }
  .sm\:px-7 {
    padding-left: 1.75rem;
    padding-right: 1.75rem;
  }
}
@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:inline {
    display: inline;
  }
  .md\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
  }
  .md\:flex-auto {
    -webkit-box-flex: 1;
    -ms-flex: 1 1 auto;
    -webkit-flex: 1 1 auto;
    flex: 1 1 auto;
  }
  .md\:ml-3 {
    margin-left: 0.75rem;
  }
  .md\:ml-5 {
    margin-left: 1.25rem;
  }
  .md\:mr-10 {
    margin-right: 2.5rem;
  }
  .md\:mr-5 {
    margin-right: 1.25rem;
  }
  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .md\:px-10 {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
  .md\:col-span-1 {
    -ms-grid-column-span: span 1 / span 1;
    grid-column: span 1 / span 1;
  }
  .md\:col-span-2 {
    -ms-grid-column-span: span 2 / span 2;
    grid-column: span 2 / span 2;
  }
  .md\:col-span-3 {
    -ms-grid-column-span: span 3 / span 3;
    grid-column: span 3 / span 3;
  }
  .md\:col-span-4 {
    -ms-grid-column-span: span 4 / span 4;
    grid-column: span 4 / span 4;
  }
  .md\:col-span-5 {
    -ms-grid-column-span: span 5 / span 5;
    grid-column: span 5 / span 5;
  }
  .md\:col-span-6 {
    -ms-grid-column-span: span 6 / span 6;
    grid-column: span 6 / span 6;
  }
  .md\:col-span-7 {
    -ms-grid-column-span: span 7 / span 7;
    grid-column: span 7 / span 7;
  }
}
@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  .lg\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    -webkit-flex-direction: row;
    flex-direction: row;
  }
  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  .lg\:max-h-\[calc\(100vh-205px\)\] {
    max-height: calc(100vh - 205px);
  }
  .lg\:p-2 {
    padding: 0.5rem;
  }
  .lg\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
  .lg\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
  .lg\:col-span-1 {
    -ms-grid-column-span: span 1 / span 1;
    grid-column: span 1 / span 1;
  }
  .lg\:col-span-2 {
    -ms-grid-column-span: span 2 / span 2;
    grid-column: span 2 / span 2;
  }
  .lg\:col-span-3 {
    -ms-grid-column-span: span 3 / span 3;
    grid-column: span 3 / span 3;
  }
  .lg\:col-span-4 {
    -ms-grid-column-span: span 4 / span 4;
    grid-column: span 4 / span 4;
  }
  .lg\:col-span-5 {
    -ms-grid-column-span: span 5 / span 5;
    grid-column: span 5 / span 5;
  }
  .lg\:col-span-6 {
    -ms-grid-column-span: span 6 / span 6;
    grid-column: span 6 / span 6;
  }
  .lg\:col-span-7 {
    -ms-grid-column-span: span 7 / span 7;
    grid-column: span 7 / span 7;
  }
}
@media (min-width: 1280px) {
  .xl\:block {
    display: block;
  }
  .xl\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
  }
  .xl\:ml-3 {
    margin-left: 0.75rem;
  }
  .xl\:w-full {
    width: 100%;
  }
  .xl\:col-span-1 {
    -ms-grid-column-span: span 1 / span 1;
    grid-column: span 1 / span 1;
  }
  .xl\:col-span-2 {
    -ms-grid-column-span: span 2 / span 2;
    grid-column: span 2 / span 2;
  }
  .xl\:col-span-3 {
    -ms-grid-column-span: span 3 / span 3;
    grid-column: span 3 / span 3;
  }
  .xl\:col-span-4 {
    -ms-grid-column-span: span 4 / span 4;
    grid-column: span 4 / span 4;
  }
  .xl\:col-span-5 {
    -ms-grid-column-span: span 5 / span 5;
    grid-column: span 5 / span 5;
  }
  .xl\:col-span-6 {
    -ms-grid-column-span: span 6 / span 6;
    grid-column: span 6 / span 6;
  }
  .xl\:col-span-7 {
    -ms-grid-column-span: span 7 / span 7;
    grid-column: span 7 / span 7;
  }
}
@media (min-width: 1536px) {
  .\32 xl\:space-x-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(0px * var(--tw-space-x-reverse));
    margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
  }
  .\32 xl\:block {
    display: block;
  }
  .\32 xl\:flex {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
  }
  .\32 xl\:hidden {
    display: none;
  }
  .\32 xl\:mr-5 {
    margin-right: 1.25rem;
  }
  .\32 xl\:mb-0 {
    margin-bottom: 0px;
  }
  .\32 xl\:mt-4 {
    margin-top: 1rem;
  }
  .\32 xl\:max-h-\[calc\(100vh-100px\)\] {
    max-height: calc(100vh - 100px);
  }
  .\32 xl\:text-left {
    text-align: left;
  }
  .\32 xl\:w-250px {
    width: 250px;
  }
}