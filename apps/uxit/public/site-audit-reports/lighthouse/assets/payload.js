window.__unlighthouse_payload = {"reports":[{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"6666cd","url":"https://lighthouse.storage","$url":"https://lighthouse.storage/","path":"/","definition":{"name":"_index","path":"/"}},"reportId":"6666cd","artifactPath":"","artifactUrl":"/site-audit-reports/lighthouse/reports","tasksTime":{"inspectHtmlTask":1746542342741,"runLighthouseTask":1746542344575},"seo":{"favicon":"/favicon.ico","title":"Lighthouse Storage - Store Data Permanently & Securely","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","og":{"image":"https://gateway.lighthouse.storage/ipfs/Qmd7rR9EPKomhmoRUw2WB7FJAeSWAtC8c1nkKgGZL39LpB","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","title":"Lighthouse Storage - Store Data Permanently & Securely"},"internalLinks":0,"externalLinks":2,"htmlSize":136384},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.75},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.89},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:39:32.575Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":null,"scoreDisplayMode":"notApplicable","metricSavings":{"CLS":0},"details":{"type":"table","headings":[],"items":[]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"7,100 ms","metricSavings":{"LCP":4600},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,SPAN,0,DIV,0,SPAN,0,IMG","selector":"span.HomeBanner_Banner__bgImg1__7G89r > div.ImageBox_imageRatioContainer__1Zigy > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":95,"bottom":424,"left":-70,"right":260,"width":330,"height":330},"snippet":"<img alt=\"icon\" src=\"https://lighthouse.storage/_next/image?url=%2FBannerImages%2Fdark_bannerIm…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=%2FBannerImages%2Fdark_bannerImg_1.png&amp;w=640&amp;q=75 640w, /…\">","nodeLabel":"icon"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":655.8319,"percent":"9%"},{"phase":"Load Delay","timing":5436.************,"percent":"77%"},{"phase":"Load Time","timing":569.1996965271364,"percent":"8%"},{"phase":"Render Delay","timing":440.75035335583016,"percent":"6%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.05,"scoreDisplayMode":"numeric","numericValue":7102.095299999999,"numericUnit":"millisecond","displayValue":"7.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":1,"scoreDisplayMode":"numeric","numericValue":0,"numericUnit":"unitless","displayValue":"0","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0,"newEngineResult":{"cumulativeLayoutShift":0,"cumulativeLayoutShiftMainFrame":0},"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.9,"scoreDisplayMode":"numeric","numericValue":1765.7548000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":4.5,"numericUnit":"millisecond","displayValue":"0 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":59,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.22,"scoreDisplayMode":"numeric","numericValue":10701.921675000001,"numericUnit":"millisecond","displayValue":"10.7 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181951908077,"data":"reports/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181952283077,"data":"reports/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181952658077,"data":"reports/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181953033077,"data":"reports/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181953408077,"data":"reports/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181953783077,"data":"reports/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181954158077,"data":"reports/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181954533077,"data":"reports/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1765.7548000000002,"numericUnit":"millisecond","displayValue":"1.8 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors do not have a sufficient contrast ratio.","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,DIV,0,DIV,2,P","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerLeft__rU_bF > p.LighthouseSuit_description__yQosg","boundingRect":{"top":7106,"bottom":7170,"left":16,"right":396,"width":380,"height":65},"snippet":"<p class=\"LighthouseSuit_description__yQosg\">","nodeLabel":"Create Data DAOs that generate yield and ensure long-term data preservation thr…","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 4.22 (foreground color: #667085, background color: #000000, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1"},"subItems":{"type":"subitems","items":[{"relatedNode":{"type":"node","lhId":"1-1-BODY","path":"1,HTML,1,BODY","selector":"body","boundingRect":{"top":0,"bottom":19735,"left":0,"right":412,"width":412,"height":19735},"snippet":"<body data-aos-easing=\"ease\" data-aos-duration=\"400\" data-aos-delay=\"0\">","nodeLabel":"body"}}]}},{"node":{"type":"node","lhId":"1-2-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,0,DIV,0,DIV,2,P","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerLeft__rU_bF > p.LighthouseSuit_description__yQosg","boundingRect":{"top":8282,"bottom":8389,"left":16,"right":396,"width":380,"height":108},"snippet":"<p class=\"LighthouseSuit_description__yQosg\">","nodeLabel":"Log in to our web application and access your files with ease. Our app provides…","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 4.22 (foreground color: #667085, background color: #000000, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1"},"subItems":{"type":"subitems","items":[{"relatedNode":{"type":"node","lhId":"1-1-BODY","path":"1,HTML,1,BODY","selector":"body","boundingRect":{"top":0,"bottom":19735,"left":0,"right":412,"width":412,"height":19735},"snippet":"<body data-aos-easing=\"ease\" data-aos-duration=\"400\" data-aos-delay=\"0\">","nodeLabel":"body"}}]}},{"node":{"type":"node","lhId":"1-3-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,0,DIV,0,DIV,2,P","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerLeft__rU_bF > p.LighthouseSuit_description__yQosg","boundingRect":{"top":9618,"bottom":9682,"left":16,"right":396,"width":380,"height":65},"snippet":"<p class=\"LighthouseSuit_description__yQosg\">","nodeLabel":"Developers can easily integrate our SDK & CLI into their application pipeline i…","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 4.22 (foreground color: #667085, background color: #000000, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1"},"subItems":{"type":"subitems","items":[{"relatedNode":{"type":"node","lhId":"1-1-BODY","path":"1,HTML,1,BODY","selector":"body","boundingRect":{"top":0,"bottom":19735,"left":0,"right":412,"width":412,"height":19735},"snippet":"<body data-aos-easing=\"ease\" data-aos-duration=\"400\" data-aos-delay=\"0\">","nodeLabel":"body"}}]}},{"node":{"type":"node","lhId":"1-4-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,0,DIV,0,DIV,2,P","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerLeft__rU_bF > p.LighthouseSuit_description__yQosg","boundingRect":{"top":10974,"bottom":11038,"left":16,"right":396,"width":380,"height":65},"snippet":"<p class=\"LighthouseSuit_description__yQosg\">","nodeLabel":"Use our encryption sdk to easily store private files and token gate them with o…","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 4.22 (foreground color: #667085, background color: #000000, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1"},"subItems":{"type":"subitems","items":[{"relatedNode":{"type":"node","lhId":"1-1-BODY","path":"1,HTML,1,BODY","selector":"body","boundingRect":{"top":0,"bottom":19735,"left":0,"right":412,"width":412,"height":19735},"snippet":"<body data-aos-easing=\"ease\" data-aos-duration=\"400\" data-aos-delay=\"0\">","nodeLabel":"body"}}]}},{"node":{"type":"node","lhId":"1-5-INPUT","path":"1,HTML,1,BODY,0,DIV,0,DIV,10,DIV,0,DIV,3,DIV,1,INPUT","selector":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","boundingRect":{"top":19484,"bottom":19524,"left":16,"right":396,"width":380,"height":40},"snippet":"<input type=\"text\" placeholder=\"<EMAIL>\">","nodeLabel":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 2.56 (foreground color: #a1a1aa, background color: #ffffff, font size: 10.8pt (14.4px), font weight: normal). Expected contrast ratio of 4.5:1"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.color","wcag2aa","wcag143","TTv5","TT13.c","EN-301-549","EN-*******","ACT"]}}},"heading-order":{"id":"heading-order","title":"Heading elements are not in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-6-H5","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,1,DIV,0,DIV,1,H5","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_features__QL54W > div.LighthouseSuit_featureCard___4Yw_ > h5.LighthouseSuit_featureTitle__x0qSD","boundingRect":{"top":7582,"bottom":7606,"left":32,"right":380,"width":348,"height":24},"snippet":"<h5 class=\"LighthouseSuit_featureTitle__x0qSD\">","nodeLabel":"Governance & Yield","explanation":"Fix any of the following:\n  Heading order invalid"}},{"node":{"type":"node","lhId":"1-7-H5","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,1,DIV,0,DIV,1,H5","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_features__QL54W > div.LighthouseSuit_featureCard___4Yw_ > h5.LighthouseSuit_featureTitle__x0qSD","boundingRect":{"top":8886,"bottom":8910,"left":32,"right":380,"width":348,"height":24},"snippet":"<h5 class=\"LighthouseSuit_featureTitle__x0qSD\">","nodeLabel":"Seamless Access","explanation":"Fix any of the following:\n  Heading order invalid"}},{"node":{"type":"node","lhId":"1-8-H5","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,1,DIV,0,DIV,1,H5","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_features__QL54W > div.LighthouseSuit_featureCard___4Yw_ > h5.LighthouseSuit_featureTitle__x0qSD","boundingRect":{"top":10183,"bottom":10207,"left":32,"right":380,"width":348,"height":24},"snippet":"<h5 class=\"LighthouseSuit_featureTitle__x0qSD\">","nodeLabel":"Lighthouse SDK","explanation":"Fix any of the following:\n  Heading order invalid"}},{"node":{"type":"node","lhId":"1-9-H5","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,1,DIV,0,DIV,1,H5","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_features__QL54W > div.LighthouseSuit_featureCard___4Yw_ > h5.LighthouseSuit_featureTitle__x0qSD","boundingRect":{"top":11493,"bottom":11517,"left":32,"right":380,"width":348,"height":24},"snippet":"<h5 class=\"LighthouseSuit_featureTitle__x0qSD\">","nodeLabel":"Threshold Encryption","explanation":"Fix any of the following:\n  Heading order invalid"}}],"debugData":{"type":"debugdata","impact":"moderate","tags":["cat.semantics","best-practice"]}}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/launcher_settings","urlProvider":"network","line":0,"column":0}},{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/ping","urlProvider":"network","line":0,"column":0}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"url":"https://lighthouse.storage/client/screenshot/eternal-ai.png","node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,1,DIV,0,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_highlightWrapper__oqXVr > div.EcosystemGrid_highlightCard__eRY_f > img.EcosystemGrid_highlightImage___PiUJ","boundingRect":{"top":12423,"bottom":12600,"left":49,"right":363,"width":314,"height":177},"snippet":"<img src=\"/client/screenshot/eternal-ai.png\" alt=\"Eternal AI\" class=\"EcosystemGrid_highlightImage___PiUJ\">","nodeLabel":"Eternal AI"}},{"url":"https://lighthouse.storage/suitsIcon/web_app.png","node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":8488,"bottom":8784,"left":16,"right":396,"width":380,"height":297},"snippet":"<img src=\"/suitsIcon/web_app.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"}},{"url":"https://lighthouse.storage/suitsIcon/data_daos.png","node":{"type":"node","lhId":"1-44-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":7269,"bottom":7481,"left":16,"right":396,"width":380,"height":212},"snippet":"<img src=\"/suitsIcon/data_daos.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"}},{"url":"https://lighthouse.storage/suitsIcon/sdk_cli.png","node":{"type":"node","lhId":"1-46-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":9781,"bottom":10082,"left":16,"right":396,"width":380,"height":301},"snippet":"<img src=\"/suitsIcon/sdk_cli.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"}},{"url":"https://lighthouse.storage/suitsIcon/encryption.png","node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":11137,"bottom":11391,"left":16,"right":396,"width":380,"height":255},"snippet":"<img src=\"/suitsIcon/encryption.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"}},{"url":"https://lighthouse.storage/client/singularity.svg","node":{"type":"node","lhId":"1-50-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,2,DIV,0,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_cardGrid__hLGtE > div.EcosystemGrid_card__HU1sQ > img.EcosystemGrid_logo__MfDtV","boundingRect":{"top":12905,"bottom":12945,"left":57,"right":215,"width":158,"height":40},"snippet":"<img src=\"/client/singularity.svg\" alt=\"Singularity\" class=\"EcosystemGrid_logo__MfDtV\" style=\"filter: brightness(100%);\">","nodeLabel":"Singularity"}},{"url":"https://lighthouse.storage/client/nuklai.svg","node":{"type":"node","lhId":"1-54-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,2,DIV,4,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_cardGrid__hLGtE > div.EcosystemGrid_card__HU1sQ > img.EcosystemGrid_logo__MfDtV","boundingRect":{"top":13900,"bottom":13940,"left":57,"right":178,"width":121,"height":40},"snippet":"<img src=\"/client/nuklai.svg\" alt=\"nuklai\" class=\"EcosystemGrid_logo__MfDtV\" style=\"filter: brightness(100%);\">","nodeLabel":"nuklai"}},{"url":"https://lighthouse.storage/client/bagel.svg","node":{"type":"node","lhId":"1-53-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,2,DIV,3,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_cardGrid__hLGtE > div.EcosystemGrid_card__HU1sQ > img.EcosystemGrid_logo__MfDtV","boundingRect":{"top":13621,"bottom":13661,"left":57,"right":189,"width":132,"height":40},"snippet":"<img src=\"/client/bagel.svg\" alt=\"Open Bagel\" class=\"EcosystemGrid_logo__MfDtV\" style=\"filter: brightness(100%);\">","nodeLabel":"Open Bagel"}},{"url":"https://lighthouse.storage/client/ocean.svg","node":{"type":"node","lhId":"1-51-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,2,DIV,1,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_cardGrid__hLGtE > div.EcosystemGrid_card__HU1sQ > img.EcosystemGrid_logo__MfDtV","boundingRect":{"top":13143,"bottom":13183,"left":57,"right":257,"width":200,"height":40},"snippet":"<img src=\"/client/ocean.svg\" alt=\"Ocean Protocol\" class=\"EcosystemGrid_logo__MfDtV\" style=\"filter: brightness(100%);\">","nodeLabel":"Ocean Protocol"}},{"url":"https://lighthouse.storage/client/top-ai.svg","node":{"type":"node","lhId":"1-52-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,2,DIV,2,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_cardGrid__hLGtE > div.EcosystemGrid_card__HU1sQ > img.EcosystemGrid_logo__MfDtV","boundingRect":{"top":13382,"bottom":13422,"left":57,"right":313,"width":256,"height":40},"snippet":"<img src=\"/client/top-ai.svg\" alt=\"TopAI Network\" class=\"EcosystemGrid_logo__MfDtV\" style=\"filter: brightness(100%);\">","nodeLabel":"TopAI Network"}},{"node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,1,DIV,0,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_highlightWrapper__oqXVr > div.EcosystemGrid_highlightCard__eRY_f > img.EcosystemGrid_highlightImage___PiUJ","boundingRect":{"top":12423,"bottom":12600,"left":49,"right":363,"width":314,"height":177},"snippet":"<img src=\"/client/screenshot/eternal-ai.png\" alt=\"Eternal AI\" class=\"EcosystemGrid_highlightImage___PiUJ\">","nodeLabel":"Eternal AI"},"url":"https://lighthouse.storage/client/screenshot/eternal-ai.png","requestStartTime":181951983.671,"totalBytes":1321436,"wastedBytes":1321436,"wastedPercent":100},{"node":{"type":"node","lhId":"1-46-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":9781,"bottom":10082,"left":16,"right":396,"width":380,"height":301},"snippet":"<img src=\"/suitsIcon/sdk_cli.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/sdk_cli.png","requestStartTime":181951983.546,"totalBytes":262211,"wastedBytes":262211,"wastedPercent":100},{"node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":8488,"bottom":8784,"left":16,"right":396,"width":380,"height":297},"snippet":"<img src=\"/suitsIcon/web_app.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/web_app.png","requestStartTime":181951651.453,"totalBytes":165580,"wastedBytes":165580,"wastedPercent":100},{"node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":11137,"bottom":11391,"left":16,"right":396,"width":380,"height":255},"snippet":"<img src=\"/suitsIcon/encryption.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/encryption.png","requestStartTime":181951983.623,"totalBytes":128255,"wastedBytes":128255,"wastedPercent":100},{"node":{"type":"node","lhId":"1-44-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":7269,"bottom":7481,"left":16,"right":396,"width":380,"height":212},"snippet":"<img src=\"/suitsIcon/data_daos.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/data_daos.png","requestStartTime":181951651.415,"totalBytes":113258,"wastedBytes":113258,"wastedPercent":100},{"node":{"type":"node","lhId":"1-31-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,0,SECTION,3,DIV,1,DIV,1,DIV,0,IMG","selector":"div.Pricing_cardGrid__oFbxl > div.Pricing_card__QJPaj > div.Pricing_iconWrapper__7bvdL > img","boundingRect":{"top":4361,"bottom":4447,"left":163,"right":249,"width":86,"height":86},"snippet":"<img src=\"/icons/navigator.png\" alt=\"Navigator icon\">","nodeLabel":"Navigator icon"},"url":"https://lighthouse.storage/icons/navigator.png","requestStartTime":181951651.175,"totalBytes":6591,"wastedBytes":6591,"wastedPercent":100},{"node":{"type":"node","lhId":"1-30-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,0,SECTION,3,DIV,0,DIV,1,DIV,0,IMG","selector":"div.Pricing_cardGrid__oFbxl > div.Pricing_card__QJPaj > div.Pricing_iconWrapper__7bvdL > img","boundingRect":{"top":3786,"bottom":3872,"left":163,"right":249,"width":86,"height":86},"snippet":"<img src=\"/icons/beacon.png\" alt=\"Beacon icon\">","nodeLabel":"Beacon icon"},"url":"https://lighthouse.storage/icons/beacon.png","requestStartTime":181951651.078,"totalBytes":5070,"wastedBytes":5070,"wastedPercent":100},{"node":{"type":"node","lhId":"1-32-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,0,SECTION,3,DIV,2,DIV,1,DIV,0,IMG","selector":"div.Pricing_cardGrid__oFbxl > div.Pricing_card__QJPaj > div.Pricing_iconWrapper__7bvdL > img","boundingRect":{"top":4937,"bottom":5023,"left":163,"right":249,"width":86,"height":86},"snippet":"<img src=\"/icons/harbor.png\" alt=\"Harbor icon\">","nodeLabel":"Harbor icon"},"url":"https://lighthouse.storage/icons/harbor.png","requestStartTime":181951651.37800002,"totalBytes":2311,"wastedBytes":2311,"wastedPercent":100},{"node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,1,DIV,0,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_highlightWrapper__oqXVr > div.EcosystemGrid_highlightCard__eRY_f > img.EcosystemGrid_highlightImage___PiUJ","boundingRect":{"top":12423,"bottom":12600,"left":49,"right":363,"width":314,"height":177},"snippet":"<img src=\"/client/screenshot/eternal-ai.png\" alt=\"Eternal AI\" class=\"EcosystemGrid_highlightImage___PiUJ\">","nodeLabel":"Eternal AI"},"url":"https://lighthouse.storage/client/screenshot/eternal-ai.png","fromProtocol":true,"isCrossOrigin":false,"totalBytes":1321436,"wastedBytes":1153107.55,"wastedWebpBytes":1109668},{"node":{"type":"node","lhId":"1-46-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":9781,"bottom":10082,"left":16,"right":396,"width":380,"height":301},"snippet":"<img src=\"/suitsIcon/sdk_cli.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/sdk_cli.png","fromProtocol":true,"isCrossOrigin":false,"totalBytes":262211,"wastedBytes":205198.7,"wastedWebpBytes":200459},{"node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":8488,"bottom":8784,"left":16,"right":396,"width":380,"height":297},"snippet":"<img src=\"/suitsIcon/web_app.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/web_app.png","fromProtocol":true,"isCrossOrigin":false,"totalBytes":165580,"wastedBytes":119563.9,"wastedWebpBytes":120216},{"node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":11137,"bottom":11391,"left":16,"right":396,"width":380,"height":255},"snippet":"<img src=\"/suitsIcon/encryption.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/encryption.png","fromProtocol":true,"isCrossOrigin":false,"totalBytes":128255,"wastedBytes":94223.2,"wastedWebpBytes":94913},{"node":{"type":"node","lhId":"1-44-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":7269,"bottom":7481,"left":16,"right":396,"width":380,"height":212},"snippet":"<img src=\"/suitsIcon/data_daos.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/data_daos.png","fromProtocol":true,"isCrossOrigin":false,"totalBytes":113258,"wastedBytes":80879.95,"wastedWebpBytes":82066},{"node":{"type":"node","lhId":"1-48-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,6,DIV,0,DIV,1,SECTION,1,DIV,0,DIV,0,IMG","selector":"section.EcosystemGrid_ecosystemSection__7Koqu > div.EcosystemGrid_highlightWrapper__oqXVr > div.EcosystemGrid_highlightCard__eRY_f > img.EcosystemGrid_highlightImage___PiUJ","boundingRect":{"top":12423,"bottom":12600,"left":49,"right":363,"width":314,"height":177},"snippet":"<img src=\"/client/screenshot/eternal-ai.png\" alt=\"Eternal AI\" class=\"EcosystemGrid_highlightImage___PiUJ\">","nodeLabel":"Eternal AI"},"url":"https://lighthouse.storage/client/screenshot/eternal-ai.png","totalBytes":1321436,"wastedBytes":1213069,"wastedPercent":91.79927801225716},{"node":{"type":"node","lhId":"1-46-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":9781,"bottom":10082,"left":16,"right":396,"width":380,"height":301},"snippet":"<img src=\"/suitsIcon/sdk_cli.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/sdk_cli.png","totalBytes":262211,"wastedBytes":171654,"wastedPercent":65.46402247128975},{"node":{"type":"node","lhId":"1-45-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,3,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":8488,"bottom":8784,"left":16,"right":396,"width":380,"height":297},"snippet":"<img src=\"/suitsIcon/web_app.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/web_app.png","totalBytes":165580,"wastedBytes":115013,"wastedPercent":69.4605084116774},{"node":{"type":"node","lhId":"1-47-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,7,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":11137,"bottom":11391,"left":16,"right":396,"width":380,"height":255},"snippet":"<img src=\"/suitsIcon/encryption.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/encryption.png","totalBytes":128255,"wastedBytes":89581,"wastedPercent":69.84625725503585},{"node":{"type":"node","lhId":"1-44-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,5,DIV,0,DIV,1,DIV,0,DIV,1,DIV,0,IMG","selector":"div.LighthouseSuit_LighthouseSuitContainer__LaAyy > div.LighthouseSuit_header__6LPhJ > div.LighthouseSuit_headerRight__bRizF > img","boundingRect":{"top":7269,"bottom":7481,"left":16,"right":396,"width":380,"height":212},"snippet":"<img src=\"/suitsIcon/data_daos.png\" alt=\"Dashboard\" width=\"100%\" height=\"auto\">","nodeLabel":"Dashboard"},"url":"https://lighthouse.storage/suitsIcon/data_daos.png","totalBytes":113258,"wastedBytes":86244,"wastedPercent":76.14805290227774}]},"displayValue":28,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.88}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"51704a","url":"https://www.lighthouse.storage/blogs","$url":"https://www.lighthouse.storage/blogs","path":"/blogs","definition":{"name":"blogs","path":"/blogs"}},"reportId":"51704a","artifactPath":"","artifactUrl":"/site-audit-reports/lighthouse/reports/blogs","tasksTime":{"inspectHtmlTask":1746542340707,"runLighthouseTask":1746542343436},"seo":{"favicon":"/favicon.ico","title":"Lighthouse Storage | Blogs","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","og":{"image":"https://gateway.lighthouse.storage/ipfs/Qmd7rR9EPKomhmoRUw2WB7FJAeSWAtC8c1nkKgGZL39LpB","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","title":"Lighthouse Storage | Blogs"},"internalLinks":0,"externalLinks":2,"htmlSize":375721},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.68},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.9},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:39:29.419Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"1 layout shift found","metricSavings":{"CLS":0.155},"details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"extra"},"label":"Element"},{"key":"score","valueType":"numeric","subItemsHeading":{"key":"cause","valueType":"text"},"granularity":0.001,"label":"Layout shift score"}],"items":[{"node":{"type":"node","lhId":"page-1-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV","selector":"body > div#__next > div.bodyContainer > div.sectionContainer","boundingRect":{"top":736,"bottom":12655,"left":0,"right":412,"width":412,"height":11919},"snippet":"<div class=\"sectionContainer\" style=\"min-height:auto\">","nodeLabel":"Read our latest blog\n\nNandit Mehra\n\nLighthouse Monthly Update – January 2025\n\nN…"},"score":0.15463520128327593}]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"7,100 ms","metricSavings":{"LCP":4600},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.FeaturedArticle_blogContainer__imageBox__VAWiH > div.ImageBox_imageRatioContainer__1Zigy > span > img.ptr","boundingRect":{"top":137,"bottom":396,"left":16,"right":396,"width":380,"height":259},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthous…\" decoding=\"async\" data-nimg=\"fill\" class=\"ptr\" style=\"object-fit: contain; border-radius: 10px; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fimage1_c…\">","nodeLabel":"icon"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":654.7511,"percent":"9%"},{"phase":"Load Delay","timing":4235.************,"percent":"60%"},{"phase":"Load Time","timing":1097.************,"percent":"15%"},{"phase":"Render Delay","timing":1110.0729769975014,"percent":"16%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.05,"scoreDisplayMode":"numeric","numericValue":7098.038050000001,"numericUnit":"millisecond","displayValue":"7.1 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":0.74,"scoreDisplayMode":"numeric","numericValue":0.15463520128327593,"numericUnit":"unitless","displayValue":"0.155","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0.15463520128327593,"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.82,"scoreDisplayMode":"numeric","numericValue":2057.9111,"numericUnit":"millisecond","displayValue":"2.1 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":6,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":62,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.52,"scoreDisplayMode":"numeric","numericValue":7113.038705000001,"numericUnit":"millisecond","displayValue":"7.1 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181948758416,"data":"reports/blogs/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181949133416,"data":"reports/blogs/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181949508416,"data":"reports/blogs/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181949883416,"data":"reports/blogs/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181950258416,"data":"reports/blogs/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181950633416,"data":"reports/blogs/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181951008416,"data":"reports/blogs/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181951383416,"data":"reports/blogs/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2057.9111,"numericUnit":"millisecond","displayValue":"2.1 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors do not have a sufficient contrast ratio.","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-INPUT","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,3,DIV,1,INPUT","selector":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","boundingRect":{"top":13854,"bottom":13894,"left":16,"right":396,"width":380,"height":40},"snippet":"<input type=\"text\" placeholder=\"<EMAIL>\">","nodeLabel":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 2.56 (foreground color: #a1a1aa, background color: #ffffff, font size: 10.8pt (14.4px), font weight: normal). Expected contrast ratio of 4.5:1"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.color","wcag2aa","wcag143","TTv5","TT13.c","EN-301-549","EN-*******","ACT"]}}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":null,"scoreDisplayMode":"notApplicable"},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/ping","urlProvider":"network","line":0,"column":0}},{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/launcher_settings","urlProvider":"network","line":0,"column":0}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.87}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"777812","url":"https://www.lighthouse.storage/blogs/Decentralized%20Excellence%3A%20Elevating%20Data%20Storage%20with%20Lighthouse","$url":"https://www.lighthouse.storage/blogs/Decentralized%20Excellence%3A%20Elevating%20Data%20Storage%20with%20Lighthouse","path":"/blogs/Decentralized%20Excellence%3A%20Elevating%20Data%20Storage%20with%20Lighthouse","definition":{"name":"blogs-slug","path":"/blogs/Decentralized%20Excellence%3A%20Elevating%20Data%20Storage%20with%20Lighthouse"}},"reportId":"777812","artifactPath":"","artifactUrl":"/site-audit-reports/lighthouse/reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse","tasksTime":{"inspectHtmlTask":1746542341046,"runLighthouseTask":1746542343950},"seo":{"favicon":"/favicon.ico","title":"Decentralized Excellence: Elevating Data Storage with Lighthouse","description":"## Decentralized Excellence: Elevating Data Storage with Lighthouse\n\nIn the ever-evolving landscape ...","og":{"image":"https://cms.lighthouse.storage/uploads/i_Stock_1408906928_931b5c5517.jpg","description":"## Decentralized Excellence: Elevating Data Storage with Lighthouse\n\nIn the ever-evolving landscape ...","title":"Decentralized Excellence: Elevating Data Storage with Lighthouse"},"internalLinks":0,"externalLinks":2,"htmlSize":386662},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.86},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.91},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:39:30.814Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2 layout shifts found","metricSavings":{"CLS":0.106},"details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"extra"},"label":"Element"},{"key":"score","valueType":"numeric","subItemsHeading":{"key":"cause","valueType":"text"},"granularity":0.001,"label":"Layout shift score"}],"items":[{"node":{"type":"node","lhId":"page-2-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV","selector":"div.sectionContainer > div.contentContainer > div.BlogView_BlogView__S0NOY > div.BlogView_BlogView__contentContainer__VwpAI","boundingRect":{"top":396,"bottom":4755,"left":0,"right":412,"width":412,"height":4359},"snippet":"<div class=\"BlogView_BlogView__contentContainer__VwpAI\">","nodeLabel":"Decentralized Excellence: Elevating Data Storage with Lighthouse\n\nDecentralized…"},"score":0.1047674948215712,"subItems":{"type":"subitems","items":[{"extra":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.BlogView_BlogView__BannerImage__r2Y5B > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":150,"bottom":364,"left":16,"right":396,"width":380,"height":214},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthous…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fi_Stock_…\">","nodeLabel":"icon"},"cause":"Media element lacking an explicit size"}]}},{"node":{"type":"node","lhId":"page-1-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV,0,P","selector":"div.contentContainer > div.BlogView_BlogView__S0NOY > div.BlogView_BlogView__contentContainer__VwpAI > p.BlogView_title__v8CBG","boundingRect":{"top":460,"bottom":748,"left":21,"right":391,"width":371,"height":288},"snippet":"<p class=\"BlogView_title__v8CBG\">","nodeLabel":"Decentralized Excellence: Elevating Data Storage with Lighthouse"},"score":0.0013361126780008976,"subItems":{"type":"subitems","items":[{"extra":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.BlogView_BlogView__BannerImage__r2Y5B > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":150,"bottom":364,"left":16,"right":396,"width":380,"height":214},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthous…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fi_Stock_…\">","nodeLabel":"icon"},"cause":"Media element lacking an explicit size"},{"extra":{"type":"url","value":"https://fonts.gstatic.com/s/opensans/v40/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2"},"cause":"Web font loaded"}]}}]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"3,500 ms","metricSavings":{"LCP":1000},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.BlogView_BlogView__BannerImage__r2Y5B > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":150,"bottom":364,"left":16,"right":396,"width":380,"height":214},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthous…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fi_Stock_…\">","nodeLabel":"icon"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":650.6427,"percent":"19%"},{"phase":"Load Delay","timing":2104.************,"percent":"60%"},{"phase":"Load Time","timing":432.4411421823879,"percent":"12%"},{"phase":"Render Delay","timing":312.6665057376572,"percent":"9%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.64,"scoreDisplayMode":"numeric","numericValue":3500.6763,"numericUnit":"millisecond","displayValue":"3.5 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":0.88,"scoreDisplayMode":"numeric","numericValue":0.1061036074995721,"numericUnit":"unitless","displayValue":"0.106","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0.1061036074995721,"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.83,"scoreDisplayMode":"numeric","numericValue":2047.4826000000003,"numericUnit":"millisecond","displayValue":"2.0 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":5,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":60,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.37,"scoreDisplayMode":"numeric","numericValue":8628.805890000001,"numericUnit":"millisecond","displayValue":"8.6 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181950172732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181950547732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181950922732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181951297732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181951672732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181952047732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181952422732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181952797732,"data":"reports/blogs/Decentralizedpercent20Excellencepercent3Apercent20Elevatingpercent20Datapercent20Storagepercent20withpercent20Lighthouse/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":0.99,"scoreDisplayMode":"numeric","numericValue":2047.4826000000003,"numericUnit":"millisecond","displayValue":"2.0 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors do not have a sufficient contrast ratio.","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-INPUT","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,3,DIV,1,INPUT","selector":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","boundingRect":{"top":17889,"bottom":17929,"left":16,"right":396,"width":380,"height":40},"snippet":"<input type=\"text\" placeholder=\"<EMAIL>\">","nodeLabel":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 2.56 (foreground color: #a1a1aa, background color: #ffffff, font size: 10.8pt (14.4px), font weight: normal). Expected contrast ratio of 4.5:1"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.color","wcag2aa","wcag143","TTv5","TT13.c","EN-301-549","EN-*******","ACT"]}}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/launcher_settings","urlProvider":"network","line":0,"column":0}},{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/ping","urlProvider":"network","line":0,"column":0}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-11-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.BlogView_BlogView__BannerImage__r2Y5B > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":150,"bottom":364,"left":16,"right":396,"width":380,"height":214},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthous…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fi_Stock_…\">","nodeLabel":"icon"},"url":"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fcms.lighthouse.storage%2Fuploads%2Fi_Stock_1408906928_931b5c5517.jpg&w=750&q=75","totalBytes":68652,"wastedBytes":14632,"wastedPercent":21.313586097946292}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.91}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"558762","url":"https://www.lighthouse.storage/documentation","$url":"https://www.lighthouse.storage/documentation","path":"/documentation","definition":{"name":"documentation","path":"/documentation"}},"reportId":"558762","artifactPath":"","artifactUrl":"/site-audit-reports/lighthouse/reports/documentation","tasksTime":{"inspectHtmlTask":1746542341566,"runLighthouseTask":1746542343462},"seo":{"favicon":"/favicon.ico","title":"Lighthouse Storage | Documentation","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","og":{"image":"https://gateway.lighthouse.storage/ipfs/Qmd7rR9EPKomhmoRUw2WB7FJAeSWAtC8c1nkKgGZL39LpB","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","title":"Lighthouse Storage | Documentation"},"internalLinks":0,"externalLinks":2,"htmlSize":28836},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.92},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.9},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:39:28.427Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2 layout shifts found","metricSavings":{"CLS":0.112},"details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"extra"},"label":"Element"},{"key":"score","valueType":"numeric","subItemsHeading":{"key":"cause","valueType":"text"},"granularity":0.001,"label":"Layout shift score"}],"items":[{"node":{"type":"node","lhId":"page-1-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV","selector":"body > div#__next > div.bodyContainer > div.sectionContainer","boundingRect":{"top":934,"bottom":2577,"left":0,"right":412,"width":412,"height":1643},"snippet":"<div class=\"sectionContainer\" style=\"min-height:auto\">","nodeLabel":"Not sure where to start?\n\nEverything builders need to start using Lighthouse.\n\n…"},"score":0.09665714531025472,"subItems":{"type":"subitems","items":[{"extra":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.DocContainer_DocContainer__imageBox__8rdQ5 > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":560,"bottom":854,"left":54,"right":358,"width":304,"height":294},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fgateway.light…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; padding-top: 2rem; position: absolute; top: 0px; left: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fgateway.lighthouse.storage%2Fipfs%2FQmSdS3Z…\">","nodeLabel":"icon"},"cause":"Media element lacking an explicit size"}]}},{"node":{"type":"node","lhId":"page-1-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV","selector":"body > div#__next > div.bodyContainer > div.sectionContainer","boundingRect":{"top":934,"bottom":2577,"left":0,"right":412,"width":412,"height":1643},"snippet":"<div class=\"sectionContainer\" style=\"min-height:auto\">","nodeLabel":"Not sure where to start?\n\nEverything builders need to start using Lighthouse.\n\n…"},"score":0.015415975840396619,"subItems":{"type":"subitems","items":[{"extra":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.DocContainer_DocContainer__imageBox__8rdQ5 > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":560,"bottom":854,"left":54,"right":358,"width":304,"height":294},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fgateway.light…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; padding-top: 2rem; position: absolute; top: 0px; left: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fgateway.lighthouse.storage%2Fipfs%2FQmSdS3Z…\">","nodeLabel":"icon"},"cause":"Media element lacking an explicit size"},{"extra":{"type":"url","value":"https://fonts.gstatic.com/s/opensans/v40/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2"},"cause":"Web font loaded"}]}}]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,690 ms","metricSavings":{"LCP":200},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.DocContainer_DocContainer__imageBox__8rdQ5 > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":560,"bottom":854,"left":54,"right":358,"width":304,"height":294},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fgateway.light…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; padding-top: 2rem; position: absolute; top: 0px; left: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fgateway.lighthouse.storage%2Fipfs%2FQmSdS3Z…\">","nodeLabel":"icon"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":658.9665,"percent":"25%"},{"phase":"Load Delay","timing":1363.4888662392514,"percent":"51%"},{"phase":"Load Time","timing":427.0359628082115,"percent":"16%"},{"phase":"Render Delay","timing":236.76337095253666,"percent":"9%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.86,"scoreDisplayMode":"numeric","numericValue":2686.2546999999995,"numericUnit":"millisecond","displayValue":"2.7 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":0.86,"scoreDisplayMode":"numeric","numericValue":0.11207312115065135,"numericUnit":"unitless","displayValue":"0.112","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0.11207312115065135,"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.94,"scoreDisplayMode":"numeric","numericValue":1603.6019999999999,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":5.5,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":61,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.44,"scoreDisplayMode":"numeric","numericValue":7866.107619999999,"numericUnit":"millisecond","displayValue":"7.9 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181947802751,"data":"reports/documentation/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181948177751,"data":"reports/documentation/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181948552751,"data":"reports/documentation/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181948927751,"data":"reports/documentation/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181949302751,"data":"reports/documentation/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181949677751,"data":"reports/documentation/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181950052751,"data":"reports/documentation/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181950427751,"data":"reports/documentation/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1603.6019999999999,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors do not have a sufficient contrast ratio.","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-INPUT","path":"1,HTML,1,BODY,0,DIV,0,DIV,3,DIV,0,DIV,3,DIV,1,INPUT","selector":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","boundingRect":{"top":3775,"bottom":3815,"left":16,"right":396,"width":380,"height":40},"snippet":"<input type=\"text\" placeholder=\"<EMAIL>\">","nodeLabel":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 2.56 (foreground color: #a1a1aa, background color: #ffffff, font size: 10.8pt (14.4px), font weight: normal). Expected contrast ratio of 4.5:1"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.color","wcag2aa","wcag143","TTv5","TT13.c","EN-301-549","EN-*******","ACT"]}}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":null,"scoreDisplayMode":"notApplicable"},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/ping","urlProvider":"network","line":0,"column":0}},{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/launcher_settings","urlProvider":"network","line":0,"column":0}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[{"node":{"type":"node","lhId":"1-11-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,1,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.DocContainer_DocContainer__imageBox__8rdQ5 > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":560,"bottom":854,"left":54,"right":358,"width":304,"height":294},"snippet":"<img alt=\"icon\" src=\"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fgateway.light…\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; padding-top: 2rem; position: absolute; top: 0px; left: 0px;\" sizes=\"100vw\" srcset=\"/_next/image?url=https%3A%2F%2Fgateway.lighthouse.storage%2Fipfs%2FQmSdS3Z…\">","nodeLabel":"icon"},"url":"https://www.lighthouse.storage/_next/image?url=https%3A%2F%2Fgateway.lighthouse.storage%2Fipfs%2FQmSdS3Zxk64vC28LhNy4jJrcYzD4ziqPB69UaR1ufNXib7&w=750&q=75","totalBytes":42342,"wastedBytes":21028,"wastedPercent":49.66179310344827}]},"displayValue":1,"score":0},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.93}},{"tasks":{"runLighthouseTask":"completed","inspectHtmlTask":"completed"},"route":{"id":"da75cd","url":"https://www.lighthouse.storage/faq","$url":"https://www.lighthouse.storage/faq","path":"/faq","definition":{"name":"faq","path":"/faq"}},"reportId":"da75cd","artifactPath":"","artifactUrl":"/site-audit-reports/lighthouse/reports/faq","tasksTime":{"inspectHtmlTask":1746542342188,"runLighthouseTask":1746542343975},"seo":{"favicon":"/favicon.ico","title":"Lighthouse Storage | FAQs","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","og":{"image":"https://gateway.lighthouse.storage/ipfs/Qmd7rR9EPKomhmoRUw2WB7FJAeSWAtC8c1nkKgGZL39LpB","description":"Decentralized storage powered by Filecoin. Secure, scalable, and ideal for individuals, developers, and enterprises.","title":"Lighthouse Storage | FAQs"},"internalLinks":0,"externalLinks":2,"htmlSize":24902},"report":{"categories":[{"key":"performance","id":"performance","title":"Performance","score":0.86},{"key":"accessibility","id":"accessibility","title":"Accessibility","score":0.9},{"key":"best-practices","id":"best-practices","title":"Best Practices","score":0.96},{"key":"seo","id":"seo","title":"SEO","score":0.92}],"fetchTime":"2025-05-06T14:39:29.118Z","audits":{"redirects":{"id":"redirects","title":"Avoid multiple page redirects","description":"Redirects introduce additional delays before the page can be loaded. [Learn how to avoid page redirects](https://developer.chrome.com/docs/lighthouse/performance/redirects/).","score":1,"scoreDisplayMode":"metricSavings","numericValue":0,"numericUnit":"millisecond","displayValue":"","metricSavings":{"LCP":0,"FCP":0},"details":{"type":"opportunity","headings":[],"items":[],"overallSavingsMs":0},"guidanceLevel":2},"layout-shifts":{"id":"layout-shifts","title":"Avoid large layout shifts","description":"These are the largest layout shifts observed on the page. Each table item represents a single layout shift, and shows the element that shifted the most. Below each item are possible root causes that led to the layout shift. Some of these layout shifts may not be included in the CLS metric value due to [windowing](https://web.dev/articles/cls#what_is_cls). [Learn how to improve CLS](https://web.dev/articles/optimize-cls)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2 layout shifts found","metricSavings":{"CLS":0.202},"details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"extra"},"label":"Element"},{"key":"score","valueType":"numeric","subItemsHeading":{"key":"cause","valueType":"text"},"granularity":0.001,"label":"Layout shift score"}],"items":[{"node":{"type":"node","lhId":"page-2-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV","selector":"body > div#__next > div.bodyContainer > div.Footer_Footer__62HR9","boundingRect":{"top":1776,"bottom":2961,"left":0,"right":412,"width":412,"height":1185},"snippet":"<div class=\"Footer_Footer__62HR9\">","nodeLabel":"<EMAIL>\n\nSITEMAP\n\nHome\n\nFAQ's\n\nBlogs\n\nDocumentation\n\nHELP\n\nCont…"},"score":0.19441069258809235,"subItems":{"type":"subitems","items":[{"extra":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.Footer_logo__WsCNs > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":1824,"bottom":1939,"left":16,"right":396,"width":380,"height":115},"snippet":"<img alt=\"icon\" src=\"/logo.svg\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/logo.svg 640w, /logo.svg 750w, /logo.svg 828w, /logo.svg 1080w, /logo.svg…\">","nodeLabel":"icon"},"cause":"Media element lacking an explicit size"}]}},{"node":{"type":"node","lhId":"page-2-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV","selector":"body > div#__next > div.bodyContainer > div.Footer_Footer__62HR9","boundingRect":{"top":1776,"bottom":2961,"left":0,"right":412,"width":412,"height":1185},"snippet":"<div class=\"Footer_Footer__62HR9\">","nodeLabel":"<EMAIL>\n\nSITEMAP\n\nHome\n\nFAQ's\n\nBlogs\n\nDocumentation\n\nHELP\n\nCont…"},"score":0.007672362459355808,"subItems":{"type":"subitems","items":[{"extra":{"type":"url","value":"https://fonts.gstatic.com/s/opensans/v40/memvYaGs126MiZpBA-UvWbX2vVnXBbObj2OVTS-mu0SC55I.woff2"},"cause":"Web font loaded"}]}}]},"guidanceLevel":2},"largest-contentful-paint-element":{"id":"largest-contentful-paint-element","title":"Largest Contentful Paint element","description":"This is the largest contentful element painted within the viewport. [Learn more about the Largest Contentful Paint element](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0,"scoreDisplayMode":"metricSavings","displayValue":"2,680 ms","metricSavings":{"LCP":200},"details":{"type":"list","items":[{"type":"table","headings":[{"key":"node","valueType":"node","label":"Element"}],"items":[{"node":{"type":"node","lhId":"page-0-IMG","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV,0,DIV,0,DIV,0,DIV,0,DIV,0,SPAN,0,IMG","selector":"div.Footer_logo__WsCNs > div.ImageBox_imageContainer__W_6mX > span > img.ImageBox_image__6Xj_d","boundingRect":{"top":1824,"bottom":1939,"left":16,"right":396,"width":380,"height":115},"snippet":"<img alt=\"icon\" src=\"/logo.svg\" decoding=\"async\" data-nimg=\"fill\" class=\"ImageBox_image__6Xj_d\" style=\"object-fit: contain; position: absolute; inset: 0px;\" sizes=\"100vw\" srcset=\"/logo.svg 640w, /logo.svg 750w, /logo.svg 828w, /logo.svg 1080w, /logo.svg…\">","nodeLabel":"icon"}}]},{"type":"table","headings":[{"key":"phase","valueType":"text","label":"Phase"},{"key":"percent","valueType":"text","label":"% of LCP"},{"key":"timing","valueType":"ms","label":"Timing"}],"items":[{"phase":"TTFB","timing":654.75,"percent":"24%"},{"phase":"Load Delay","timing":1428.************,"percent":"53%"},{"phase":"Load Time","timing":387.5905943540738,"percent":"14%"},{"phase":"Render Delay","timing":210.2115159537534,"percent":"8%"}]}]},"guidanceLevel":1},"largest-contentful-paint":{"id":"largest-contentful-paint","title":"Largest Contentful Paint","description":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more about the Largest Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-largest-contentful-paint/)","score":0.86,"scoreDisplayMode":"numeric","numericValue":2681.0950000000003,"numericUnit":"millisecond","displayValue":"2.7 s","scoringOptions":{"p10":2500,"median":4000}},"cumulative-layout-shift":{"id":"cumulative-layout-shift","title":"Cumulative Layout Shift","description":"Cumulative Layout Shift measures the movement of visible elements within the viewport. [Learn more about the Cumulative Layout Shift metric](https://web.dev/articles/cls).","score":0.61,"scoreDisplayMode":"numeric","numericValue":0.20208305504744817,"numericUnit":"unitless","displayValue":"0.202","scoringOptions":{"p10":0.1,"median":0.25},"details":{"type":"debugdata","items":[{"cumulativeLayoutShiftMainFrame":0.20208305504744817,"newEngineResultDiffered":false}]}},"first-contentful-paint":{"id":"first-contentful-paint","title":"First Contentful Paint","description":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","score":0.94,"scoreDisplayMode":"numeric","numericValue":1607.1358,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":1800,"median":3000}},"total-blocking-time":{"id":"total-blocking-time","title":"Total Blocking Time","description":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more about the Total Blocking Time metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-total-blocking-time/).","score":1,"scoreDisplayMode":"numeric","numericValue":6.5,"numericUnit":"millisecond","displayValue":"10 ms","scoringOptions":{"p10":200,"median":600}},"max-potential-fid":{"id":"max-potential-fid","title":"Max Potential First Input Delay","description":"The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more about the Maximum Potential First Input Delay metric](https://developer.chrome.com/docs/lighthouse/performance/lighthouse-max-potential-fid/).","score":1,"scoreDisplayMode":"numeric","numericValue":63,"numericUnit":"millisecond","displayValue":"60 ms"},"interactive":{"id":"interactive","title":"Time to Interactive","description":"Time to Interactive is the amount of time it takes for the page to become fully interactive. [Learn more about the Time to Interactive metric](https://developer.chrome.com/docs/lighthouse/performance/interactive/).","score":0.43,"scoreDisplayMode":"numeric","numericValue":7933.871130000001,"numericUnit":"millisecond","displayValue":"7.9 s"},"screenshot-thumbnails":{"id":"screenshot-thumbnails","title":"Screenshot Thumbnails","description":"This is what the load of your site looked like.","score":1,"scoreDisplayMode":"informative","details":{"type":"filmstrip","scale":3000,"items":[{"timing":375,"timestamp":181948539811,"data":"reports/faq/__screenshot-thumbnails__/0.jpeg"},{"timing":750,"timestamp":181948914811,"data":"reports/faq/__screenshot-thumbnails__/1.jpeg"},{"timing":1125,"timestamp":181949289811,"data":"reports/faq/__screenshot-thumbnails__/2.jpeg"},{"timing":1500,"timestamp":181949664811,"data":"reports/faq/__screenshot-thumbnails__/3.jpeg"},{"timing":1875,"timestamp":181950039811,"data":"reports/faq/__screenshot-thumbnails__/4.jpeg"},{"timing":2250,"timestamp":181950414811,"data":"reports/faq/__screenshot-thumbnails__/5.jpeg"},{"timing":2625,"timestamp":181950789811,"data":"reports/faq/__screenshot-thumbnails__/6.jpeg"},{"timing":3000,"timestamp":181951164811,"data":"reports/faq/__screenshot-thumbnails__/7.jpeg"}]}},"speed-index":{"id":"speed-index","title":"Speed Index","description":"Speed Index shows how quickly the contents of a page are visibly populated. [Learn more about the Speed Index metric](https://developer.chrome.com/docs/lighthouse/performance/speed-index/).","score":1,"scoreDisplayMode":"numeric","numericValue":1607.1358,"numericUnit":"millisecond","displayValue":"1.6 s","scoringOptions":{"p10":3387,"median":5800}},"color-contrast":{"id":"color-contrast","title":"Background and foreground colors do not have a sufficient contrast ratio.","description":"Low-contrast text is difficult or impossible for many users to read. [Learn how to provide sufficient color contrast](https://dequeuniversity.com/rules/axe/4.10/color-contrast).","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"node","valueType":"node","subItemsHeading":{"key":"relatedNode","valueType":"node"},"label":"Failing Elements"}],"items":[{"node":{"type":"node","lhId":"1-0-P","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,2,DIV,1,P","selector":"div.contentContainer > div.FaqContainer_FAQContainer__Wxe6B > div.FaqContainer_FAQContainer__QuestionBox__Ngumt > p.FaqContainer_subTitle__cC3au","boundingRect":{"top":1515,"bottom":1587,"left":72,"right":340,"width":268,"height":72},"snippet":"<p class=\"FaqContainer_subTitle__cC3au\">","nodeLabel":"Didn’t find what you were looking for?\nOur team is happy to help.","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 3.35 (foreground color: #667085, background color: #1f1d23, font size: 12.0pt (16px), font weight: normal). Expected contrast ratio of 4.5:1"},"subItems":{"type":"subitems","items":[{"relatedNode":{"type":"node","lhId":"1-1-DIV","path":"1,HTML,1,BODY,0,DIV,0,DIV,1,DIV,0,DIV,0,DIV,2,DIV","selector":"div.sectionContainer > div.contentContainer > div.FaqContainer_FAQContainer__Wxe6B > div.FaqContainer_FAQContainer__QuestionBox__Ngumt","boundingRect":{"top":1408,"bottom":1696,"left":40,"right":372,"width":332,"height":288},"snippet":"<div class=\"FaqContainer_FAQContainer__QuestionBox__Ngumt aos-init aos-animate\" data-aos=\"fade-up\">","nodeLabel":"Lets Talk !\n\nDidn’t find what you were looking for?\nOur team is happy to help.\n…"}}]}},{"node":{"type":"node","lhId":"1-2-INPUT","path":"1,HTML,1,BODY,0,DIV,0,DIV,2,DIV,0,DIV,3,DIV,1,INPUT","selector":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","boundingRect":{"top":2710,"bottom":2750,"left":16,"right":396,"width":380,"height":40},"snippet":"<input type=\"text\" placeholder=\"<EMAIL>\">","nodeLabel":"div.Footer_Footer__62HR9 > div.Footer_Footer__upperFooter__wic77 > div.Footer_newsLetterBox__NHgSl > input","explanation":"Fix any of the following:\n  Element has insufficient color contrast of 2.56 (foreground color: #a1a1aa, background color: #ffffff, font size: 10.8pt (14.4px), font weight: normal). Expected contrast ratio of 4.5:1"}}],"debugData":{"type":"debugdata","impact":"serious","tags":["cat.color","wcag2aa","wcag143","TTv5","TT13.c","EN-301-549","EN-*******","ACT"]}}},"heading-order":{"id":"heading-order","title":"Heading elements appear in a sequentially-descending order","description":"Properly ordered headings that do not skip levels convey the semantic structure of the page, making it easier to navigate and understand when using assistive technologies. [Learn more about heading order](https://dequeuniversity.com/rules/axe/4.10/heading-order).","score":null,"scoreDisplayMode":"notApplicable"},"label":{"id":"label","title":"Form elements have associated labels","description":"Labels ensure that form controls are announced properly by assistive technologies, like screen readers. [Learn more about form element labels](https://dequeuniversity.com/rules/axe/4.10/label).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-alt":{"id":"image-alt","title":"Image elements have `[alt]` attributes","description":"Informative elements should aim for short, descriptive alternate text. Decorative elements can be ignored with an empty alt attribute. [Learn more about the `alt` attribute](https://dequeuniversity.com/rules/axe/4.10/image-alt).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"link-name":{"id":"link-name","title":"Links have a discernible name","description":"Link text (and alternate text for images, when used as links) that is discernible, unique, and focusable improves the navigation experience for screen reader users. [Learn how to make links accessible](https://dequeuniversity.com/rules/axe/4.10/link-name).","score":null,"scoreDisplayMode":"notApplicable"},"errors-in-console":{"id":"errors-in-console","title":"Browser errors were logged to the console","description":"Errors logged to the console indicate unresolved problems. They can come from network request failures and other browser concerns. [Learn more about this errors in console diagnostic audit](https://developer.chrome.com/docs/lighthouse/best-practices/errors-in-console/)","score":0,"scoreDisplayMode":"binary","details":{"type":"table","headings":[{"key":"sourceLocation","valueType":"source-location","label":"Source"},{"key":"description","valueType":"code","label":"Description"}],"items":[{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/ping","urlProvider":"network","line":0,"column":0}},{"source":"network","description":"Failed to load resource: the server responded with a status of 403 ()","sourceLocation":{"type":"source-location","url":"https://api-iam.intercom.io/messenger/web/launcher_settings","urlProvider":"network","line":0,"column":0}}]}},"inspector-issues":{"id":"inspector-issues","title":"No issues in the `Issues` panel in Chrome Devtools","description":"Issues logged to the `Issues` panel in Chrome Devtools indicate unresolved problems. They can come from network request failures, insufficient security controls, and other browser concerns. Open up the Issues panel in Chrome DevTools for more details on each issue.","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-size-responsive":{"id":"image-size-responsive","title":"Serves images with appropriate resolution","description":"Image natural dimensions should be proportional to the display size and the pixel ratio to maximize image clarity. [Learn how to provide responsive images](https://web.dev/articles/serve-responsive-images).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"image-aspect-ratio":{"id":"image-aspect-ratio","title":"Displays images with correct aspect ratio","description":"Image display dimensions should match natural aspect ratio. [Learn more about image aspect ratio](https://developer.chrome.com/docs/lighthouse/best-practices/image-aspect-ratio/).","score":1,"scoreDisplayMode":"binary","details":{"type":"table","headings":[],"items":[]}},"is-crawlable":{"id":"is-crawlable","title":"Page isn’t blocked from indexing","description":"Search engines are unable to include your pages in search results if they don't have permission to crawl them. [Learn more about crawler directives](https://developer.chrome.com/docs/lighthouse/seo/is-crawlable/).","score":1,"scoreDisplayMode":"binary","warnings":[],"details":{"type":"table","headings":[],"items":[]}}},"computed":{"imageIssues":{"details":{"items":[]},"displayValue":0,"score":1},"ariaIssues":{"details":{"items":[]},"displayValue":0,"score":1}},"score":0.91}}],"scanMeta":{"favicon":"/favicon.ico","monitor":{"status":"completed","timeRunning":45916,"doneTargets":12,"allTargets":12,"donePercStr":"100","errorPerc":"0.00","timeRemaining":0,"pagesPerSecond":"0.26","cpuUsage":"40.0%","memoryUsage":"92.0%","workers":7},"routes":5,"score":0.9},"options":{"client":{"groupRoutesKey":"route.definition.name","columns":{"overview":[{"label":"Screenshot Timeline","key":"report.audits.screenshot-thumbnails","cols":6}],"performance":[{"cols":1,"label":"FCP","tooltip":"First Contentful Paint marks the time at which the first text or image is painted. [Learn more about the First Contentful Paint metric](https://developer.chrome.com/docs/lighthouse/performance/first-contentful-paint/).","key":"report.audits.first-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"LCP","tooltip":"Largest Contentful Paint marks the time at which the largest text or image is painted. [Learn more](https://web.dev/lighthouse-largest-contentful-paint/)","key":"report.audits.largest-contentful-paint","sortKey":"numericValue"},{"cols":2,"label":"CLS","tooltip":"Cumulative Layout Shift measures the movement of visible elements within the viewport.","sortKey":"numericValue","key":"report.audits.cumulative-layout-shift"},{"cols":1,"label":"FID","warning":true,"tooltip":"Warning: This is deprecated in favour if INP which is not yet supported by Unlighthouse. The maximum potential First Input Delay that your users could experience is the duration of the longest task. [Learn more](https://web.dev/lighthouse-max-potential-fid/).","sortKey":"numericValue","key":"report.audits.max-potential-fid"},{"cols":1,"label":"TBT","tooltip":"Sum of all time periods between FCP and Time to Interactive, when task length exceeded 50ms, expressed in milliseconds. [Learn more](https://web.dev/lighthouse-total-blocking-time/).","sortKey":"numericValue","key":"report.audits.total-blocking-time"},{"cols":1,"label":"SI","sortKey":"numericValue","tooltip":"The speed index is a page load performance metric that shows you how quickly the contents of a page are visibly populated. [Learn more](https://web.dev/speed-index/).","key":"report.audits.speed-index"}],"accessibility":[{"cols":3,"label":"Color Contrast","tooltip":"Background and foreground colors do not have a sufficient contrast ratio.","sortKey":"length:details.items","key":"report.audits.color-contrast"},{"cols":1,"label":"Headings","tooltip":"Heading elements appear in a sequentially-descending order","sortKey":"length:details.items","key":"report.audits.heading-order"},{"cols":1,"label":"ARIA","tooltip":"An aggregate of all ARIA audits.","sortKey":"displayValue","sortable":true,"key":"report.computed.ariaIssues"},{"cols":1,"label":"Labels","tooltip":"Form elements have associated labels","sortKey":"length:details.items","key":"report.audits.label"},{"cols":1,"label":"Image Alts","tooltip":"Image elements have [alt] attributes","sortKey":"length:details.items","key":"report.audits.image-alt"},{"cols":1,"label":"Link Names","tooltip":"Links do not have a discernible name","sortKey":"length:details.items","key":"report.audits.link-name"}],"best-practices":[{"cols":2,"label":"Errors","tooltip":"No browser errors logged to the console","sortKey":"length:details.items","key":"report.audits.errors-in-console"},{"cols":2,"label":"Inspector Issues","tooltip":"No issues in the `Issues` panel in Chrome Devtools","sortKey":"length:details.items","key":"report.audits.inspector-issues"},{"cols":2,"label":"Images Responsive","tooltip":"Serves images with appropriate resolution","sortKey":"length:details.items","key":"report.audits.image-size-responsive"},{"cols":2,"label":"Image Aspect Ratio","tooltip":"Displays images with correct aspect ratio","sortKey":"length:details.items","key":"report.audits.image-aspect-ratio"}],"seo":[{"cols":1,"label":"Indexable","tooltip":"Page isn’t blocked from indexing","key":"report.audits.is-crawlable"},{"cols":1,"label":"Internal link","sortable":true,"key":"seo.internalLinks"},{"cols":1,"label":"External link","sortable":true,"key":"seo.externalLinks"},{"cols":2,"label":"Description","key":"seo.description"},{"cols":2,"label":"Share Image","key":"seo.og.image"}]}},"site":"https://lighthouse.storage","lighthouseOptions":{"onlyCategories":["performance","accessibility","best-practices","seo"]},"scanner":{"customSampling":{},"ignoreI18nPages":true,"maxRoutes":200,"skipJavascript":false,"samples":3,"throttle":true,"crawler":true,"dynamicSampling":1,"sitemap":["https://www.lighthouse.storage/sitemap.xml"],"robotsTxt":true,"device":"mobile","exclude":["/cdn-cgi/*"],"_robotsTxtRules":[{"pattern":"/","allow":true}]},"routerPrefix":"/site-audit-reports/lighthouse/"}}