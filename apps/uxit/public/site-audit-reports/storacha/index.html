<!DOCTYPE html>
<html lang="en" class="dark">
<head>
  <base href="/site-audit-reports/storacha/">
  <style>button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}</style>

  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" href="/site-audit-reports/storacha/assets/logo.svg" type="image/svg+xml" media="(prefers-color-scheme:no-preference)">
  <link rel="icon" href="/site-audit-reports/storacha/assets/logo-dark.svg" type="image/svg+xml" media="(prefers-color-scheme:dark)">
  <link rel="icon" href="/site-audit-reports/storacha/assets/logo-light.svg" type="image/svg+xml" media="(prefers-color-scheme:light)">

  <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=DM+Mono:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&family=DM+Sans:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&family=DM+Serif+Display:ital@0;1&display=swap">
  <title>Unlighthouse</title>
  <script data-unlighthouse-inline>window.__unlighthouse_static = true</script>
  <script data-unlighthouse-payload src="/site-audit-reports/storacha/assets/payload.js"></script>
  <style>:root {
  --c-bg: #fff;
  --c-scrollbar: #eee;
  --c-scrollbar-hover: #bbb;
}
html.dark {
  --c-bg: #050505;
  --c-scrollbar: #111;
  --c-scrollbar-hover: #222;
}
* {
  scrollbar-color: var(--c-scrollbar) var(--c-bg);
}
::-webkit-scrollbar {
  width: 6px;
}
::-webkit-scrollbar:horizontal {
  height: 6px;
}
::-webkit-scrollbar-track, ::-webkit-scrollbar-corner {
  background: var(--c-bg);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb {
  background: var(--c-scrollbar);
  border-radius: 10px;
}
::-webkit-scrollbar-thumb:hover {
  background: var(--c-scrollbar-hover);
}</style>
  <script type="module" crossorigin src="/site-audit-reports/storacha/assets/index.5e63f8c4.js"></script>
  <link rel="stylesheet" href="/site-audit-reports/storacha/assets/index.2dd8ef1e.css">
</head>
<body class="dark:bg-[#020617]">
<div id="app"></div>
<script>
(function () {
  const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches
  const setting = localStorage.getItem('color-schema') || 'auto'
  if (setting === 'dark' || (prefersDark && setting !== 'light'))
    document.documentElement.classList.toggle('dark', true)

  // document.title = __unlighthouse_data.options.host.replace(/https?:\/\/(www.)?/, '') + ' | Unlighthouse'
})()
</script>

</body>
</html>
