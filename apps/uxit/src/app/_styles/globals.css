@import 'tailwindcss';
@source "../../../../../node_modules/@filecoin-foundation/ui";

@theme {
  --spacing-readable: 60ch;
}

@utility brand-outline {
  outline: 2px solid var(--color-indigo-500);
  outline-offset: 0px;
  border-color: transparent;
}

@layer components {
  /* BUTTON */
  .button {
    @apply focus:brand-outline transition;
  }

  /* CARD */
  .card {
    @apply rounded-md bg-transparent hover:bg-indigo-50;
  }

  .card-link {
    @apply rounded-md;
  }

  /* SITE LAYOUT */
  .site-layout {
    @apply bg-neutral-100 text-indigo-950;
  }

  /* TEXT LINKS */
  .text-link {
    @apply focus:brand-outline font-normal text-indigo-500 no-underline hover:text-indigo-800 hover:underline;
  }
}
