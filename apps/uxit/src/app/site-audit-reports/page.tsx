import { ArrowRightIcon } from '@phosphor-icons/react/dist/ssr'

import { CardGrid } from '@filecoin-foundation/ui/CardGrid'
import { Heading } from '@filecoin-foundation/ui/Heading'
import { PageLayout } from '@filecoin-foundation/ui/PageLayout'
import { ExternalTextLink } from '@filecoin-foundation/ui/TextLink/ExternalTextLink'

import { PATHS } from '@/constants/paths'

import { createMetadata } from '@/utils/createMetadata'

import { Card } from '@/components/Card'

import { siteMetadataList } from './data/siteMetadataList'

export default function SiteAuditPage() {
  return (
    <PageLayout>
      <header className="max-w-readable space-y-4">
        <Heading tag="h1" variant="4xl">
          Website Lighthouse Audits
        </Heading>
        <p>
          Static Lighthouse reports are available for each site and can be
          viewed directly in the browser.
        </p>
        <p className="max-w-readable">
          These reports are generated periodically using{' '}
          <ExternalTextLink href="https://unlighthouse.dev/integrations/ci">
            Unlighthouse CI
          </ExternalTextLink>{' '}
          and include performance metrics, accessibility scores, best practices
          analysis, and SEO recommendations.
        </p>
      </header>

      <CardGrid as="section" cols="smTwoLgThree">
        {siteMetadataList.map(({ name, id }) => (
          <Card
            key={id}
            as="div"
            title={{ text: name }}
            cta={{
              href: `${PATHS.SITE_AUDIT_REPORTS.path}/${id}/`,
              text: 'View Report',
              icon: {
                component: ArrowRightIcon,
                position: 'trailing',
              },
            }}
          />
        ))}
      </CardGrid>
    </PageLayout>
  )
}

export const metadata = createMetadata({
  title: 'Website Performance & Accessibility Audits',
  description:
    'Explore Lighthouse audit reports across Filecoin ecosystem websites. View detailed performance, accessibility, and SEO insights generated by UXIT using Unlighthouse CI.',
  path: PATHS.SITE_AUDIT_REPORTS.path,
})
