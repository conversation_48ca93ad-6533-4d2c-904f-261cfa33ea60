{"extends": "@filecoin-foundation/typescript-config/base.json", "compilerOptions": {"noEmit": true, "jsx": "preserve", "paths": {"@/*": ["./src/app/*"], "@/actions/*": ["./src/app/_actions/*"], "@/assets/*": ["./public/assets/*"], "@/components/*": ["./src/app/_components/*"], "@/constants/*": ["./src/app/_constants/*"], "@/content/*": ["./src/content/*"], "@/data/*": ["./src/app/_data/*"], "@/hooks/*": ["./src/app/_hooks/*"], "@/schemas/*": ["./src/app/_schemas/*"], "@/services/*": ["./src/app/_services/*"], "@/styles/*": ["./src/app/_styles/*"], "@/support/*": ["./cypress/support/*"], "@/types/*": ["./src/app/_types/*"], "@/utils/*": ["./src/app/_utils/*"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "src/app/_types/**/*.d.ts", "**/*.ts", "**/*.tsx", "cypress/**/*.ts"], "exclude": ["node_modules"]}