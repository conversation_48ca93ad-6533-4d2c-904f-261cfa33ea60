# Active Context: Filecoin Foundation Web Projects

## Current Work Focus

The current focus is on initializing the memory bank for the Filecoin Foundation web projects. This involves creating comprehensive documentation about the project structure, technologies, patterns, and context to facilitate future development and maintenance.

The memory bank is being created with the following files:

1. `projectbrief.md` - Overview of the project, core requirements, and success criteria
2. `productContext.md` - Why the project exists, problems it solves, and user experience goals
3. `systemPatterns.md` - System architecture, key technical decisions, and design patterns
4. `techContext.md` - Technologies used, development setup, and tool usage patterns
5. `activeContext.md` (this file) - Current work focus, recent changes, and next steps
6. `progress.md` - What works, what's left to build, and known issues

## Recent Changes

As this is the initial setup of the memory bank, there are no recent changes to report. The memory bank is being created based on the current state of the project, which includes:

1. A monorepo structure with multiple applications:

   - ff-site: Main Filecoin Foundation website
   - ffdweb-site: Filecoin Foundation for the Decentralized Web website
   - uxit: UX/UI testing and auditing tool

2. Shared packages for common functionality:
   - ui: Shared UI components
   - utils: Common utility functions
   - hooks: Shared React hooks
   - cypress: E2E testing utilities
   - eslint-config: Shared ESLint configuration
   - typescript-config: Shared TypeScript configuration
   - next-config: Shared Next.js configuration

## Next Steps

After initializing the memory bank, potential next steps could include:

1. **Code Review and Refactoring**:

   - Review the codebase for potential improvements
   - Identify areas for refactoring
   - Implement best practices and patterns

2. **Feature Development**:

   - Identify new features to enhance the websites
   - Implement features according to requirements
   - Test and deploy new features

3. **Performance Optimization**:

   - Analyze website performance
   - Identify bottlenecks
   - Implement optimizations

4. **Accessibility Improvements**:

   - Conduct accessibility audits
   - Implement accessibility improvements
   - Test with screen readers and keyboard navigation

5. **Content Management Enhancements**:

   - Improve CMS configuration
   - Add new content types
   - Enhance content editing experience

6. **Testing and Quality Assurance**:
   - Expand test coverage
   - Implement additional testing strategies
   - Automate more testing processes

## Active Decisions and Considerations

As this is the initial setup of the memory bank, there are no active decisions or considerations to report. Future work will involve making decisions about:

1. **Technology Updates**:

   - When to update to newer versions of dependencies
   - How to handle breaking changes
   - Which new technologies to adopt

2. **Feature Prioritization**:

   - Which features to implement first
   - How to balance new features with maintenance
   - How to measure feature impact

3. **Performance vs. Features**:

   - How to balance performance with feature richness
   - When to optimize existing features
   - How to measure performance impact

4. **Accessibility vs. Design**:

   - How to balance accessibility with design requirements
   - When to prioritize accessibility over design
   - How to measure accessibility impact

5. **Content Structure**:
   - How to structure content for optimal user experience
   - When to add new content types
   - How to maintain content consistency

## Important Patterns and Preferences

Based on the project structure and documentation, the following patterns and preferences have been identified:

1. **Code Organization**:

   - Monorepo structure with clear separation of concerns
   - Shared packages for common functionality
   - App-specific code kept within app directories

2. **Component Design**:

   - Functional components with hooks
   - TypeScript for type safety
   - Tailwind CSS for styling
   - Server Components as the default

3. **State Management**:

   - React hooks for local state
   - Context API for global state
   - SWR for remote state
   - nuqs for URL search parameter state

4. **Content Management**:

   - Git-based CMS with Decap CMS
   - Markdown files with YAML frontmatter
   - Content stored in src/content directory
   - CMS configuration in public/admin/config.yml

5. **Testing Strategy**:
   - Cypress for E2E testing
   - Percy for visual regression testing
   - ESLint for code quality
   - Unlighthouse for performance and accessibility

## Learnings and Project Insights

As this is the initial setup of the memory bank, there are no specific learnings or project insights to report. Future work will involve gathering insights about:

1. **Development Efficiency**:

   - How well the monorepo structure works
   - Whether shared packages are effective
   - How to improve development workflows

2. **User Experience**:

   - How users interact with the websites
   - Which features are most used
   - What pain points users experience

3. **Content Management**:

   - How well the CMS meets content editors' needs
   - What challenges content editors face
   - How to improve the content editing experience

4. **Performance**:

   - How well the websites perform
   - What impacts performance
   - How to improve performance

5. **Accessibility**:
   - How accessible the websites are
   - What barriers users with disabilities face
   - How to improve accessibility
