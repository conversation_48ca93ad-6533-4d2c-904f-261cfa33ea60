# Project Brief: Filecoin Foundation Web Monorepo

## Overview

This monorepo contains multiple web projects for the Filecoin Foundation, managed using Turborepo. The primary goal is to maintain and develop websites and tools that provide information, resources, and support for the Filecoin ecosystem and the decentralized web.

## Core Requirements

1. Maintain and enhance the main Filecoin Foundation website (ff-site)
2. Maintain and enhance the Filecoin Foundation for the Decentralized Web website (ffdweb-site)
3. Provide UX/UI testing and auditing tools (uxit) for these websites
4. Share common components, utilities, and configurations across projects
5. Ensure high-quality, accessible, and user-friendly web experiences
6. Support content management through Decap CMS for non-technical team members
7. Maintain proper SEO and structured data for better discoverability
8. Ensure security for sensitive information through encryption where necessary

## Project Scope

### Applications

1. **ff-site**: The main Filecoin Foundation website

   - Provides comprehensive information about Filecoin's initiatives
   - Includes content management through Decap CMS
   - Features encryption for sensitive data

2. **ffdweb-site**: Filecoin Foundation for the Decentralized Web website

   - Focuses on the decentralized web aspects of Filecoin
   - Shares similar architecture with ff-site

3. **uxit**: UX/UI Testing Tool
   - Generates site audit reports using Unlighthouse
   - Helps maintain quality across all web properties

### Shared Packages

1. **ui**: Shared UI components
2. **utils**: Common utility functions
3. **hooks**: Shared React hooks
4. **cypress**: E2E testing utilities
5. **eslint-config**: Shared ESLint configuration
6. **typescript-config**: Shared TypeScript configuration
7. **next-config**: Shared Next.js configuration

## Key Goals

1. Provide clear, accessible information about Filecoin Foundation and its initiatives
2. Support the growth and development of the Filecoin ecosystem
3. Promote the decentralized web and its benefits
4. Maintain high standards for web performance, accessibility, and user experience
5. Enable efficient content management for non-technical team members
6. Ensure security and privacy for sensitive information
7. Facilitate collaboration among developers through shared code and standards

## Success Criteria

1. Websites load quickly and perform well on all devices
2. Content is easily manageable through the CMS
3. Websites meet accessibility standards
4. SEO is optimized for better discoverability
5. Code is maintainable and follows best practices
6. Security measures effectively protect sensitive information
7. Shared components and utilities reduce duplication and increase efficiency
