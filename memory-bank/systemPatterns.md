# System Patterns: Filecoin Foundation Web Projects

## System Architecture

The Filecoin Foundation web projects are structured as a monorepo using Turborepo, with multiple applications and shared packages. This architecture enables code sharing, consistent standards, and efficient development across all projects.

```
filecoin-foundation/
├── apps/
│   ├── ff-site/           # Main Filecoin Foundation website
│   ├── ffdweb-site/       # Filecoin Foundation for the Decentralized Web website
│   └── uxit/              # UX/UI testing and auditing tool
├── packages/
│   ├── ui/                # Shared UI components
│   ├── utils/             # Common utility functions
│   ├── hooks/             # Shared React hooks
│   ├── cypress/           # E2E testing utilities
│   ├── eslint-config/     # Shared ESLint configuration
│   ├── typescript-config/ # Shared TypeScript configuration
│   └── next-config/       # Shared Next.js configuration
└── memory-bank/           # Project documentation
```

### Application Architecture

Each website application (ff-site and ffdweb-site) follows the Next.js App Router architecture, which includes:

1. **App Directory Structure**:

   - `/app`: Contains all routes, layouts, and page components
   - `/app/api`: Contains API routes
   - `/app/_components`: Contains shared components specific to the application
   - `/app/_utils`: Contains utility functions specific to the application
   - `/app/_constants`: Contains constants specific to the application
   - `/app/content`: Contains content files (Markdown) managed by the CMS

2. **Content Management**:

   - Decap CMS (formerly Netlify CMS) for content management
   - Git-based CMS with content stored as Markdown files
   - CMS configuration in `public/admin/config.yml`
   - Content encryption for sensitive information

3. **Rendering Strategy**:
   - Server Components as the default for most UI components
   - Client Components ('use client') only when necessary for interactivity
   - Static Site Generation (SSG) for most pages
   - Server-Side Rendering (SSR) when dynamic data is needed

## Key Technical Decisions

1. **Monorepo Structure**:

   - Decision: Use Turborepo to manage multiple applications and packages
   - Rationale: Enables code sharing, consistent standards, and efficient development
   - Implementation: Workspace configuration in root package.json

2. **Next.js App Router**:

   - Decision: Use Next.js App Router for routing and rendering
   - Rationale: Provides modern, efficient routing with built-in support for layouts, loading states, and error handling
   - Implementation: App directory structure with page.tsx files for routes

3. **Content Management**:

   - Decision: Use Decap CMS for content management
   - Rationale: Git-based CMS allows version control of content and doesn't require a separate database
   - Implementation: CMS configuration in public/admin/config.yml

4. **TypeScript**:

   - Decision: Use TypeScript for all code
   - Rationale: Provides type safety, better developer experience, and reduces runtime errors
   - Implementation: TypeScript configuration in packages/typescript-config

5. **Tailwind CSS**:

   - Decision: Use Tailwind CSS for styling
   - Rationale: Utility-first approach enables rapid UI development and consistent design
   - Implementation: Tailwind configuration in each application

6. **Component Library**:

   - Decision: Create a shared UI component library
   - Rationale: Ensures consistent UI across applications and reduces duplication
   - Implementation: Shared components in packages/ui

7. **Testing Strategy**:

   - Decision: Use Cypress for E2E testing
   - Rationale: Provides comprehensive testing of user flows and interactions
   - Implementation: Cypress configuration and tests in each application

8. **Data Encryption**:
   - Decision: Encrypt sensitive personal information
   - Rationale: Protects user data in an open-source environment
   - Implementation: CryptoJS.AES encryption in ff-site

## Design Patterns

1. **Server Components**:

   - Pattern: Use React Server Components as the default
   - Implementation: Components without 'use client' directive
   - Benefits: Reduced client-side JavaScript, improved performance, and SEO

2. **Shared UI Components**:

   - Pattern: Create reusable UI components in the shared UI package
   - Implementation: Components in packages/ui
   - Benefits: Consistent UI across applications, reduced duplication

3. **Content as Data**:

   - Pattern: Treat content as data with structured frontmatter
   - Implementation: Markdown files with YAML frontmatter
   - Benefits: Structured content that can be queried and displayed flexibly

4. **Centralized Configuration**:

   - Pattern: Centralize configuration in shared packages
   - Implementation: Shared configurations for ESLint, TypeScript, and Next.js
   - Benefits: Consistent standards across all projects

5. **Functional Components**:

   - Pattern: Use functional React components with hooks
   - Implementation: All React components are functional
   - Benefits: Simpler code, better performance, and easier testing

6. **Custom Hooks**:

   - Pattern: Extract reusable logic into custom hooks
   - Implementation: Shared hooks in packages/hooks
   - Benefits: Reusable logic across components and applications

7. **Utility Functions**:
   - Pattern: Create pure utility functions for common operations
   - Implementation: Shared utilities in packages/utils
   - Benefits: Reusable, testable functions for common operations

## Component Relationships

1. **Layout Hierarchy**:

   ```
   RootLayout
   ├── SiteLayout
   │   ├── PageLayout
   │   │   ├── PageHeader
   │   │   ├── Page Content
   │   │   └── Footer
   │   └── Navigation
   └── Metadata
   ```

2. **Content Rendering**:

   ```
   ContentPage
   ├── Metadata
   ├── StructuredData
   ├── PageHeader
   ├── MarkdownPage
   │   ├── Markdown Components
   │   │   ├── Headings
   │   │   ├── Lists
   │   │   ├── Links
   │   │   └── etc.
   │   └── Custom Components
   └── Related Content
   ```

3. **Component Library Structure**:
   ```
   UI Package
   ├── Basic Components
   │   ├── Button
   │   ├── Input
   │   ├── Card
   │   └── etc.
   ├── Composite Components
   │   ├── CardGrid
   │   ├── FilterSidebar
   │   └── etc.
   ├── Layout Components
   │   ├── PageLayout
   │   ├── SiteLayout
   │   └── etc.
   └── Utility Components
       ├── ErrorMessage
       ├── LoadingSpinner
       └── etc.
   ```

## Critical Implementation Paths

1. **Content Management Flow**:

   ```
   Content Editor
   ├── Decap CMS Interface
   ├── Edit Markdown Content
   ├── Commit Changes to Git
   ├── CI/CD Pipeline
   └── Deploy Updated Content
   ```

2. **Page Rendering Flow**:

   ```
   User Request
   ├── Next.js Server
   ├── App Router
   ├── Layout Components
   ├── Page Component
   │   ├── Fetch Content
   │   ├── Process Markdown
   │   └── Render Components
   └── Serve HTML
   ```

3. **Development Workflow**:

   ```
   Developer
   ├── Clone Repository
   ├── Install Dependencies
   ├── Run Development Server
   ├── Make Changes
   │   ├── Update Components
   │   ├── Update Styles
   │   └── Update Content
   ├── Test Changes
   │   ├── Manual Testing
   │   └── Automated Testing
   ├── Commit Changes
   └── Create Pull Request
   ```

4. **Deployment Pipeline**:
   ```
   Pull Request Merged
   ├── CI/CD Pipeline
   │   ├── Install Dependencies
   │   ├── Run Tests
   │   ├── Build Application
   │   └── Deploy to Vercel
   └── Production Deployment
   ```
