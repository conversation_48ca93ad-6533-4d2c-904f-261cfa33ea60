# Tech Context: Filecoin Foundation Web Projects

## Technologies Used

### Core Technologies

1. **Next.js 15**

   - Server-side rendering and static site generation
   - App Router for routing and layouts
   - API routes for backend functionality
   - Image optimization
   - Font optimization

2. **React 19**

   - Server Components for improved performance
   - Client Components for interactive elements
   - Hooks for state management and side effects
   - Context API for global state

3. **TypeScript**

   - Static type checking
   - Enhanced developer experience
   - Improved code quality and maintainability
   - Shared type definitions

4. **Tailwind CSS 4**
   - Utility-first CSS framework
   - Responsive design
   - Component styling
   - Dark mode support

### Content Management

1. **Decap CMS**

   - Git-based content management
   - Markdown content with YAML frontmatter
   - Custom widgets and previews
   - GitHub authentication

2. **Markdown Processing**
   - gray-matter for frontmatter parsing
   - react-markdown for rendering
   - remark-gfm for GitHub Flavored Markdown
   - rehype plugins for HTML processing

### State Management

1. **React Hooks**

   - useState for local state
   - useContext for global state
   - useReducer for complex state logic
   - Custom hooks for reusable logic

2. **SWR**

   - Data fetching and caching
   - Revalidation strategies
   - Error handling
   - Loading states

3. **nuqs**
   - URL search parameter state management
   - Query string synchronization
   - Persistent state across page loads

### UI Components

1. **Headless UI**

   - Accessible UI components
   - Customizable styling
   - Keyboard navigation
   - Screen reader support

2. **Phosphor Icons**
   - Comprehensive icon library
   - Consistent styling
   - Customizable size and color

### Testing and Quality Assurance

1. **Cypress**

   - End-to-end testing
   - Component testing
   - Visual regression testing with Percy
   - Custom commands and utilities

2. **ESLint**

   - Code quality enforcement
   - Consistent coding standards
   - TypeScript integration
   - Custom rules

3. **Prettier**

   - Code formatting
   - Consistent style
   - Integration with ESLint
   - Pre-commit hooks

4. **Unlighthouse**
   - Lighthouse audits
   - Performance monitoring
   - Accessibility checks
   - Best practices validation

### Deployment and Monitoring

1. **Vercel**

   - Continuous deployment
   - Preview deployments
   - Edge functions
   - Analytics

2. **Sentry**

   - Error tracking
   - Performance monitoring
   - User feedback
   - Release tracking

3. **Plausible Analytics**
   - Privacy-focused analytics
   - Page views
   - Referrer tracking
   - Event tracking

### Build Tools

1. **Turborepo**

   - Monorepo management
   - Task orchestration
   - Caching
   - Remote building

2. **npm**
   - Package management
   - Script running
   - Workspace management
   - Dependency resolution

## Development Setup

### Prerequisites

1. **Node.js**

   - Version 18 or later required
   - npm 10.9.2 or later

2. **Git**
   - Version control
   - Collaboration
   - Content management

### Local Development

1. **Installation**

   ```bash
   git clone https://github.com/FilecoinFoundationWeb/filecoin-foundation
   cd filecoin-foundation
   npm install
   ```

2. **Development Server**

   ```bash
   npm run dev
   ```

   - Starts development servers for all applications
   - Hot module replacement
   - Fast refresh
   - Automatic TypeScript checking

3. **Building for Production**

   ```bash
   npm run build
   ```

   - Builds all applications and packages
   - Optimizes for production
   - Generates static assets
   - Creates server bundles

4. **Starting Production Server**
   ```bash
   npm run start
   ```
   - Starts production servers for all applications
   - Serves optimized builds
   - Minimal runtime overhead

### Content Management

1. **Local CMS Setup**

   ```bash
   npx decap-server
   npm run dev
   ```

   - Starts Decap CMS server
   - Enables local content editing
   - Connects to local Git repository

2. **Content Structure**
   - Content stored in `src/content/` directory
   - Markdown files with YAML frontmatter
   - Images and assets in `public/assets/` directory
   - CMS configuration in `public/admin/config.yml`

### Testing

1. **Running Tests**

   ```bash
   npm run test
   ```

   - Runs all tests across all applications and packages
   - Cypress for end-to-end testing
   - Percy for visual regression testing

2. **Linting**
   ```bash
   npm run lint
   ```
   - Runs ESLint across all applications and packages
   - Enforces code quality standards
   - Fixes automatically fixable issues

## Technical Constraints

1. **Performance Requirements**

   - Core Web Vitals targets:
     - Largest Contentful Paint (LCP): < 2.5s
     - First Input Delay (FID): < 100ms
     - Cumulative Layout Shift (CLS): < 0.1
   - Lighthouse score targets:
     - Performance: > 90
     - Accessibility: > 95
     - Best Practices: > 95
     - SEO: > 95

2. **Accessibility Requirements**

   - WCAG 2.1 AA compliance
   - Keyboard navigation
   - Screen reader support
   - Color contrast requirements
   - Focus management

3. **Browser Support**

   - Modern evergreen browsers (Chrome, Firefox, Safari, Edge)
   - No IE11 support required
   - Mobile browser support (iOS Safari, Android Chrome)
   - Responsive design for all screen sizes

4. **Content Management Limitations**

   - Git-based CMS requires Git knowledge for advanced operations
   - Limited rich text editing capabilities
   - No real-time collaboration
   - Content preview limitations

5. **Deployment Constraints**
   - Vercel deployment size limits
   - Build time constraints
   - Environment variable management
   - API rate limits

## Dependencies

### Core Dependencies

1. **Frontend Framework**

   - next: ^15.3.2
   - react: ^19.1.0
   - react-dom: ^19.1.0

2. **Styling**

   - tailwindcss: ^4.1.4
   - @tailwindcss/forms: ^0.5.10
   - @tailwindcss/typography: ^0.5.15
   - clsx: ^2.1.1

3. **TypeScript**

   - typescript: ^5.8.3
   - @types/react: 19.1.3
   - @types/react-dom: 19.1.3
   - @types/node: ^22.15.17

4. **Content Management**

   - gray-matter: ^4.0.3
   - react-markdown: ^10.1.0
   - remark-gfm: ^4.0.0
   - rehype-raw: ^7.0.0
   - rehype-slug: ^6.0.0

5. **UI Components**

   - @headlessui/react: ^2.2.2
   - @phosphor-icons/react: ^2.1.7

6. **State Management**

   - swr: ^2.3.0
   - nuqs: ^2.4.3

7. **Validation**

   - zod: ^3.24.4

8. **Monitoring**
   - @sentry/nextjs: ^9.17.0
   - next-plausible: ^3.12.4

### Development Dependencies

1. **Build Tools**

   - turbo: ^2.5.3
   - postcss: ^8.5.3
   - tsx: ^4.19.4

2. **Linting and Formatting**

   - prettier: ^3.5.3
   - prettier-plugin-tailwindcss: ^0.6.11
   - husky: ^9.1.7

3. **Testing**

   - cypress: ^14.3.3
   - @percy/cypress: ^3.1.6
   - @percy/cli: ^1.30.10

4. **Other Tools**
   - syncpack: ^13.0.4
   - markdownlint-cli2: ^0.18.0

## Tool Usage Patterns

### Next.js Patterns

1. **App Router Usage**

   - Use the app directory for all routes
   - Create page.tsx files for routes
   - Use layout.tsx files for shared layouts
   - Use loading.tsx files for loading states
   - Use error.tsx files for error handling

2. **Data Fetching**

   - Use Server Components for data fetching when possible
   - Use SWR for client-side data fetching
   - Implement proper caching strategies
   - Handle loading and error states

3. **Metadata and SEO**
   - Use the Metadata API for page metadata
   - Implement structured data for rich results
   - Create dynamic OG images
   - Set proper canonical URLs

### React Patterns

1. **Component Structure**

   - Use functional components exclusively
   - Implement proper prop typing
   - Extract reusable logic to custom hooks
   - Keep components focused and small

2. **State Management**

   - Use useState for simple local state
   - Use useReducer for complex state logic
   - Use Context API for global state
   - Use SWR for remote state

3. **Performance Optimization**
   - Minimize client-side JavaScript
   - Use Server Components where possible
   - Implement proper memoization
   - Optimize images and assets

### TypeScript Patterns

1. **Type Definitions**

   - Create shared type definitions
   - Use interfaces for object shapes
   - Use type aliases for unions and intersections
   - Implement proper generics

2. **Type Safety**
   - Enable strict mode
   - Avoid any type
   - Use proper type narrowing
   - Implement proper error handling

### Testing Patterns

1. **E2E Testing**

   - Test critical user flows
   - Verify page content and functionality
   - Test form submissions
   - Test navigation and routing

2. **Visual Testing**
   - Use Percy for visual regression testing
   - Verify component appearance
   - Test responsive layouts
   - Verify dark mode

### Content Management Patterns

1. **Content Structure**

   - Organize content by type
   - Use consistent frontmatter fields
   - Implement proper validation
   - Create reusable content components

2. **CMS Configuration**
   - Define clear content models
   - Create custom widgets when needed
   - Implement proper previews
   - Document content structure
