{"name": "@filecoin-foundation/next-config", "version": "0.0.0", "private": true, "type": "module", "imports": {"#*": "./src/*"}, "exports": {".": "./src/index.js", "./createNextConfig": "./src/createNextConfig.ts", "./createSentryConfig": "./src/createSentryConfig.ts", "./tracing": "./src/tracing.js", "./webpackRules": "./src/webpackRules.js"}, "peerDependencies": {"next": "^15.3.4"}, "devDependencies": {"@filecoin-foundation/eslint-config": "0.0.0", "@filecoin-foundation/typescript-config": "0.0.0"}}